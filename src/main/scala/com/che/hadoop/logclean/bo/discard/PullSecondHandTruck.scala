package com.che.hadoop.logclean.bo.discard

import com.che.hadoop.underlay.dao.{MyKudu, MySparkSession, MySqlBaseDao}
import org.apache.spark.sql.SparkSession

/**
 * 拉取二手车相关业务数据 --转实时
 */
object PullSecondHandTruck {
  def main(args: Array[String]): Unit = {
    val spark = MySparkSession.conn()
    val mysqlBase = new MySqlBaseDao("che_test")
    val myKudu=new MyKudu(spark)
    myKudu.select("t_logdata_thba_streaming")
      .where("day='20231220' and billtype rlike 'tao_'").createOrReplaceTempView("t_biz_data_temp")
    taoDealer(spark,mysqlBase,"20231206")
    taoProductPublish(spark,mysqlBase,"20231206")
    taoClues(spark,mysqlBase,"20231206")
    tao400(spark,mysqlBase,"20231206")
  }

  /**
   * 二手车商家数据
   * @param spark SparkSession
   * @param sqlBase MySql工具类
   * @param day 计算日期
   */
  def taoDealer(spark:SparkSession,sqlBase:MySqlBaseDao,day: String): Unit = {
    try {
      val resultMysqlTable = "db_userportrait_test.t_secondhandtruck"
      val resultDF = spark.sql(
        s"""
           |select c0 as id,c1 as uid,c2 as userisdelete,c3 as usercreatetime,c4 as companyid,c5 as companyname,c6 as companycreatetime,c7 as isvip,c8 as onmain,c9 as onsold,c10 as licence,c11 as storestatus,c12 as licencestatus,c13 as isopen,ROW_NUMBER() OVER(partition by c0 order by `timestamp` desc) as rank from
           |(select json_tuple(data, 'id', 'uid', 'userIsDelete','userCreateTime','companyId', 'companyName', 'companyCreateTime', 'isVIP','onMain', 'onSold', 'licence','licenceStatus', 'storeStatus', 'isOpen'),`timestamp`
           |from t_biz_data_temp where billType='tao_dealer') as t
           |""".stripMargin).filter("rank=1").drop("rank")
      sqlBase.executeMany2(
        s"""
           |REPLACE INTO $resultMysqlTable (id, uid, userisdelete, usercreatetime, companyid, companyname, companycreatetime, isvip, onmain, onsold, licence, storestatus, licencestatus, isopen)
           |VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
           |""".stripMargin, resultDF.collect())
//      sqlBase.execute(s"update t_check set flag=1, message='$day:tao_dealer 运行成功' where name='aliyun_tao_dealer'")
    } catch {
      case e: Exception => {
        e.printStackTrace()
//        try {
//          val exec = new MySqlBaseDao()
//          exec.execute(s"update t_check set flag=2, message='$day:tao_dealer 运行失败' where name='aliyun_tao_dealer'")
//        } catch {
//          case e: Throwable => {
//            e.printStackTrace()
//          }
//        }
      }
    }
  }

  /**
   * 二手车发布数据
   * @param spark SparkSession
   * @param sqlBase MySql工具类
   * @param day 计算日期
   */
  def taoProductPublish(spark: SparkSession, sqlBase: MySqlBaseDao, day: String): Unit = {
    try {
      val resultMysqlTable = "db_userportrait_test.t_secondhandposttruck"
      val resultDF = spark.sql(
        s"""
           |select c0 as id,c1 as uid,c2 as category,c3 as catid,c4 as brandid,c5 as seriesid,c6 as tradprovno,c7 as tradcityno,c8 as tradareano,c9 as onsale,c10 as shelves,c11 as publisher,c12 as lastmodified,c13 as closingtime,c14 as createtime,c15 as shelvesdate,c16 as refreshdate,c17 as isdelete,c18 as `type`,c19 as `number`,c20 as isself,c21 as tonnageid,ROW_NUMBER() OVER(partition by c0 order by `timestamp` desc) as rank from
           |(select json_tuple(data, 'id', 'uid', 'category', 'catId', 'brandId','seriesId', 'tradProvNo', 'tradCityNo', 'tradAreaNo', 'onSale','shelves', 'publisher', 'lastModified', 'closingTime', 'createTime','shelvesDate', 'refreshDate', 'isDelete', 'type', 'number', 'isSelf','tonnageId'),`timestamp`
           |from t_biz_data_temp where billType='tao_product_publish') as t
           |""".stripMargin).filter("rank=1").drop("rank")
      sqlBase.executeMany2(
        s"""
           |REPLACE INTO $resultMysqlTable (id, uid, category, catid, brandid, seriesid, tradprovno, tradcityno, tradareano, onsale, shelves, publisher, lastmodified, closingtime, createtime, shelvesdate, refreshdate, isdelete, `type`, `number`, isself, tonnageid)
           |VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
           |""".stripMargin, resultDF.collect())
//      sqlBase.execute(s"update t_check set flag=1, message='$day:tao_product_publish 运行成功' where name='aliyun_tao_product_publish'")
    } catch {
      case e: Exception => {
        e.printStackTrace()
//        try {
//          val exec = new MySqlBaseDao()
//          exec.execute(s"update t_check set flag=2, message='$day:tao_product_publish 运行失败' where name='aliyun_tao_product_publish'")
//        } catch {
//          case e: Throwable => {
//            e.printStackTrace()
//          }
//        }
      }
    }
  }

  /**
   * 二手车线索数据
   * @param spark SparkSession
   * @param sqlBase MySql工具类
   * @param day 计算日期
   */
  def taoClues(spark: SparkSession, sqlBase: MySqlBaseDao, day: String): Unit = {
    try {
      val resultMysqlTable = "db_userportrait_test.t_tao_clues"
      val resultDF = spark.sql(
        s"""
           |select c0 as id,c1 as vid,c2 as uid,c3 as realname,c4 as mobile,c5 as cateid,c6 as series_id,c7 as brand_id,c8 as tonnage_id,c9 as category,c10 as city_id,c11 as city_name,c12 as `type`,c13 as in_clues,c14 as deleted,c15 as is_sanzhen,c16 as create_time,c17 as truck_id,from_unixtime(CAST (SUBSTR(`timestamp`,1,10) as bigint) ,'yyyyMMdd') as day,ROW_NUMBER() OVER(partition by c0 order by `timestamp` desc) as rank from
           |(select json_tuple(data, 'id', 'vid', 'uid','realname','mobile', 'catId','seriesId', 'brandId', 'tonnageId', 'category', 'cityId', 'cityName', 'type', 'inClues', 'deleted', 'isSanzhen', 'createTime','truckId'),`timestamp`
           |from t_biz_data_temp where billType='tao_clues') as t
           |""".stripMargin).filter("rank=1").drop("rank")
      sqlBase.executeMany2(
        s"""
           |REPLACE INTO $resultMysqlTable (id, vid, uid, realname, mobile, cateid, series_id, brand_id, tonnage_id, category, city_id, city_name, `type`, in_clues, deleted, is_sanzhen, create_time,truck_id, `day`)
           |VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?)
           |""".stripMargin, resultDF.collect())
//      sqlBase.execute(s"update t_check set flag=1, message='$day:tao_clues 运行成功' where name='aliyun_tao_clues'")
    } catch {
      case e: Exception => {
        e.printStackTrace()
//        try {
//          val exec = new MySqlBaseDao()
//          exec.execute(s"update t_check set flag=2, message='$day:tao_clues 运行失败' where name='aliyun_tao_clues'")
//        } catch {
//          case e: Throwable => {
//            e.printStackTrace()
//          }
//        }
      }
    }
  }

  /**
   * 二手车400数据
   * @param spark SparkSession
   * @param sqlBase MySql工具类
   * @param day 计算日期
   */
  def tao400(spark: SparkSession, sqlBase: MySqlBaseDao, day: String): Unit = {
    try {
      val resultMysqlTable = "db_userportrait_test.t_tao_400"
      val resultDF = spark.sql(
        s"""
           |select c0 as id,c1 as tel,c2 as `400`,c3 as is_vnumber,c4 as is_sanzhen,c5 as create_time,from_unixtime(CAST (SUBSTR(`timestamp`,1,10) as bigint) ,'yyyyMMdd') as day,ROW_NUMBER() OVER(partition by c0 order by `timestamp` desc) as rank from
           |(select json_tuple(data, 'id', 'tel', '400', 'isVNumber', 'isSanzhen', 'createTime'),`timestamp`
           |from t_biz_data_temp where billType='tao_400') as t
           |""".stripMargin).filter("rank=1").drop("rank")
      sqlBase.executeMany2(
        s"""
           |REPLACE INTO $resultMysqlTable (id, tel, `400`, is_vnumber, is_sanzhen, create_time, `day`)
           |VALUES (?,?,?,?,?,?,?)
           |""".stripMargin, resultDF.collect())
//      sqlBase.execute(s"update t_check set flag=1, message='$day:tao_400 运行成功' where name='aliyun_tao_400'")
    } catch {
      case e: Exception => {
        e.printStackTrace()
//        try {
//          val exec = new MySqlBaseDao()
//          exec.execute(s"update t_check set flag=2, message='$day:tao_400 运行失败' where name='aliyun_tao_400'")
//        } catch {
//          case e: Throwable => {
//            e.printStackTrace()
//          }
//        }
      }
    }
  }

}
