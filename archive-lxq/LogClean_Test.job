#LogClean_Test
type=command
command=spark-submit --name LogClean_Test --master yarn --deploy-mode cluster --driver-memory 5G --num-executors 2 --executor-cores 6 --executor-memory 5G --driver-class-path 360che.hadoop.logclean-1.0.jar:360che.hadoop.underlay-2.0.jar --conf spark.executor.extraClassPath=360che.hadoop.logclean-1.0.jar:360che.hadoop.underlay-2.0.jar --jars 360che.hadoop.underlay-2.0.jar --class com.che.hadoop.logclean.bo.PullBusinessData 360che.hadoop.logclean-1.0.jar