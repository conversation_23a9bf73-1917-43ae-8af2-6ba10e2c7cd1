package com.che.hadoop.logclean.bo

import java.util.UUID

import com.che.hadoop.underlay.dao.{MySparkSession, MySqlBaseDao}
import com.che.hadoop.logclean.utils.Common
import org.apache.spark.sql.{DataFrame, Row, SparkSession}
import org.apache.spark.sql.types.{DoubleType, StringType, StructField, StructType}

object LogCleanSession {

  def main(args: Array[String]): Unit = {
    val spark=MySparkSession.conn()
    Common.getBetweenDates("20220301","20220412").foreach(day=>{
      session(spark,day)
    })
    spark.stop()
  }

  /**
   * 切割session 计算停留时长  清洗方法
   * @param day
   */
  def session(spark:SparkSession,day:String)= {
    val sqlBase=new MySqlBaseDao()
    try {
      //查当天的数据，存放成临时表
      spark.sql(
        s"""
           |select cid,
           |		screen_name ,
           |		`time`,
           |    title,
           |		 os,
           |		 day,
           |    event,
           |    dm_id,
           |    manufacturer,
           |    model,
           |    app_version,
           |    cl,
           |  ROW_NUMBER() over(partition by cid order by `time`) as num
           |	from t_ods_appsc_log
           |	where event in ("AppViewScreen","AppEnd")
           |		and `day`=$day
           |		and cid != ""
           |   and from_unixtime(cast(substring(time,1,10) as bigint),"yyyyMMdd")=$day
           |""".stripMargin).createOrReplaceTempView("t_log_session")
      spark.catalog.cacheTable("t_log_session")

      // 查询数据
      val arrayList: DataFrame = spark.sql(
        s"""
           |select
           |	t1.cid as cid ,
           |	t1.screen_name as screen_name,
           |	t1.`time` as `time`,
           |	t1.day as day,
           |	t1.num as num,
           |  t1.event as event,
           |  t1.title as title,
           |	t1.os as os,
           |  t1.dm_id as dm_id,
           |  t1.manufacturer as manufacturer ,
           |  t1.model as model,
           |  t1.app_version as app_version,
           |  t1.cl as cl,
           |	round(((t2.`time`-t1.`time`)/1000.0 ),2) as stay_time
           |from t_log_session t1 left join t_log_session t2 on t1.cid=t2.cid and t1.num = t2.num-1
           |order by num
           |""".stripMargin)
      // 循环获取sessionID
      var sessionId=""
      var lastStayTime=0.0
      var lastEvent=""
      val rowRDD= arrayList.rdd.groupBy(row => {
        row.getAs[String]("cid")
      })
        .map(item => {
          val rows=item._2.toArray
            .sortBy(lines => lines.getAs[Int]("num"))
            .map(line => {
              val cid = line.getAs[String]("cid")
              val event = line.getAs[String]("event")
              val screen_name = line.getAs[String]("screen_name")
              val time = line.getAs[String]("time")
              val day = line.getAs[String]("day")
              val stay_time = line.getAs[Double]("stay_time")
              val title = line.getAs[String]("title")
              val os = line.getAs[String]("os")
              val dmid= line.getAs[String]("dm_id")
              val manufacturer = line.getAs[String]("manufacturer")
              val model = line.getAs[String]("model")
              val app_version = line.getAs[String]("app_version")
              val cl = line.getAs[String]("cl")
              if (line.getAs("num") == 1 || lastStayTime >= 60 || event =="AppEnd") {
                sessionId = UUID.randomUUID().toString.replace("-", "")
              }
              lastStayTime=stay_time
              lastEvent=event
              (cid, screen_name, time, title, os, stay_time, sessionId, dmid,manufacturer,model,app_version,cl,day)
            }
            )
          (item._1,rows)
        }
        ).flatMap(f=>{f._2.map({ff=>{Row(ff._1,ff._2,ff._3,ff._4,ff._5,ff._6,ff._7,ff._8,ff._9,ff._10,ff._11,ff._12,ff._13)}})})

      //创建schem信息
      val structType = StructType(Array(
        StructField("cid", StringType, true),
        StructField("screen_name", StringType, true),
        StructField("time", StringType, true),
        StructField("title",StringType,true),
        StructField("os",StringType,true),
        StructField("stay_time", DoubleType, true),
        StructField("sessionId", StringType, true),
        StructField("dm_id", StringType, true),
        StructField("manufacturer", StringType, true),
        StructField("model", StringType, true),
        StructField("app_version", StringType, true),
        StructField("cl", StringType, true),
        StructField("day", StringType, true)
      ))
      //转化成DF
      val resultDF = spark.sqlContext.createDataFrame(rowRDD, structType)
      //删除当天数据
      spark.sql(s" alter table t_ods_session_log drop if exists partition(day='$day') ")
      //写进hive中
      resultDF.repartition(1).write.mode("append").partitionBy("day").format("Hive").saveAsTable("t_ods_session_log")
      sqlBase.executeInsertOrUpdate(s"update t_check set flag=1, message='$day:运行成功' where name='aliyun_t_ods_session_log'",null)
    } catch {
      case e: Exception => {
        e.printStackTrace()
        try{
          sqlBase.executeInsertOrUpdate(s"update t_check set flag=2, message='$day:aliyun_t_ods_session_log 运行失败' where name='aliyun_t_ods_session_log'",null)
        }catch {
          case e:Throwable=>{
            e.printStackTrace()
          }
        }
      }
    }
  }

}
