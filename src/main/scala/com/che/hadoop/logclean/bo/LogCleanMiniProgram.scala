package com.che.hadoop.logclean.bo

import java.util.ResourceBundle

import com.che.hadoop.logclean.utils.Common
import com.che.hadoop.underlay.dao.{MyKudu, MySparkSession, MySqlBaseDao}
import org.apache.kudu.spark.kudu.KuduContext
import org.apache.spark.sql.SparkSession

/**
 * 清洗小程序日志
 * gaojie
 */
object LogCleanMiniProgram {
  def main(args: Array[String]): Unit = {
    //    val spark= MySparkSession.conn()
    //    val sqlBase=new MySqlBaseDao("bigdata_test2")
    //    val resultDF=spark.sql(
    //      """
    //        |select * from t_dwd_dmid where day='20220809'
    //        |""".stripMargin)
    //    sqlBase.saveMysql(resultDF,"t_dwd_dmid_20220809")
//    cleanMiniProgramLog("20221030")

  }

  /**
   * 小程序日志清洗
   * 已经完全上报神策，清洗任务省略
   * @param day
   */
  def cleanMiniProgramLog(spark:SparkSession,day:String,dbEnv:String="default."): Unit = {
    val sqlBase = new MySqlBaseDao()
    var mysqlDB="db_hitsstatistic."
    val logTable = s"${dbEnv}t_ods_miniprogram_log"
    var kuduTable="t_logdata_mpsc_streaming"
    if (dbEnv != "default.") {
      kuduTable = "t_logdata_mpsc_streaming_test"
      mysqlDB = "db_hitsstatistic_test."
    }
    try {
//      val dataStruct = StructType(Array(
//        StructField("openid", StringType, true), StructField("anonymous_id", StringType, true),
//        StructField("distinct_id", StringType, true), StructField("uid", StringType, true),
//        StructField("event", StringType, true), StructField("screen", StringType, true),
//        StructField("latest_scene", StringType, true), StructField("url_path", StringType, true),
//        StructField("title", StringType, true), StructField("url_query", StringType, true),
//        StructField("referrer", StringType, true), StructField("element_id", StringType, true),
//        StructField("element_content", StringType, true), StructField("element_name", StringType, true),
//        StructField("type_id", StringType, true), StructField("label", StringType, true),
//        StructField("action", StringType, true), StructField("element_type", StringType, true),
//        StructField("manufacturer", StringType, true), StructField("os", StringType, true),
//        StructField("model", StringType, true), StructField("os_version", StringType, true),
//        StructField("app_id", StringType, true),
//        StructField("res", StringType, true), StructField("ads", StringType, true),
//        StructField("latitude", StringType, true), StructField("longitude", StringType, true),
//        StructField("network_type", StringType, true), StructField("time", StringType, true),
//        StructField("event_duration", StringType, true), StructField("share_depth", StringType, true),
//        StructField("share_distinct_id", StringType, true), StructField("share_url_path", StringType, true),
//        StructField("share_method", StringType, true), StructField("unionid", StringType, true),
//        StructField("province", StringType, true),
//        StructField("city", StringType, true),
//        StructField("eventid", StringType, true),
//        StructField("referrer_title", StringType, true),
//        StructField("day", StringType, true)
//      ))

      //查询kudu  数据
      val myKudu=new MyKudu(spark)
      myKudu.select(kuduTable).createOrReplaceTempView("t_logdata_mpsc_streaming_tmp")
      val mpsckuduDF = spark.sql(
        s"""
           |select distinct `openid`,`anonymous_id`,`distinct_id`,`uid`,`event`,`screen`,`latest_scene`,`url_path`,`title`,`url_query`,`referrer`,`element_id`,`element_content`,`element_name`,`type_id`,`label`,`action`,`element_type`,`manufacturer`,`os`,`model`,`os_version`,`app_id`,res,`ads`,`latitude`,`longitude`,`network_type`,`time`,`event_duration`,`share_depth`,`share_distinct_id`,`share_url_path`,`share_method`,unionid,province,city,eventid ,referrer_title,`day`,country,recv_time
           |from t_logdata_mpsc_streaming_tmp where day='$day'
           |""".stripMargin)
      spark.sql(s"alter table $logTable drop if exists partition(day='$day')")
      println(s"alter table $logTable drop if exists partition(day='$day')")
//      val dirPath = "oss://360che-bigdata.cn-beijing.oss-dls.aliyuncs.com/pv-nginx-appsc/" + day
//      val LogRdd = spark.read.json(dirPath).filter(t => t.getAs[String]("status") == "200" &&
//        t.getAs[String]("args") != "-"
//        && t.getAs[String]("http_host") == "thappsc.360che.com") //过滤业务日志，参数为"-"
//      val logData = LogRdd.rdd.map(line => {
//        var result = Row()
//        val args = line.getAs[String]("args")
//        val decoder = Base64.getDecoder
//        try {
//          val data = new String(decoder.decode(UrlDecodeUtil.urlDecode(Common.getResByName(args, "data"))))
//          val argObj = JSON.parseObject(data)
//          val properties = argObj.getJSONObject("properties")
//          val time = argObj.getOrDefault("time", properties.getString("$time")).toString
//          val event = argObj.getString("event").replace("$", "")
//          val uid = argObj.getOrDefault("login_id", "").toString
//          val distinctId = argObj.getString("distinct_id")
//          val anonymousId = argObj.getString("anonymous_id")
//          val openId = properties.getOrDefault("$open_id", "").toString
//          val title = properties.getOrDefault("$title", "").toString
//          val screen = properties.getOrDefault("$screen", "").toString
//          val latestScene = properties.getOrDefault("$latest_scene", "").toString
//          val urlPath = properties.getOrDefault("$url_path", "").toString
//          val urlQuery = properties.getOrDefault("$url_query", "").toString
//          val referrer = properties.getOrDefault("$referrer", "").toString
//          val elementId = properties.getOrDefault("$element_id", "").toString
//          val elementName = properties.getOrDefault("$element_name", "").toString
//          val elementContent = properties.getOrDefault("$element_content", "").toString
//          val typeId = properties.getOrDefault("$type_id", "").toString
//          val label = properties.getOrDefault("$label", "").toString
//          val action = properties.getOrDefault("$action", "").toString
//          val elementType = properties.getOrDefault("$element_type", "").toString
//          val manufacturer = properties.getString("$manufacturer")
//          val os = properties.getString("$os")
//          val model = properties.getString("$model")
//          val osVersion = properties.getString("$os_version")
//          val appId = properties.getString("$app_id")
//          val screenWidth = properties.getString("$screen_width")
//          val screenHeight = properties.getString("$screen_height")
//          val res = s"$screenWidth*$screenHeight"
//          val ads = properties.getOrDefault("$ads", "").toString
//          var latitude = ""
//          if (properties.containsKey("$latitude")) {
//            latitude = (properties.getDouble("$latitude") / 1000000).toString
//          }
//          var longitude = ""
//          if (properties.containsKey("$longitude")) {
//            longitude = (properties.getDouble("$longitude") / 1000000).toString
//          }
//          val networkType = properties.getString("$network_type")
//          val eventDuration = properties.getOrDefault("event_duration", "").toString
//          val shareDepth = properties.getOrDefault("$share_depth", "").toString
//          val shareDistinctId = properties.getOrDefault("$share_distinct_id", "").toString
//          val shareUrlPath = properties.getOrDefault("$share_url_path", "").toString
//          val shareMethod = properties.getOrDefault("$share_method", "").toString
//          val unionid = properties.getOrDefault("unionid", "").toString
//          val province = properties.getOrDefault("$province", "").toString
//          val city = properties.getOrDefault("$city", "").toString
//          val eventid = properties.getOrDefault("eventid", "").toString
//          val referrer_title = properties.getOrDefault("$referrer_title", "").toString
//          result = Row(openId, anonymousId, distinctId, uid, event, screen, latestScene,
//            urlPath, title, urlQuery, referrer, elementId, elementContent, elementName, typeId, label, action, elementType,
//            manufacturer, os, model, osVersion, appId, res, ads, latitude, longitude, networkType, time,
//            eventDuration, shareDepth, shareDistinctId, shareUrlPath, shareMethod, unionid, province, city,eventid ,referrer_title, day)
//        } catch {
//          case ex: ClassCastException => {
//            ex.printStackTrace()
//
//          }
//          case ex: NullPointerException => {
//            ex.printStackTrace()
//          }
//          case ex: Exception => {
//            println(args)
//            ex.printStackTrace()
//          }
//        }
//        result
//      }).filter(f => f.length != 0)
//      println(logData.count())
//      println(mpsckuduDF.count())
//      spark.createDataFrame(logData, dataStruct).union(mpsckuduDF).repartition(2).write.mode("append").format("Hive")
      mpsckuduDF.repartition(2).write.mode("append").format("Hive")
        .partitionBy("day").saveAsTable(logTable)
      sqlBase.executeInsertOrUpdate(s"update ${mysqlDB}t_check set flag=1, message='$day:运行成功' where name='aliyun_t_ods_miniprogram_log'", null)
    } catch {
      case e: Exception => {
        e.printStackTrace()
        try {
          sqlBase.executeInsertOrUpdate(s"update ${mysqlDB}t_check set flag=2, message='$day:aliyun_t_ods_miniprogram_log 运行失败' where name='aliyun_t_ods_miniprogram_log'", null)
        } catch {
          case e: Throwable => {
            e.printStackTrace()
          }
        }
      }
    }
  }

  /**
   * 删除
   * @param day
   */
  def delKuduLog(day:String): Unit ={
    val spark = MySparkSession.conn()
    val mpLogKuduTable = "t_logdata_mp_streaming"
    val kuduMasters = ResourceBundle.getBundle("connection").getString("kudu.master")
    spark.conf.set("spark.rpc.askTimeout", "600s")
    // 删除两天前的实时数据
    val before2Day = Common.getSpecifyDate(day, 2)
    val myKudu=new MyKudu(spark)
    myKudu.select(mpLogKuduTable).createOrReplaceTempView("t_log_tmp")
    val mpOldData = spark.sql(s"select id from t_log_tmp where day<='$before2Day'")
    val kuduContext = new KuduContext(kuduMasters, spark.sparkContext)
    kuduContext.deleteRows(mpOldData, mpLogKuduTable)

  }

}
