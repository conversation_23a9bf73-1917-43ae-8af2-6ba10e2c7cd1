package com.che.hadoop.logclean.bo

import java.net.URLDecoder
import java.text.SimpleDateFormat
import java.util.{Date, ResourceBundle}
import com.che.hadoop.underlay.dao.{MyKudu, MySparkSession, MySqlBaseDao}
import com.che.hadoop.underlay.tool.common.HDFSFileRenamer
import com.che.hadoop.logclean.utils.{Common, ConverPercent}
import com.che.hadoop.underlay.tool.date.DateTranslate
import org.apache.spark.rdd
import org.apache.spark.sql.types.{IntegerType, StringType, StructField, StructType}
import org.apache.spark.sql.{DataFrame, Row, SaveMode, SparkSession}


/**
 * Created by zkpk on 1/4/19.
 * web日志清洗
 */
object LogCleanWeb {

  def main(args: Array[String]): Unit = {
    println(DateTranslate.getNowTime("yyyy-MM-dd'T'HH:mm:ssX"))
    val spark = MySparkSession.conn()
    saveLogCleanToHive(spark,"20241026")
  }


  val structTypeWeb = StructType(Array(
    StructField("res", StringType, true),
    StructField("k", StringType, true),
    StructField("vtc", StringType, true),
    StructField("i", StringType, true),
    StructField("vtf", StringType, true),
    StructField("vtl", StringType, true),
    StructField("uid", StringType, true),
    StructField("vid", StringType, true),
    StructField("sid", StringType, true),
    StructField("ip", StringType, true),
    StructField("ua", StringType, true),
    StructField("daytime", StringType, true),
    StructField("eventid", StringType, true),
    StructField("action", StringType, true),
    StructField("lable", StringType, true),
    StructField("code", StringType, true),
    StructField("ga_vid", StringType, true),
    StructField("dealers_uid", StringType, true),
    StructField("hours", IntegerType, true),
    StructField("day", StringType, true)
  ))

  /*清洗数据保存到hive */
  def saveLogCleanToHive(ss:SparkSession,dayValue:String,dbEnv:String="default."):Unit=

  {
    val sqlBase=new MySqlBaseDao()
    val mysqlDB="db_hitsstatistic."
    try{
      val tablename=s"${dbEnv}t_logdata"
      //重命名tmp文件（flume采集异常情况下会有tmp文件）
      val URI=ResourceBundle.getBundle("connection_underlay").getString("oss-hdfs.uri")
      val path=s"/pv-nginx-new/$dayValue"
      val today=DateTranslate.getDate(0)
      //当天文件不自动重命名，因为正在采集过程中会有tmp文件
      if(today == dayValue){
        println("当天采集数据不能重命名tmp文件")
      }else{
        HDFSFileRenamer.renameTmpFile(URI,path)
      }
      val dirPath = s"$URI$path/*/*.gz"
      val mySqlJson=getTypeByMsql(sqlBase,ss).collect()
      /*得到已过滤的数据*/
      sqlBase.getCacheTable("t_filterIP",s"(select f_ip as ip from t_filter_Ip where date_format(f_updatetime,'%Y%m%d')=$dayValue)t",ss)
      ss.conf.set("spark.sql.broadcastTimeout","1200")
      /*得到数据*/
      val logRdd = ss.read.json(dirPath).filter(t=>t.getAs[String]("status")=="200"
        && t.getAs[String]("args").split("&").length>=8  && t.getAs[String]("args").split("&").length<=17)
      val rowRDD=getWebRdd(logRdd,mySqlJson,dayValue)
      ss.sqlContext.createDataFrame(rowRDD,structTypeWeb).filter(t=>t.getAs[String]("day")==dayValue).createOrReplaceTempView("logclean")
      /*保存数据*/
      //过滤来源为空，且访问人数大于200ip的数据,排除ua里面包含spider的数据
      val saveDF=ss.sql(
        s"""select `res`,`k`,`vtc`,`i`,`vtf`,`vtl`,`uid`,`vid`,`sid`,`ip`,`ua`,`daytime`,`eventid`,`action`,if(eventid ='ck_ap' and size(split(lable,'\\\\|'))=5, concat_ws('\\|',base64(split(lable,'\\\\|')[0]),base64(split(lable,'\\\\|')[1]),split(lable,'\\\\|')[2],split(lable,'\\\\|')[3],split(lable,'\\\\|')[4]) ,lable) as `lable`,`code`,`ga_vid`,`dealers_uid`,`hours`,`day`
           |from logclean
           |where
           |ip not in (select ip from (select ip,count(distinct vid) as t from logclean where i='' group by ip) s where t>=200)
           |and ip not in (select ip from t_filterIP)
           |and ip not rlike '^40'
           |and lower(ua) not rlike 'spider'""".stripMargin)
      //删除旧数据
      ss.sql(s"ALTER TABLE $tablename DROP IF EXISTS PARTITION(day=$dayValue)")
      saveDF.distinct().repartition(1).write.mode("append").format("Hive").partitionBy("day").saveAsTable(tablename)
      /*过滤ip访问大于1000的 */
      ss.sql(s"select * from $tablename where day="+dayValue).createOrReplaceTempView("t_filter_data")
      val savemysqlDF=ss.sql("select k as f_url,ip as F_IP,t as f_count,f_updatetime from (select ip,count(k) as t,k,i ,from_unixtime(daytime/1000,'yyyy-MM-dd') as f_updatetime from t_filter_data " +
        "where i='' group by ip,k,i,from_unixtime(daytime/1000,'yyyy-MM-dd')) s where t>=2000") //来源为空，且访问数量大于2000的数据，保存到mysql
      sqlBase.saveMysql(savemysqlDF,"t_filter_Ip")
      sqlBase.executeInsertOrUpdate(s"update ${mysqlDB}t_check set flag=1, message='$dayValue:运行成功' where name='aliyun_t_logdata'",null)
    }catch {
      case e: Exception => {
        e.printStackTrace()
        try{
          sqlBase.executeInsertOrUpdate(s"update ${mysqlDB}t_check set flag=2, message='$dayValue:运行失败' where name='aliyun_t_logdata'",null)
        }catch {
          case e:Throwable=>{
            e.printStackTrace()
          }
        }
      }
    }

  }


  //返回参数值
  def getPValueByName(args: String,value: String): String = {
    var str:String=""
    val a1 =args
    val p1 = ("""(\?|\&)"""+value+"""=([^\&]+)""").r
    val arg=(p1 findAllIn a1 toList)
    if(arg.length>0&&arg(0).split("=").length>1)
    {
      str= arg(0).split("=")(1)
    }
    return str
  }

  //返回res参数值
  def getPValueByName_res(args: String,value: String): String = {
    var str:String=""
    val a1 =args
    val p1 = ("""(\\?|\\&)"""+value+"""=([^\\&]+)""").r
    val arg=(p1 findAllIn a1 toList)
    if(arg.length>0&&arg(0).split("=").length>1)
    {
      str= arg(0).split("=")(1)
    }
    return str
  }

  /*正则判断是否utf8转码*/
  def urlDecode(code:String ) :String={
    if (code.matches("^(?:[\\x00-\\x7f]|[\\xe0-\\xef][\\x80-\\xbf]{2})+$"))
      return URLDecoder.decode(code, "utf-8");
    else
      return code;
  }

  /*app获取vid*/
  def getAPPVid(vid:String,ua:String):String=
  {
    var result=vid
    if(ua.contains("360CHE")&&(ua.contains("iPhone")||ua.contains("Android")||ua.contains("iPad")))
    {

      if(ua.contains("360CHESPEED"))
      {
        if (ua.contains("iPhone"))
        {
          result=getAppVidValue(vid, ua, "iPhone", "DID", 33)
        } else if (ua.contains("iPad")) {
          result = getAppVidValue(vid, ua, "iPad", "DID",33)
        }
        else if (ua.contains("Android"))
        {
          result=getAppVidValue(vid, ua, "Android", "DID", 37)
        }
      }
      else
      {
        if (ua.contains("iPhone"))
        {
          result=getAppVidValue(vid, ua, "iPhone", "DID", 33)
        }else if (ua.contains("iPad")) {
          result = getAppVidValue(vid, ua, "iPad", "DID",33)
        }
        else if (ua.contains("Android"))
        {
          result=getAppVidValue(vid, ua, "Android", "APPCID", 33)
        }
      }
      //          if(ua.contains("iPhone"))
      //          {
      //            val value=ua.split("DID")
      //            if(value.size>1)
      //            {
      //              result= value(1).substring(1,33).trim
      //            }
      //          }
      //          else if(ua.contains("Android"))
      //          {
      //            val value=ua.split("APPCID")
      //            if(value.size>1)
      //            {
      //              result= value(1).substring(1,37).trim
      //            }
      //          }
    }
    return result
  }

  /*app获取vid*/
  def getAppVidValue(vid:String,ua:String,appType:String,billtype:String,length:Int):String=
  {
    var result=vid
    if(ua.contains(appType))
    {
      val value=ua.split(billtype)
      if(value.size>1)
      {
        result=  value(1).split(" ")(0).split("/")(1).trim
      }
    }
    result
  }

  /*app获取Uid*/
  def getAPPUid(uid:String,ua:String):String=
  {
    var result=uid
    if(ua.contains("360CHE")&&ua.contains("USERID")&&(ua.contains("iPhone")||ua.contains("Android") || ua.contains("iPad")))
    {
      val value=ua.split("USERID")
      if(value.size>1)
      {
        val resultuid=value(1).split("\\s")
        val appuid=resultuid(0).substring(1).trim
        if(appuid!="0")
        {
          result=appuid
        }
      }
    }
    return result
  }

  /*从mysql查询得到t_type数据*/
  def getTypeByMsql(sqlBase: MySqlBaseDao,spark:SparkSession): DataFrame =
  {
    sqlBase.getDfTable(
      """
        |(select cast(substr(f_code,1,2) as SIGNED) as parentid,b.f_isdefault as defaultvalue
        |,b.f_urlregular as urlregular
        |,cast(a.F_code as SIGNED) as code from
        |t_type as a join t_typeregular as b on a.f_id=b.f_typeid
        |where a.f_isdelete=0 and b.f_isdelete=0) as t
        |""".stripMargin,spark)
  }

  /* 得到t_type code码*/
  def getCode(ua:String,value:String,arrays: Array[Row]):String=
  {
    var code="0"
    // var array=arrays.filter(it=> it.getAs[Long]("channlPagecode")>1000 && it.getAs[Long]("channlPagecode")<10000 && GetRegular(value,it.getAs("urlregular")))  //正则匹配channlpage
    //  if(array.length<=0) {
    var array = arrays.filter(it =>Common.GetRegular(value, it.getAs("urlregular"))) //正则匹配channl
    //  array=arrayvt
    // }

    if(array!=null &&array.length>0)//是否正则匹配成功
    {
      if(array.length>1) //如果正则匹配数量>1
      {
        val vt=getUa(ua)
        val array_1=array.filter(it=>it.getAs("parentid")==vt)//匹配ua
        if(array_1.length>0)
        {
          val array_sort=  array_1.sortBy(r => (r.getAs[Long]("code"))).reverse //降序排序， 然后取第一条赋值
          code=array_sort(0).getAs[Long]("code").toString
        }
        else
        {
          var array_2=array.filter(it=>it.getAs("defaultvalue")==1) //匹配默认值
          if(array_2.length>0)
          {
            val array_sort= array_2. sortBy(r => (r.getAs[Long]("code"))).reverse //降序排序， 然后取第一条赋值
            code=array_sort(0).getAs[Long]("code").toString
          }
        }
      }
      else
      {
        code=array(0).getAs[Long]("code").toString
      }
    }
    return  code
  }

  /*匹配user_agent判断vt 1 pc,2,ios,3.anrod,4 m站 5 微信端*/
  def  getUa(ua:String):Int={
    if(ua.contains("Windows")|| ua.contains("Macintosh"))
      return 11
    else if(ua.contains("MicroMessenger"))
      return 15
    else if(!ua.contains("MicroMessenger")&& !ua.contains("360CHE")&& !ua.contains("Windows")&& !ua.contains("Macintosh"))
      return 12
    else if((ua.contains("iPhone") || ua.contains("iPad")) && ua.contains("360CHE"))
      return 13
    else if(ua.contains("Android")&&ua.contains("360CHE"))
      return 14
    else
      return 0
  }


  /*etl清洗*/
  def getWebRdd(logRdd:DataFrame,mySqlJson:Array[Row],dayValue:String):rdd.RDD[Row]=
  {
    //转换日志格式
    val rowRDD = logRdd.rdd.map(line => {

      var res="" ;var k="" ;var vtc=""; var i=""; var vtf="" ;var vtl="" ;var uid=""; var vid=""; var sid=""; var ip=""
      var ua="" ;var daytime="" ;var eventid="";var action="" ;var lable=""; var ga_vid=""; var dealers_uid="";  var day=""
      var hours=0; var code=""
      try{
        val args = line.getAs[String]("args").split("&")
        var sendTime = line.getAs[String]("@timestamp");
        if (!DateTranslate.timeIsFormat(sendTime)) {
          sendTime = DateTranslate.getNowTime("yyyy-MM-dd'T'HH:mm:ssXXX")
        }
        val days = getHourOrDayByTimeStamp(sendTime,"yyyyMMdd")
        hours = getHourOrDayByTimeStamp(sendTime, "HH").toInt
        daytime = getTimeTotimeStamp(sendTime).toString()
        val timestamp=getTimeStampToDate(getPValueByName(line.getAs[String]("args"), "vtc")+"000","yyyyMMdd")
        if(days==dayValue&&timestamp==dayValue)
        {
          res = getPValueByName_res(line.getAs[String]("args"), "res")
          k = URLDecoder.decode(ConverPercent.convertPercent(URLDecoder.decode(getPValueByName(line.getAs[String]("args"), "k"), "UTF-8")),"UTF-8")
          vtc = getPValueByName(line.getAs[String]("args"), "vtc")
          i = URLDecoder.decode(ConverPercent.convertPercent(URLDecoder.decode(getPValueByName(line.getAs[String]("args"), "i"), "UTF-8")),"UTF-8")
          vtf = getPValueByName(line.getAs[String]("args"), "vtf")
          vtl = getPValueByName(line.getAs[String]("args"), "vtl")
          uid = if (getPValueByName(line.getAs[String]("args"), "uid") == "delete") "" else getAPPUid(getPValueByName(line.getAs[String]("args"), "uid"), line.getAs[String]("user_agent"))
          vid = getAPPVid(getPValueByName(line.getAs[String]("args"), "vid"), line.getAs[String]("user_agent"))
          sid = getPValueByName(line.getAs[String]("args"), "sid")
          ip = line.getAs[String]("clientip")
          ua = line.getAs[String]("user_agent")

          eventid = if (args.length >= 13) getPValueByName(line.getAs[String]("args"), "eventid") else ""
          action = if (args.length >= 13) URLDecoder.decode(getPValueByName(line.getAs[String]("args"), "action"), "UTF-8") else ""
          lable = if (args.length >= 13) URLDecoder.decode(getPValueByName(line.getAs[String]("args"), "lable"), "UTF-8") else ""
          ga_vid = if (args.length >= 14) getPValueByName(line.getAs[String]("args"), "ga_vid") else ""
          dealers_uid = if (args.length >= 15) getPValueByName(line.getAs[String]("args"), "dealers_uid") else ""
          code = getCode(line.getAs[String]("user_agent"), k, mySqlJson)
          day = getHourOrDayByTimeStamp(sendTime,"yyyyMMdd")
          //      Row(res, k, vtc, i, vtf, vtl, uid, vid,sid,ip,ua,daytime, eventid,action,lable,ga_vid,dealers_uid, day)
        }
      } catch {
        case ex: Exception => {
          println(ex)
        }

      }
      Row(res, k, vtc, i, vtf, vtl, uid, vid, sid, ip, ua, daytime, eventid, action, lable, code, ga_vid, dealers_uid, hours, day)
    })
    return rowRDD
  }

  //      /*时间戳转日期*/
  //      def getTimeStampToDate(tm:String) :String={
  //        val fm = new SimpleDateFormat("yyyyMMdd")
  //        val tim = fm.format(new Date(tm.toLong))
  //        tim
  //      }
  //
  //      /*时间戳转小时*/
  //      def getTimeStampToHour(tm:String) :String={
  //        val fm = new SimpleDateFormat("HH")
  //        val tim = fm.format(new Date(tm.toLong))
  //        tim
  //      }

  /*时间戳转换*/
  def getTimeStampToDate(tm:String,formatType:String) :String={
    val fm = new SimpleDateFormat(formatType)
    val tim = fm.format(new Date(tm.toLong))
    tim
  }

  /*日期类型转换时间戳*/
  def getTimeTotimeStamp(tm: String): Long = {
    val dateFormat: SimpleDateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssX")
    val dt = dateFormat.parse(tm)
    //val aa = dateFormat.format(dt)
    val tim: Long = dt.getTime()
    // println(tim)
    tim
  }

  //      /*根据日期得到小时的字符串数据*/
  //      def getHourByTimeStamp(date:String):String={
  //        val dateFormat: SimpleDateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssX")
  //        val dateHour: SimpleDateFormat = new SimpleDateFormat("HH")
  //        val dt = dateFormat.parse(date)
  //        val hours = dateHour.format(dt.getTime())
  //        return hours
  //      }
  //
  //      /*根据日期得到类型为20191010的字符串数据*/
  //      def getDayByTimeStamp(date:String):String={
  //        val dateFormat: SimpleDateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssX")
  //        val dateHour: SimpleDateFormat = new SimpleDateFormat("yyyyMMdd")
  //        val dt = dateFormat.parse(date)
  //        val days = dateHour.format(dt.getTime())
  //        return days
  //      }

  /*日期类型转换天，小时*/
  def getHourOrDayByTimeStamp(date: String,formatType:String): String = {
    val dateFormat: SimpleDateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssX")
    val dateHour: SimpleDateFormat = new SimpleDateFormat(formatType)
    val dt = dateFormat.parse(date)
    val hours = dateHour.format(dt.getTime())
    return hours
  }

  def webscLogClean(day:String): Unit ={
    val spark=MySparkSession.conn()
    val resultTable="t_logdata_websc"
    val myKudu=new MyKudu(spark)
    myKudu.select("t_logdata_websc_streaming").createOrReplaceTempView("t_websc_tmp")
    val webscKudu = spark.sql(
      s"""
         |select res,k,vtc,i,vtf,vtl,case when uid rlike '^[1-9][0-9]*$$' then uid else '' end as uid,
         |vid,sid,ip,ua,daytime,eventid,action,lable,code,user_id,
         |from_unixtime(cast(substr(daytime,0,10) as bigint), 'HH') as hours,province,city,is_first_day,event_duration,platform_type,manufacturer,os,contentId,event,day,element_id,element_content,title,referrer_title,element_position,openid,unionid,type_id,page_title,tag1,tag2
         |from t_websc_tmp where day='$day'
         |""".stripMargin)
    spark.sql(s"alter table $resultTable drop if exists partition(day='$day')")
    webscKudu.repartition(1).write.format("hive")
      .mode(SaveMode.Append).partitionBy("day").saveAsTable(s"$resultTable")
  }

  /**
   * 测试卡家天眼配置正则
   */
  def getCodeTest(): Unit ={

    val ss=MySparkSession.conn()
    val sqlBase=new MySqlBaseDao()
    val jsonArr=getTypeByMsql(sqlBase,ss).collect()
    //    val k="https://news-app.360che.com/article.html?articleId=174109"
    //    val ua="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    //    val code=getCode(ua,k,jsonArr)
    //    println(code)
    //    return
    val structTypeCode = StructType(Array(
      StructField("client", IntegerType, true),
      StructField("k", StringType, true),
      StructField("ua", StringType, true),
      StructField("code", StringType, true)
    ))
    val codeRDD=ss.sql(
      """
        |select k,ua from t_logdata where day="20220810" and code =0 and parse_url(k,"HOST")='truckmall.360che.com'
        |""".stripMargin)
      .rdd.map(data=>{
      val k=data.getAs[String]("k")
      val ua=data.getAs[String]("ua")
      val code=getCode(ua,k,jsonArr)
      val client=getUa(ua)
      Row(client,k,ua,code)
    }
    )
    ss.sqlContext.createDataFrame(codeRDD,structTypeCode)
      .filter("code=0")
      //      .filter("code rlike '^11'")
      .show(200,false)
  }
}
