package com.che.hadoop.logclean.bo.discard

import com.che.hadoop.underlay.dao
import org.apache.spark.sql.SparkSession

/**
 * Created by zkpk on 1/4/19.
 * web日志code转码
 */
object logtransform {

  def main(args: Array[String]): Unit = {
//     savehive("20191029")
  }

    /*转换mysql t_type.f_code数据保存到Hive*/
   def savehive(day:String,spark:SparkSession):Unit={
//      val spark=dao.MySparkSession.conn
     val log=  dao.MySparkSession.conn().sql(s"select *," +
       s"from_unixtime(cast(substring(vtc,0,10) as bigint),'yyyyMMdd') as vtcvale," +
       s"from_unixtime(cast(substring(daytime,0,10) as bigint),'yyyyMMdd') as daytime1 from t_logdata where day=$day").createOrReplaceTempView("t_log")

     spark.sql(s"select res,k,vtc,i,vtf,vtl,uid,vid,sid,ip,ua,daytime,eventid,action,lable," +
       s"code,ga_vid,dealers_uid, " +
       s"cast(from_unixtime(cast(substring(daytime,0,10) as bigint),'HH') as int) as hours from t_log where vtcvale=$day and daytime1=$day").createOrReplaceTempView("t_logresult")


     /*合并小文件*/
     spark.sql("set mapred.reduce.tasks=1")
     val sql =
       s"""insert overwrite table t_logdata partition(day=$day)
          |select res,k,vtc,i,vtf,vtl,uid,vid,sid,ip,ua,daytime,eventid,action,lable,code,ga_vid,dealers_uid, case when hours is NULL then -1 else hours end hours
          |from  t_logresult   DISTRIBUTE BY rand()""".stripMargin
     spark.sql(sql)
   }
}
