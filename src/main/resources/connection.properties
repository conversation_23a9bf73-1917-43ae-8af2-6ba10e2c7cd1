#-----------------------------------数据库链接-----------------------------------
##---------80数据库废弃，迁移到线上
#che.driver=com.mysql.cj.jdbc.Driver
#che.url=****************************************************************************
#che.username=root
#che.password=4271bf88302e7108
#che.max_connection=8
#che.connection_num=10
#
#portrait.driver=com.mysql.cj.jdbc.Driver
#portrait.url=***************************************************************************
#portrait.username=root
#portrait.password=4271bf88302e7108
#portrait.max_connection=8
#portrait.connection_num=100
#数据据切换，che和portrait同bigdata2
che.driver=com.mysql.cj.jdbc.Driver
#che.url=****************************************************************************************
#che.url=****************************************************************************************
che.url=************************************************************************************************************************
che.username=2C_Hits_rw
che.password=SBMlbWxR8Zd3S6wo
che.max_connection=8
che.connection_num=10

portrait.driver=com.mysql.cj.jdbc.Driver
#portrait.url=***************************************************************************************
portrait.url=***********************************************************************************************************************

portrait.username=2C_Hits_rw
portrait.password=SBMlbWxR8Zd3S6wo
portrait.max_connection=8
portrait.connection_num=10

ga.driver=com.mysql.cj.jdbc.Driver
#ga.url=**************************************************************************************************
ga.username=digu
ga.password=digu
ga.max_connection=8
ga.connection_num=10

#数据挖掘与产品库（张春阳）对接中间数据库连接
bigdata.driver=com.mysql.cj.jdbc.Driver
#bigdata.url=************************************************************************************
bigdata.username=Bigdata2Sy
bigdata.password=360che.com
bigdata.max_connection=8
bigdata.connection_num=10

#数据挖掘与产品库（张春阳）对接中间数据库连接 测试地址
bigdata_test.driver=com.mysql.cj.jdbc.Driver
#bigdata_test.url=************************************************************************************
bigdata_test.username=Bigdata2Sy
bigdata_test.password=360Che.com
bigdata_test.max_connection=8
bigdata_test.connection_num=10

#数据挖掘网易有数数据库(正式环境)
bigdata2.driver=com.mysql.cj.jdbc.Driver
#bigdata2.url=****************************************************************************************
bigdata2.url=************************************************************************************************************************
bigdata2.username=2C_Hits_rw
bigdata2.password=SBMlbWxR8Zd3S6wo
bigdata2.max_connection=8
bigdata2.connection_num=10

#数据挖掘网易有数数据库(测试环境)
bigdata_test2.driver=com.mysql.cj.jdbc.Driver
#bigdata_test2.url=****************************************************************************************
#bigdata_test2.url=******************************************************************************************
bigdata_test2.username=2c_hits_rw
bigdata_test2.password=SF2uQFXoTEI!#uNn
bigdata_test2.max_connection=8
bigdata_test2.connection_num=10

#备份库sqlserver数据库链接
truckhome.driver=com.microsoft.sqlserver.jdbc.SQLServerDriver
truckhome.url=******************************************************;
#truckhome.url=*******************************************************;
truckhome.username=bigdata_r
truckhome.password=360che.com
#truckhome.username=TruckhomeDB
#truckhome.password=truckhome#!#
truckhome.max_connection=8
truckhome.connection_num=10
#--------------------------------其它--------------
#kudu.master=dn-02.360che.com:7051,dn-03.360che.com:7051,dn-04.360che.com:7051
kudu.master=172.16.10.1:7051,172.16.10.248:7051,172.16.10.252:7051
#kudu.master=hadoop03:7051
#集群HDFS根路径
hdfs.path=hdfs://hdfs-cluster
oss-hdfs.uri=oss://360che-bigdata.cn-beijing.oss-dls.aliyuncs.com
#-----------------------------------接口-----------------------------------
#----------线索数据接口——————————————
api.dealer.clues = http://dealer-api.360che.com/dealerlocalvisitapi/api/InquiryClue/PullInquiryClues
api.dealer.cluesSpm =http://dealer-api.360che.com/dealerlocalvisitapi/api/InquiryClue/InquiryCluesSPM
dealer.appid=group_datamining
dealer.key=4e1383b0674731eb14947efecfc52ae1
#经销商认证数据
dealer.verify.key=df45se4r34#45v
#正式环境
api.dealer.verify.allData=http://dealer-api.360che.com/Business/GetDealerTelData.aspx
api.dealer.verify.closeData=http://dealer-api.360che.com/Business/GetDealerAllCloseIds.aspx
#测试环境
#api.dealer.verify.allData=http://dealer-api-test.360che.com/Business/GetDealerTelData.aspx
#api.dealer.verify.closeData=http://dealer-api-test.360che.com/Business/GetDealerAllCloseIds.aspx

#付费经销商热销车型数据
api.dealer.verify.hot.product=http://dealer-api.360che.com/Business/GetDealerProvinceHotProduct.aspx
#测试环境
#api.dealer.verify.hot.product=http://dealer-api-test.360che.com/Business/GetDealerProvinceHotProduct.aspx

#----------司机招聘注册信息接口——————————————
#正式
api.driverjob.register=https://job-applet.360che.com/api/backend/user/bigdataUser
#测试
#api.driverjob.register=https://job-applet-test.360che.com/api/backend/user/bigdataUser

#----------文章信息同步接口——————————————
#测试
#api.article.info=https://didi.360che.com/wxcms-api/api/DataCollection/GetArticle
#正式
api.article.info=https://wxcms-api.360che.com/api/DataCollection/GetArticle

#----------视频信息同步接口——————————————
#测试
#api.video.info=https://wxcms-api-test.360che.com/api/DataCollection/GetVideo
#正式
api.video.info=https://wxcms-api.360che.com/api/DataCollection/GetVideo

#----------直播信息同步接口——————————————
#测试
#api.live.info=https://live-admin-test.360che.com.cn/pull-live-data/pull-lives
#正式
api.live.info=https://live-admin.360che.com.cn/pull-live-data/pull-lives

#----------图集信息同步接口——————————————
#测试
#api.picture.info=https://didi.360che.com/wxcms-api/api/DataCollection/GetShow
#正式
api.picture.info=https://wxcms-api.360che.com/api/DataCollection/GetShow

#-----------bbs_user更新用户接口---------------
api.bbs.uids= https://bbs.360che.com/interface/app/index.php?action=UserData&type=terminal&method=getUids
#-----------bbs_user用户信息明细接口---------------
#测试
#api.bbs.uid_info=https://bbs-api-test.360che.com/interface/app/index.php?action=UserData&type=terminal&fieldAppend=ORC&uid=
#正式
api.bbs.uid_info=https://bbs.360che.com/interface/app/index.php?action=UserData&type=terminal&fieldAppend=ORC&uid=
#用户注册信息接口
#正式
api.bbs.regin = http://bbs-api.360che.com/interface/app/index.php?type=terminal&action=BigDataListener&method=dailyRegUsers
#测试
api.bbs.regin.test = http://bbs-test.360che.com/interface/app/index.php?type=terminal&action=BigDataListener&method=dailyRegUsers
#附近商家接口
api.nearby_shop=https://zhihuishop-api.360che.com/near_front/get_near_shop_info
#十大热帖接口
#app
api.ten_hot_thread.app = https://liaoche-api.360che.com/e/threads/ten-hot-for-big
#pc
api.ten_hot_thread.pc = https://bbs-api.360che.com/interface/app/index.php?action=TenHotThread&method=theDay
#获取经销商用户
api.dealer_user=http://dealer-api.360che.com/DealerSaas/GetDealerUsers.aspx?maxTimestamp=
#二手车用户数据
api.second_truck_user=https://tao-api.m.360che.com/foreign/big-data/user?maxTimestamp=
#测试地址
#api.second_truck_user=https://tao-api-test.m.360che.com/foreign/big-data/user?maxTimestamp=
#二手车发布数据
api.second_truck_data=https://tao-api.m.360che.com/foreign/big-data/truck?maxTimestamp=