package com.che.hadoop.logclean.activity

import com.che.hadoop.logclean.bo.UserMap
import com.che.hadoop.underlay.dao.{MySparkSession, MySqlBaseDao}
import com.che.hadoop.underlay.tool.date.DateTranslate

/**
 * 计算用户vid-uid映射
 * 从report工程下迁移过来的
 */
object UserMapJob {
  def main(args: Array[String]): Unit = {
    var startDay,endDay=DateTranslate.getDate(1)
    var dbEnv="default."
    if(args.length>=1){
      dbEnv=args(0)
      if(args.length==2){
        startDay=args(1)
        endDay=startDay
      }
      if(args.length==3){
        startDay=args(1)
        endDay=args(2)
      }
    }
    val spark = MySparkSession.conn()
    spark.sparkContext.setLogLevel("WARN")
    val exe=new MySqlBaseDao()
    DateTranslate.getDatesBetween(startDay,endDay).foreach(day=>{
      println(s"计算日期：$day")
      UserMap.cookieUser(spark,exe,day)
    })

  }
}
