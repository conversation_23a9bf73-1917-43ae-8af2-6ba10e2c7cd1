package com.che.hadoop.logclean.bo.discard

import com.che.hadoop.underlay.dao.{MyKudu, MySparkSession, MySqlBaseDao}
import org.apache.kudu.spark.kudu.KuduWriteOptions
import org.apache.spark.sql.SparkSession

/**
 * 新版卡友圈相关数据 -转实时离线废弃
 * */
object pullNewKyq {
  case class NewKyqPublish(uid: String, nickname: String, old_tid: String, tid: String, subject: String, message: String, post_time: String, client: String, `type`: String, content_type: String, status: String, isselfs: String, dun_isselfs: String, displayorder: String, update_time: String, first_label: String, second_label: String, third_label: String, scenario: String, relation_id: String, talk_id: String, timestamp: String,position: String,province_code: String,city_code: String, location: String, shopid: String, dealerid: String, relation_products: String)

  def main(args: Array[String]): Unit = {
//    oldSmallVideo()
//    test()
//    val spark = MySparkSession.conn()
    test2()
//    val day = "20240108"
//    val myKudu = new MyKudu(spark)
//    myKudu.select("t_logdata_thba_streaming").select("data", "billtype", "timestamp", "operation_state", "env").where(
//      s"""
//         |day>='$day'
//         |and env = 'test'
//         |""".stripMargin).createOrReplaceTempView("t_biz_data_temp")
//    newKyqPublish(spark, day)
  }


  /**
   * 新版卡友圈发布数据
   *
   * @param spark   SparkSession
   * @param sqlBase MySql工具类
   * @param day     计算日期
   */
  def newKyqPublish(spark: SparkSession, day: String): Unit = {
      val myKudu=new MyKudu(spark)
      import spark.implicits._
      spark.sql(
        s"""
           |select c0 as uid,c1 as nickname,c2 as old_tid,c3 as tid,c4 as subject,c5 as message,c6 as post_time,c7 as client,c8 as type,c9 as content_type,c10 as status,c11 as isselfs,c12 as dun_isselfs,c13 as displayorder,c14 as update_time,c15 as first_label,c16 as second_label,c17 as third_label,c18 as scenario,c19 as relation_id,c20 as talk_id,c21 as position,c22 as province_code,c23 as city_code,c24 as location,c25 as shopid,c26 as dealerid,c27 as relation_products,timestamp from
           |(select json_tuple(data, 'uid', 'nickName', 'oldTid', 'tid', 'subject', 'message','postTime','client','type','contentType','status','isselfs','dunIsselfs','displayorder','updateTime','firstLabel','secondLabel','thirdLabel','scenario','relationId','talkId','position','provinceCode','cityCode','location','shopid','dealerid','relation_products'),`timestamp`
           |from t_biz_data_temp where billType='new_kyq_post') as t
           |""".stripMargin).as[NewKyqPublish].groupByKey(_.tid).mapGroups((tid,attrs)=>{
        val result = new Array[String](29)
        var timestampMax = 0l
        for (attr <- attrs.toSeq.sortBy(_.timestamp.toLong)) {
          if(attr.timestamp.toLong>timestampMax){
            timestampMax = attr.timestamp.toLong
            if (attr.uid != null && attr.uid.nonEmpty) {
              result(0) = attr.uid
            }
            if (attr.nickname!= null && attr.nickname.nonEmpty) {
              result(1) = attr.nickname
            }
            if (attr.old_tid!= null && attr.old_tid.nonEmpty) {
              result(2) = attr.old_tid
            }
            if (attr.tid!= null && attr.tid.nonEmpty) {
              result(3) = attr.tid
            }
            if (attr.subject!= null && attr.subject.nonEmpty) {
              result(4) = attr.subject
            }
            if (attr.message!= null && attr.message.nonEmpty) {
              result(5) = attr.message
            }
            if (attr.post_time!= null && attr.post_time.nonEmpty) {
              result(6) = attr.post_time
            }
            if (attr.client!= null && attr.client.nonEmpty) {
              result(7) = attr.client
            }
            if (attr.`type`!= null && attr.`type`.nonEmpty) {
              result(8) = attr.`type`
            }
            if (attr.content_type!= null && attr.content_type.nonEmpty) {
              result(9) = attr.content_type
            }
            if (attr.status!= null && attr.status.nonEmpty) {
              result(10) = attr.status
            }
            if (attr.isselfs!= null && attr.isselfs.nonEmpty) {
              result(11) = attr.isselfs
            }
            if (attr.dun_isselfs!= null && attr.dun_isselfs.nonEmpty) {
              result(12) = attr.dun_isselfs
            }
            if (attr.displayorder!= null && attr.displayorder.nonEmpty) {
              result(13) = attr.displayorder
            }
            if (attr.update_time!= null && attr.update_time.nonEmpty) {
              result(14) = attr.update_time
            }
            if (attr.first_label!= null && attr.first_label.nonEmpty) {
              result(15) = attr.first_label
            }
            if (attr.second_label!= null && attr.second_label.nonEmpty) {
              result(16) = attr.second_label
            }
            if (attr.third_label!= null && attr.third_label.nonEmpty) {
              result(17) = attr.third_label
            }
            if (attr.scenario!= null && attr.scenario.nonEmpty) {
              result(18) = attr.scenario
            }
            if (attr.relation_id!= null && attr.relation_id.nonEmpty) {
              result(19) = attr.relation_id
            }
            if (attr.talk_id!= null && attr.talk_id.nonEmpty) {
              result(20) = attr.talk_id
            }
            if (attr.timestamp!= null && attr.timestamp.nonEmpty) {
              result(21) = attr.timestamp
            }
            if (attr.position!= null && attr.position.nonEmpty) {
              result(22) = attr.position
            }
            if (attr.province_code!= null && attr.province_code.nonEmpty) {
              result(23) = attr.province_code
            }
            if (attr.city_code!= null && attr.city_code.nonEmpty) {
              result(24) = attr.city_code
            }
            if (attr.location!= null && attr.location.nonEmpty) {
              result(25) = attr.location
            }
            if (attr.shopid!= null && attr.shopid.nonEmpty) {
              result(26) = attr.shopid
            }
            if (attr.dealerid!= null && attr.dealerid.nonEmpty) {
              result(27) = attr.dealerid
            }
            if (attr.relation_products!= null && attr.relation_products.nonEmpty) {
              result(28) = attr.relation_products
            }
          }
        }
        NewKyqPublish(result(0), result(1), result(2), result(3), result(4), result(5), result(6), result(7), result(8), result(9), result(10), result(11), result(12), result(13), result(14), result(15), result(16), result(17), result(18), result(19), result(20), result(21), result(22), result(23), result(24), result(25), result(26), result(27), result(28))
      }).createOrReplaceTempView("t_tmp")
    val resultDF = spark.sql(
      """
        |select cast(tid as int) as tid,uid,nickname,cast(old_tid as int) as old_tid,subject,message,post_time,client,cast(`type` as int)as`type`,cast(content_type as int)as content_type,cast(status as int)as status,cast(isselfs as int)as isselfs,cast(dun_isselfs as int)as dun_isselfs,
        |cast(displayorder as int)as displayorder,update_time,first_label,second_label,third_label,scenario,relation_id,talk_id,position,DATE_FORMAT(post_time,"yyyyMMdd")  as publish_date,cast(shopid as int) as shopid,cast(dealerid as int) as dealerid,province_code,city_code,location,relation_products as dealer_relation_products,if(province_code='0','',regexp_extract(location,'([^\-]*)(-([^\-]*))?',1)) as province, if(city_code='0','',regexp_extract(location,'([^\-]*)(-([^\-]*))?',3)) as city from t_tmp
        |""".stripMargin)
      //数据写入kudu
      myKudu.upsert(resultDF,"t_dws_kyq_post_streaming_test",KuduWriteOptions(ignoreNull=true))

  }

  def oldSmallVideo(): Unit = {
    val spark=MySparkSession.conn()
    val myKudu=new MyKudu(spark)
    myKudu.select("t_dws_bbs_post_streaming").createOrReplaceTempView("t_dws_bbs_post_streaming_ali2")
    import spark.implicits._
    Seq((4117805))
      .toDF("tid").createOrReplaceTempView("t_dealer_tmp")
    val df=spark.sql(
      """
        |select a.tid, uid, nickname,old_tid, subject, message,post_time,client ,if(b.tid is null,null,1) as `type`,content_type,status,isselfs,dun_isselfs,displayorder,update_time,first_label,second_label,third_label,scenario,relation_id,talk_id,position,publish_date from
        |(SELECT bbs_tid as tid, uid, nickname, bbs_tid as old_tid, subject, message, bbs_publish_time as post_time, case when client rlike 'android' then 'android' when client rlike 'ios' then 'ios' else client end as client ,
        |null as`type`,1 as content_type,null as status,null as isselfs,null as dun_isselfs, is_del as displayorder,update_time, bbs_first_label as first_label, bbs_second_label as second_label, bbs_third_label as third_label, bbs_scenario as scenario, relationid as relation_id,null as talk_id,position,
        |publish_date
        |from t_dws_bbs_post_streaming_ali2 where publish_date>='20231201' and bbs_type =2 and subject not rlike '测试') as a
        |left join t_dealer_tmp as b on a.tid=b.tid
        |""".stripMargin)
    myKudu.upsert(df,"t_dws_kyq_post_streaming",KuduWriteOptions(ignoreNull=true))
  }

  def test(): Unit = {
    val spark = MySparkSession.conn()
    val myKudu = new MyKudu(spark)
    myKudu.select("t_dws_kyq_post_streaming").createOrReplaceTempView("t_dws_kyq_post_streaming_123")
    val df=spark.sql("select tid,post_time as update_time from t_dws_kyq_post_streaming_123 where update_time rlike '[0-9]{8}'")
//    myKudu.delete(df,"t_dws_kyq_post_streaming")
//    val sqlBase = new MySqlBaseDao("che_test")
//    sqlBase.getDfTable("(select tid,`type`,isselfs ,is_del as displayorder,shop_id from db_hitsstatistic_test.t_kyq_append_tmp)t",spark).createOrReplaceTempView("t_tmp")
//    val df=spark.sql("select tid,`type`,isselfs ,displayorder,if(shop_id ='',0,cast(shop_id as Int)) as shopid from t_tmp")
//    df.printSchema()
    myKudu.upsert(df, "t_dws_kyq_post_streaming", KuduWriteOptions(ignoreNull = true))
  }

  def test2(): Unit = {
    val spark = MySparkSession.conn()
    val myKudu = new MyKudu(spark)
    //01根据俊亮导出的数据更新
    myKudu.select("t_dwd_video_streaming").createOrReplaceTempView("t_dwd_video_streaming_view")
    val sqlBase = new MySqlBaseDao("che_test")
//    sqlBase.getDfTable("(select tid,`type`,isselfs ,displayorder,shopid,province_code,city_code,position,location,if(province_code=0,'',SUBSTRING_INDEX(location,'-',1)) as province,if(city_code=0,'',SUBSTRING_INDEX(SUBSTRING_INDEX(location,'-',2),'-',-1))as city from db_hitsstatistic_test.t_kyq_0115)t",spark).createOrReplaceTempView("t_tmp")
//    val df=spark.sql("select tid,`type`,isselfs,0 as dun_isselfs ,displayorder,shopid,cast(province_code as string) as province_code,cast(city_code as string) as city_code,position,location,province,city,1 as status from t_tmp")
//    //    df.printSchema()
//    myKudu.upsert(df, "t_dws_kyq_post_streaming", KuduWriteOptions(ignoreNull = true))
//    02根据csv文件更新
    spark.read.option("header", "true")
              .csv("file:////H:\\Desk\\aaa.csv").createOrReplaceTempView("t")
    val df=spark.sql(
      """
        |select cast(a.tid as int) as tid ,first_label ,second_label ,third_label ,if(scenario='',null,scenario) as scenario,
        |case when size(split(relation_id,'#'))=1 and relation_id='' then null
        |     when size(split(relation_id,'#'))=1 and relation_id<>'' then concat(relation_id,'#0#0#0#0')
        |     when size(split(relation_id,'#'))=2 then concat(relation_id,'#0#0#0')
        |     when size(split(relation_id,'#'))=3 then concat(relation_id,'#0#0')
        |     when size(split(relation_id,'#'))=4 then concat(relation_id,'#0')
        |     else relation_id end as relation_id
        |from t  as a
        |inner join
        |t_dwd_video_streaming_view as b on a.id=b.id
        |""".stripMargin)
    df.show(1000,false)
    myKudu.upsert(df, "t_dws_kyq_post_streaming", KuduWriteOptions(ignoreNull = true))

    //03根据t_dws_bbs_post 更新表t_dws_kyq_post_streaming
//    myKudu.select("t_dws_bbs_post_streaming").where("publish_date>'20230601'").createOrReplaceTempView("t_dws_bbs_post_streaming_view")
//    myKudu.select("t_dws_kyq_post_streaming").createOrReplaceTempView("t_dws_kyq_post_streaming_view")
//    val df=spark.sql(
//      """
//        |select bbs_tid as tid,bbs_first_label as first_label,bbs_second_label as second_label,bbs_third_label as third_label,relationid as relation_id,bbs_scenario as scenario from t_dws_bbs_post_streaming_view as a
//        |inner join t_dws_kyq_post_streaming_view as b on a.bbs_tid=b.tid
//        |""".stripMargin)
////    df.show(false)
//    myKudu.upsert(df, "t_dws_kyq_post_streaming", KuduWriteOptions(ignoreNull = true))

  }

}
