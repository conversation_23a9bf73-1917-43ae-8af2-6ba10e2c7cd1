package com.che.hadoop.logclean.activity

import com.che.hadoop.logclean.bo.UserMapStreaming
import com.che.hadoop.underlay.dao.{MySparkSession, MySqlBaseDao}

/**
 * 实时用户vid和uid映射数据
 */
object UserMapStreamingJob {
  def main(args: Array[String]): Unit = {
    val spark=MySparkSession.conn()
    val exe=new MySqlBaseDao()
    spark.sparkContext.setLogLevel("WARN")
    UserMapStreaming.cookieUserStreaming(spark,exe)
  }
}
