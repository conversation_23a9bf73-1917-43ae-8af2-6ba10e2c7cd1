package com.che.hadoop.logclean.activity.discard

import java.util.{Calendar, ResourceBundle}

import com.che.hadoop.logclean.bo.PullKyqData
import com.che.hadoop.logclean.utils.{Common, DingRobot}

/**
  * 数据大屏-问答小时数据(数据写到mysql-t_monitor_data_screen)
 * 该任务已废弃 李晓乾标注
 * 数据大屏切换成其它的数据
  */
object PullHelpDataHourJob {
    def main(args: Array[String]): Unit = {
        try {
            val cal: Calendar = Calendar.getInstance()
            val hour: Int = cal.get(Calendar.HOUR_OF_DAY)
            var day: String = Common.getDate(0)
            if (hour == 0) {
                day = Common.getDate(1)
            }
            PullKyqData.pullHelpDataHour(day, "")
        } catch {
            case exception: Exception => {
                println(exception)
                val robot = new DingRobot(ResourceBundle.getBundle("robot").getString("ding.monitor"))
                robot.sendAlert(s"【aliyun数据大屏-问答小时数据】\n原因：${exception.toString}")

            }
        }
    }
}
