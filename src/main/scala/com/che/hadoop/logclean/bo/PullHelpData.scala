package com.che.hadoop.logclean.bo

import java.util.ResourceBundle

import com.che.hadoop.underlay.dao.{MySparkSession, MySqlBaseDao}
import com.che.hadoop.logclean.utils.{DingRobot, UrlDecodeUtil}
import org.apache.spark.sql.SparkSession

object PullHelpData {
  private val spark = MySparkSession.conn("Logclean_PullHelpData")
  spark.udf.register("urlDecode", UrlDecodeUtil.urlDecode(_: String))


  def main(args: Array[String]): Unit = {
    val forHelpTable = "t_dwd_forhelp"
    spark.sql("set spark.sql.shuffle.partitions=1")
    spark.sql(
      s"""
         |INSERT OVERWRITE table $forHelpTable
         | SELECT
         |   id,title,(case when  classifyid in ('4','6','8','15') then '19' when classifyid='16' then '5'
         |    when classifyid='7' then '3' else classifyid end) as classifyid,
         |   (case when  classifyid in ('4','6','8','15') then '其他' when classifyid='16' then '规章政策'
         |   when classifyid='7' then '问路' else classifyname end) as  classifyname,
         |   createdatetime,
         |  userid,
         |  address,
         |    content,
         |   rewordvalue,
         |    referrerpage,
         |  referrerquery,
         |    viewterminal,
         |    coordinate,
         |  cityid,
         |   detailed,
         |  resultsnote,
         |  feedback,
         |  feedbackuid,
         |    solvedate,
         |  issolve,
         |   is_del,
         |  update_date
         | from $forHelpTable
         |""".stripMargin)
  }

  /**
   * 清洗原始数据
   */
  def cleanOdsData(spark:SparkSession,day: String,hiveDB:String): Unit = {
    val t_check_pre="aliyun_"
    try {
      //求助
      spark.sql(s"alter table ${hiveDB}t_forhelp drop if exists partition(day='$day')")
      val forHelpData = spark.sql("select json_tuple(data, 'id','title','classifyId','classifyName','createDateTime','userId','address','content','rewordValue','referrerPage','referrerQuery','viewTerminal','coordinate','cityId','detailed','resultsNote','feedBack','feedBackUid','solveDate','isSolve','scenario','relationId','circleId'),`timestamp`, operation_state,from_unixtime(substr(`timestamp`,0,10), 'yyyyMMdd') from t_biz_data_temp where billtype='help_t_forhelp'")
        .toDF("id", "title", "classifyId", "classifyName", "createDateTime", "userId", "address", "content", "rewordValue", "referrerPage", "referrerQuery", "viewTerminal", "coordinate", "cityId", "detailed", "resultsNote", "feedBack", "feedBackUid", "solveDate", "isSolve","scenario","relationid","circleid", "timestamp", "operationState", "day")
      forHelpData.dropDuplicates().write.mode("append").partitionBy("day").format("hive")
        .saveAsTable(hiveDB+"t_forhelp")

      //帮助回答/追问
      spark.sql(s"alter table ${hiveDB}t_help drop if exists partition(day='$day')")
      val t = spark.sql("select json_tuple(data, 'id','content','thankTitle','createDateTime','userId','forHelpId','isAdmin'),`timestamp`, operation_state,from_unixtime(substr(`timestamp`,0,10), 'yyyyMMdd') from t_biz_data_temp where billtype='help_t_help'").toDF("id", "content", "thankTitle", "createDateTime", "userId", "forHelpId", "isAdmin", "timestamp", "operationState", "day")
      t.dropDuplicates().write.mode("append").partitionBy("day").format("hive").saveAsTable(hiveDB+"t_help")

      //求助评论

      spark.sql(s"alter table ${hiveDB}t_forhelpdiscuss drop if exists partition(day='$day')")
      val forHelpDiscuss = spark.sql("select json_tuple(data, 'id','content','createDateTime','helpId','forHelpId','discussParentID','userID', 'userName'),`timestamp`, operation_state,from_unixtime(substr(`timestamp`,0,10), 'yyyyMMdd') from t_biz_data_temp where billtype='help_t_forhelpdiscuss'")
        .toDF("id", "content", "createDateTime", "helpId", "forHelpId", "discussParentID", "userId", "userName", "timestamp", "operationState", "day")
      forHelpDiscuss.dropDuplicates().write.mode("append").partitionBy("day").format("hive").saveAsTable(hiveDB+"t_forhelpdiscuss")

      //求助关注
      spark.sql(s"alter table ${hiveDB}t_forhelpfocuson drop if exists partition(day='$day')")
      val forHelpFocuson = spark.sql("select json_tuple(data, 'id','createDateTime','forHelpId','userID'),`timestamp`, operation_state,from_unixtime(substr(`timestamp`,0,10), 'yyyyMMdd') from t_biz_data_temp where billtype='help_t_forhelpfocuson'")
        .toDF("id", "createDateTime", "forHelpId", "userId", "timestamp", "operationState", "day")
      forHelpFocuson.dropDuplicates().write.mode("append").partitionBy("day").format("hive").saveAsTable(hiveDB+"t_forhelpfocuson")
      val exec = new MySqlBaseDao()
      exec.executeInsertOrUpdate(s"update t_check set flag=1 , message='$day:运行成功' where name='${t_check_pre}t_help'",null)
    } catch {
      case e: Exception => {
        e.printStackTrace()
        try{
          val exec = new MySqlBaseDao()

          exec.executeInsertOrUpdate(s"update t_check set flag=2, message='$day:${t_check_pre}t_help 运行失败' where name='${t_check_pre}t_help'",null)
        }catch {
          case e:Throwable=>{
            e.printStackTrace()
          }
        }
      }
    }
  }

  /**
   * 发布求助-数据明细层
   */
  def saveHelpDetail(spark:SparkSession,day: String,hiveDB:String): Unit = {
    val t_check_pre="aliyun_"
    val dwdForHelpTable = hiveDB+"t_dwd_forhelp"
    val forHelpTable = hiveDB+"t_forhelp"
    val dwdHelpTable = hiveDB+"t_dwd_help"
    val helpTable = hiveDB+"t_help"
    try {
      spark.sql("set spark.sql.shuffle.partitions=1")
      spark.sql(
        s"""
           |INSERT OVERWRITE table $dwdForHelpTable
           | SELECT
           |   cast(nvl(ot.id,t.id) as int) as id,nvl(ot.title,t.title) as title,nvl(ot.classifyid,t.classifyid) as classifyid,
           |   nvl(ot.classifyname,t.classifyname) as classifyname,
           |   nvl(ot.create_date,t.createdatetime) as createdatetime,
           |   nvl(ot.userid,t.userid) as userid,
           |   nvl(ot.address,t.address)as address,
           |   nvl(ot.content,t.content) as content,
           |   nvl(ot.rewordvalue,t.rewordvalue) as rewordvalue,
           |   nvl(ot.referrerpage,t.referrerpage) as referrerpage,
           |   nvl(ot.referrerquery,t.referrerquery) as referrerquery,
           |   nvl(ot.viewterminal,t.viewterminal) as viewterminal,
           |   nvl(ot.coordinate,t.coordinate) as coordinate,
           |   nvl(ot.cityid,t.cityid) as cityid,
           |   nvl(ot.detailed,t.detailed) as detailed,
           |   nvl(ot.resultsnote,t.resultsnote) as resultsnote,
           |   nvl(ot.feedback,t.feedback) as feedback,
           |   nvl(ot.feedbackuid,t.feedbackuid) as feedbackuid,
           |   nvl(ot.solvedate,t.solvedate) as solvedate,
           |   cast(nvl(ot.issolve,t.issolve) as int) as issolve,
           |   nvl(ot.is_del,t.is_del) as is_del,
           |   nvl(ot.update_date,t.update_date) as update_date,
           |   nvl(ot.scenario,t.scenario) as scenario,
           |   nvl(ot.relationid,t.relationid) as relationid,
           |   nvl(ot.circleid,t.circleid) as circleid
           | from $dwdForHelpTable t
           |full outer join
           | (
           | select *,if(operationstate='delete',1,0) as is_del from(
           | select ROW_NUMBER() over(PARTITION by id order by `timestamp` desc) as row_num,*,
           | from_unixtime(cast(`timestamp`/1000 as int),'yyyyMMdd') as update_date,
           |-- from_unixtime(to_unix_timestamp(createdatetime, 'yyyy/MM/dd HH:mm:ss'),'yyyy-MM-dd HH:mm:ss') as create_date
           | replace(createdatetime,"/","-") as create_date
           | from $forHelpTable where day='$day') t0
           | where row_num=1
           | ) ot
           |ON t.id=ot.id
           |""".stripMargin)
      spark.sql("set spark.sql.shuffle.partitions=3")
      //求助回答数据
      spark.sql(
        s"""
           |INSERT OVERWRITE table $dwdHelpTable
           | SELECT
           |   nvl(ot.id,t.id) as id,nvl(ot.content,t.content) as content,nvl(ot.thanktitle,t.thanktitle) as thanktitle,
           |   nvl(ot.create_date,t.createdatetime) as createdatetime,
           |   nvl(ot.userid,t.userid) as userid,
           |   nvl(ot.forhelpid,t.forhelpid)as forhelpid,
           |   nvl(ot.isadmin,t.isadmin) as isadmin,
           |   nvl(ot.is_del,t.is_del) as is_del,
           |   nvl(ot.update_date,t.update_date) as update_date
           | from $dwdHelpTable t
           |full outer join
           | (
           | select  *,if(operationstate='delete',1,0) as is_del from(
           | select ROW_NUMBER() over(PARTITION by id,forhelpid order by `timestamp` desc) as row_num,*,
           | from_unixtime(cast(`timestamp`/1000 as int),'yyyyMMdd') as update_date,
           |-- from_unixtime(to_unix_timestamp(createdatetime, 'yyyy/MM/dd HH:mm:ss'),'yyyy-MM-dd HH:mm:ss') as create_date
           | replace(createdatetime,"/","-") as create_date
           | from $helpTable where day='$day') t0
           | where row_num=1
           | ) ot
           |ON t.id=ot.id and  t.forhelpid=ot.forhelpid
           |""".stripMargin)
      val exec = new MySqlBaseDao()
      exec.executeInsertOrUpdate(s"update t_check set flag=1 , message='$day:运行成功' where name='${t_check_pre}t_dwd_help'",null)
    } catch {
      case e: Exception => {
        e.printStackTrace()
        try{
          val exec = new MySqlBaseDao()

          exec.executeInsertOrUpdate(s"update t_check set flag=2, message='$day:${t_check_pre}t_dwd_help 运行失败' where name='${t_check_pre}t_dwd_help'",null)
        }catch {
          case e:Throwable=>{
            e.printStackTrace()
          }
        }
      }
    }

  }

  /**
   * 生成求助恢复评论及关注问答明细数据
   * @param spark SparkSession
   * @param day 日期
   */
  def saveForHelpDiscussDetail(spark:SparkSession,day: String): Unit = {
    val t_check_pre="aliyun_"
    val forHelpDiscussTable = "t_dwd_forhelpdiscuss"
    val forHelpFocusTable = "t_dwd_forhelpfocuson"
    try {
      spark.sql("set spark.sql.shuffle.partitions=1")
      spark.sql(
        s"""
           |INSERT OVERWRITE table $forHelpDiscussTable
           | SELECT
           |   cast(nvl(ot.id,t.id) as int) as id,nvl(ot.content,t.content) as content,
           |   nvl(ot.create_date,t.createdatetime) as createdatetime,
           |   nvl(ot.helpid,t.helpid) as helpid,
           |   nvl(ot.forhelpid,t.forhelpid)as forhelpid,
           |   nvl(ot.discussparentid,t.discussparentid)as discussparentid,
           |   nvl(ot.userid,t.userid) as userid,
           |   nvl(ot.is_del,t.is_del) as is_del,
           |   nvl(ot.update_date,t.update_date) as update_date
           | from $forHelpDiscussTable t
           |full outer join
           | (
           | select *,if(operationstate='delete',1,0) as is_del from(
           | select ROW_NUMBER() over(PARTITION by id order by `timestamp` desc) as row_num,*,
           | from_unixtime(cast(`timestamp`/1000 as int),'yyyyMMdd') as update_date,
           |-- from_unixtime(to_unix_timestamp(createdatetime, 'yyyy/MM/dd HH:mm:ss'),'yyyy-MM-dd HH:mm:ss') as create_date
           |replace(createdatetime,"/","-") as create_date
           | from t_forhelpdiscuss where day='$day')t0
           | where row_num=1
           | ) ot
           |ON t.id=ot.id
           |""".stripMargin)
      spark.sql(
        s"""
           |INSERT OVERWRITE table $forHelpFocusTable
           | SELECT
           |   cast(nvl(ot.id,t.id) as int) as id,
           |   nvl(ot.create_date,t.createdatetime) as createdatetime,
           |   nvl(ot.forhelpid,t.forhelpid)as forhelpid,
           |   nvl(ot.userid,t.userid) as userid,
           |   nvl(ot.update_date,t.update_date) as update_date
           | from $forHelpFocusTable t
           |full outer join
           | (
           | select * from(
           | select ROW_NUMBER() over(PARTITION by id order by `timestamp` desc) as row_num,*,
           | from_unixtime(cast(`timestamp`/1000 as int),'yyyyMMdd') as update_date,
           |-- from_unixtime(to_unix_timestamp(createdatetime, 'yyyy/MM/dd HH:mm:ss'),'yyyy-MM-dd HH:mm:ss') as create_date
           | replace(createdatetime,"/","-") as create_date
           | from t_forhelpfocuson where day='$day') t0
           | where row_num=1
           | ) ot
           |ON t.id=ot.id
           |""".stripMargin)
      val exec = new MySqlBaseDao()
      exec.executeInsertOrUpdate(s"update t_check set flag=1 , message='$day:运行成功' where name='${t_check_pre}t_dwd_forhelpdiscuss'",null)
    } catch {
      case e: Exception => {
        e.printStackTrace()
        try{
          val exec = new MySqlBaseDao()

          exec.executeInsertOrUpdate(s"update t_check set flag=2, message='$day:${t_check_pre}t_dwd_forhelpdiscuss 运行失败' where name='${t_check_pre}t_dwd_forhelpdiscuss'",null)
        }catch {
          case e:Throwable=>{
            e.printStackTrace()
          }
        }
      }
    }
  }

}
