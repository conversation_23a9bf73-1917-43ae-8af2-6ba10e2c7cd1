package com.che.hadoop.logclean.bo.discard

import java.util.Base64

import com.alibaba.fastjson.JSON
import com.che.hadoop.underlay.dao.MySparkSession
import com.che.hadoop.logclean.utils.{Common, GZIPUtils, UrlDecodeUtil}
import org.apache.spark.rdd.RDD

/**
 * 将神策日志清洗写入神策平台的hdfs中
 */
object CleanScHistoryDataToHDFS {
  def main(args: Array[String]): Unit = {
    //验证是否能正常读写神策平台hdfs数据
//    val spark=MySparkSession.conn()
//    val sc=spark.sparkContext
//    val textFileRdd = sc.textFile( "hdfs://***********:8020/tmp/scLogTest.log")
//    val textFileRdd = sc.textFile( "hdfs://nameservice1/tmp/thappa-test.360che.com.log.json")
//    val textFileRdd = sc.textFile( "hdfs://*************:8020/tmp/thappa-test.360che.com.log.json")
//    val textFileRdd = sc.textFile( "hdfs://*************:8020/tmp/scLogTest.log")
//    textFileRdd.foreach(println(_))
    Common.getBetweenDates("20210514","20210514").foreach(day=>{
      scHistoryClean(day)
    })


  }

  /**
   * 清洗神策app数据，数据最后导入到神策平台集群
   * @param day
   */
  def scHistoryClean(day:String): Unit ={
    val spark=MySparkSession.conn()
    import spark.implicits._
    val dirPath = "hdfs://nameservice1/pv-nginx-appsc/" + day
    val LogRdd = spark.read
      .json(dirPath).filter(t => t.getAs[String]("status") == "200" &&
      t.getAs[String]("request_body") != "-"
      && t.getAs[String]("http_host") == "thappsc.360che.com") //过滤业务日志，参数为"-"
    val resultRDD: RDD[String] =LogRdd.repartition(30).map(line => {
      var list:List[String]=Nil
      try{
        val requestBody = line.getAs[String]("request_body")
        //对发送过来的埋点数据先url解码，再进行base64解码，最后进行解压
        val dataTmp = Common.getPValueByName(requestBody, "data_list")
        val decoder = Base64.getDecoder
        val dataList = GZIPUtils.uncompressToString(decoder.decode(UrlDecodeUtil.urlDecode(dataTmp)))
        val dataJson = JSON.parseArray(dataList)
        if (dataJson != null) {
          for (a <- 0 until dataJson.size()){
            list=list:+ dataJson.getString(a)
          }
        }
      }catch {
        case exception: Exception=>{
          exception.printStackTrace()
        }
      }

      list
    }).flatMap(x=>x).rdd
    resultRDD.saveAsTextFile(s"file:\\H:\\tmp\\sc_log\\$day")

  }


}
