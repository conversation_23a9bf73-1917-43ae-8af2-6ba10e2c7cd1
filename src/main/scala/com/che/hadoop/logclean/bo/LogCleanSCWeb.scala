package com.che.hadoop.logclean.bo
import com.che.hadoop.underlay.dao.{MySparkSession, MyKudu, MySqlBaseDao}
import org.apache.spark.sql.{SaveMode, SparkSession}

/**
  * 神策平台web日志清洗
  */
object LogCleanSCWeb {

  def main(args: Array[String]): Unit = {
    val spark=MySparkSession.conn()
//    webscLogClean(spark,"20220823")
    test()
  }

  def webscLogClean(spark:SparkSession, day:String,dbEnv:String="default."): Unit ={
    val sqlBase=new MySqlBaseDao()
    val resultTable=s"${dbEnv}t_logdata_websc"
    var mysqlDB="db_hitsstatistic."
    var kuduTable="t_logdata_websc_streaming"
    if(dbEnv != "default.") {
      kuduTable = "t_logdata_websc_streaming_test"
      mysqlDB = "db_hitsstatistic_test."
    }
    try {
      val myKudu=new MyKudu(spark)
      myKudu.select(kuduTable).createOrReplaceTempView("t_websc_tmp")
      val webscKudu = spark.sql(
        s"""
           |select res,k,vtc,i,vtf,vtl,case when uid rlike '^[1-9][0-9]*$$' then uid else '' end as uid,
           |vid,sid,ip,ua,daytime,eventid,action,lable,code,user_id,
           |cast(from_unixtime(cast(substr(daytime,0,10) as bigint), 'HH') as int) as hours,province,city,is_first_day,event_duration,platform_type,manufacturer,os,contentId,event,day,element_id,element_content,title,referrer_title,element_position,openid,unionid,type_id,page_title,tag1,tag2,country,recv_time
           |from t_websc_tmp where day='$day'
           |""".stripMargin)
      spark.sql(s"alter table $resultTable drop if exists partition(day='$day')")
      webscKudu.repartition(10).write.format("hive")
        .mode(SaveMode.Append).partitionBy("day").saveAsTable(s"$resultTable")
      sqlBase.executeInsertOrUpdate(s"update ${mysqlDB}t_check set flag=1, message='$day:运行成功' where name='aliyun_t_logdata_websc'",null)
    } catch {
      case e: Exception => {
        e.printStackTrace()
        try{
          sqlBase.executeInsertOrUpdate(s"update ${mysqlDB}t_check set flag=2, message='$day:aliyun_t_logdata_websc 运行失败' where name='aliyun_t_logdata_websc'",null)
        }catch {
          case e:Throwable=>{
            e.printStackTrace()
        }
      }
    }
    }
  }

  def test(): Unit ={
    val spark=MySparkSession.conn()
    spark.sql(
      """
        |select res,k,vtc,i,vtf,vtl,uid,
        |vid,sid,ip,ua,daytime,eventid,`action`,lable,code,user_id,
        |hours,province,city,is_first_day,event_duration,platform_type,manufacturer,os,contentId,event,'' as element_id,'' as element_content,'' as title,'' as referrer_title,'' as element_position,'' as openid,'' as unionid,day
        |from  db_test.t_logdata_websc_test where day>'20220822'
        |""".stripMargin).write.format("hive")
      .mode(SaveMode.Append).partitionBy("day").saveAsTable("db_test.t_logdata_websc_20220829")
    spark.stop()
  }
}
