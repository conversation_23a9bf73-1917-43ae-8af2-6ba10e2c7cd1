package com.che.hadoop.logclean.bo.discard

import com.che.hadoop.underlay.dao.{MySparkSession, MySqlBaseDao}
import com.che.hadoop.logclean.utils.Common

object BaiduEqid {

  def main(args: Array[String]): Unit = {
    Common.getBetweenDates("20211129", "20211129").foreach(eqidPage(_))
  }

  /**
    * 分析日志，提取百度搜素用户数据
    * @param day 计算日期
    */
  def eqidPage(day: String): Unit = {
    val sqlbase=new MySqlBaseDao("bigdata2")
    sqlbase.execute(s"delete from t_eqid_page where day='$day'")
    val ss = MySparkSession.conn("LogClean_BaiduEqidJob")
    val eqidPageSql = s" SELECT vid,uid,ip,code,k as page,SUBSTRING(i,locate('eqid',i)+5,32) eqid,from_unixtime(cast(substring(daytime,0,10) as int),'yyyy-MM-dd HH:mm:ss') as v_datetime,'$day' as day FROM `default`.t_logdata where day='$day' and eventid='' and i like '%eqid%' and length(SUBSTRING(i,locate('eqid',i)+5,32))>=32"
    val saveDF=ss.sql(eqidPageSql)
    sqlbase.saveMysql(saveDF,"t_eqid_page")
    ss.stop()
  }
}
