package com.che.hadoop.logclean.dao

import java.util.ResourceBundle

import com.che.hadoop.underlay.dao.MySparkSession
import org.apache.kudu.Schema
import org.apache.kudu.client.{CreateTableOptions, KuduTable}
import org.apache.kudu.spark.kudu._
import org.apache.spark.sql.DataFrame
import org.apache.spark.sql.types._

import scala.collection.JavaConverters._

object MyKudu {

  private val spark = MySparkSession.conn
  private val kuduMasters = ResourceBundle.getBundle("connection").getString("kudu.master")

  val kuduContext = new KuduContext(kuduMasters, spark.sparkContext)

  def main(args: Array[String]): Unit = {
    //val schema = StructType(Array(StructField("uid", LongType, nullable = false), StructField("id", LongType, nullable = false)))
    //createTable("kudu-test", schema, Seq("uid"))
    //saveAsKudu("kudu-test")
    select("kudu-test").show()
  }

  /**
   * 查询
   * @param kuduTableName 表名
   * @return DataFrame
   */
  def select(kuduTableName: String): DataFrame ={

    //配置kudu参数
    val kuduOptions: Map[String, String] = Map(
      "kudu.table"  -> kuduTableName,
      "kudu.master" -> kuduMasters)
    //读取数据
    spark.read.options(kuduOptions).format("kudu").load()

  }


  /**
   * 将DataFrame追加保存到kudu指定表中
   * @param kuduTableName
   */
  def saveAsKudu(df: DataFrame, kuduTableName: String): Unit ={

    // 配置kudu参数
    val kuduOptions: Map[String, String] = Map(
      "kudu.table"  -> kuduTableName,
      "kudu.master" -> kuduMasters)

    // 执行写入操作
    df.write.options(kuduOptions).mode("append").kudu

  }

  /**
   * 创建表
   * @param tableName   表名
   * @param tableSchema 建表信息
   * @return KuduTable
   */
  def createTable(tableName: String, tableSchema: Schema): KuduTable ={

    // Delete the table if it already exists.
    if(kuduContext.tableExists(tableName)) kuduContext.deleteTable(tableName)
    kuduContext.createTable(tableName, tableSchema, new CreateTableOptions())

  }

  /**
   * 创建带主键的表
   * @param tableName   表名
   * @param tableSchema 建表信息
   * @return KuduTable
   */
  def createTable(tableName: String, tableSchema: StructType, primarykeys: Seq[String]): KuduTable ={

    // Delete the table if it already exists.
    if(kuduContext.tableExists(tableName)) kuduContext.deleteTable(tableName)
    kuduContext.createTable(tableName, tableSchema,
      primarykeys,
      new CreateTableOptions().addHashPartitions(primarykeys.asJava, 3))

  }



  /**
   * 插入
   * @param df
   * @param tableName
   * @param writeOptions
   */
  def insert(df: DataFrame, tableName: String, writeOptions: KuduWriteOptions = new KuduWriteOptions): Unit ={
    kuduContext.insertRows(df, tableName, writeOptions)
  }

  /**
   * 删除
   * @param df
   * @param tableName
   * @param writeOptions
   */
  def delete(df: DataFrame, tableName: String, writeOptions: KuduWriteOptions = new KuduWriteOptions): Unit ={
    kuduContext.deleteRows(df, tableName, writeOptions)
  }

  /**
   * 更新
   * @param df
   * @param tableName
   * @param writeOptions
   */
  def update(df: DataFrame, tableName: String, writeOptions: KuduWriteOptions = new KuduWriteOptions): Unit ={
    kuduContext.updateRows(df, tableName, writeOptions)
  }


  /**
   * 更新
   * @param df
   * @param tableName
   * @param writeOptions
   */
  def upsert(df: DataFrame, tableName: String, writeOptions: KuduWriteOptions = KuduWriteOptions()): Unit ={
    kuduContext.upsertRows(df, tableName, writeOptions)
  }

  /**
   * 删除表
   * @param tableName   表名
   * @return KuduTable
   */
  def deleteTable(tableName: String): Unit ={

    // Delete the table if it already exists.
    if(kuduContext.tableExists(tableName)) kuduContext.deleteTable(tableName)
  }
}
