package com.che.hadoop.logclean.utils;

/**
 * @Desc:url预转码，解决url中包含普通字符%造成的解码异常(URLDecoder.decode(url,"utf8")--URLDecoder: Incomplete trailing escape (%))
 * <AUTHOR>
 * @Date:2019-11-22
 */

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
public class ConverPercent {

    //判断是否为16进制数
    public static boolean isHex(char c) {
        if (((c >= '0') && (c <= '9')) ||
                ((c >= 'a') && (c <= 'f')) ||
                ((c >= 'A') && (c <= 'F')))
            return true;
        else
            return false;
    }

    public static String convertPercent(String str) {
        StringBuilder sb = new StringBuilder(str);

        for (int i = 0; i < sb.length(); i++) {
            char c = sb.charAt(i);
            //判断是否为转码符号%
            if (c == '%') {
                if (((i + 1) < sb.length()) && ((i + 2) < sb.length())) {
                    char first = sb.charAt(i + 1);
                    char second = sb.charAt(i + 2);
                    //如只是普通的%则转为%25
                    if (!(isHex(first) && isHex(second)))
                        sb.insert(i + 1, "25");
                } else {//如只是普通的%则转为%25
                    sb.insert(i + 1, "25");
                }
            }
        }
        return sb.toString();
    }

    public static void main(String[] args) throws UnsupportedEncodingException {
        String test="https%253A%252F%252Fsaasm.360che.com.cn%252Fwxnew%252Findex.html%253Fv318%2523%252Fhome";
//        URLDecoder.decode(test, "utf8");//报错java.lang.IllegalArgumentException: URLDecoder: Incomplete trailing escape (%)
//        String url = convertPercent(test);
        System.out.println(test);//http://www.baidu.com?%e4%b8%ad%e5%9b%bd123%25
        System.out.println(URLDecoder.decode(test,"utf8"));//http://www.baidu.com?中国123%
        System.out.println(URLDecoder.decode(URLDecoder.decode(test,"utf8"),"utf8"));

    }
}
