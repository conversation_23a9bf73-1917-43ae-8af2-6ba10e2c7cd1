package com.che.hadoop.logclean.bo.discard

import org.apache.http.client.entity.UrlEncodedFormEntity
import org.apache.http.client.methods.{HttpGet, HttpPost}
import org.apache.http.impl.client.{DefaultHttpClient => HttpClients}
import org.apache.http.message.BasicNameValuePair
import org.apache.http.util.EntityUtils

/**
  Http请求
  */

object MyHttpService {
  //get请求
  def get(url: String): String = {
    var strData:String=""
    val httpclient = new HttpClients
    val httpGet = new HttpGet(url)
    val response = httpclient.execute(httpGet)
    try {
      System.out.println(response.getStatusLine)
      val entity = response.getEntity
      strData=  EntityUtils.toString(entity,"utf-8")
      EntityUtils.consume(entity)
    } finally {
      //      response.close()
    }

    return  strData
  }

  def post(url: String, paramMap: Map[String,String],tokenValue:String="",contentTypeValue:String=""): String = {
    //创建httpclient对象
    val client = new HttpClients
    try {
      //创建post方式请求对象
      val httpPost = new HttpPost(url)
      if(tokenValue!=""){
        httpPost.setHeader("token", tokenValue);
      }
      if(contentTypeValue!=""){
        httpPost.setHeader("Content-Type", contentTypeValue);
      }
      //装填参数
      val nvps:java.util.ArrayList[BasicNameValuePair] = new java.util.ArrayList[BasicNameValuePair]
      paramMap.map(entrySet=>{
        nvps.add(new BasicNameValuePair(entrySet._1, entrySet._2))
      })
      //设置参数到请求对象中
      httpPost.setEntity(new UrlEncodedFormEntity(nvps, "UTF-8"))
      //执行请求操作，并拿到结果（同步阻塞）
      val response = client.execute(httpPost)
      //获取结果实体
      val entity = response.getEntity
      var body = ""
      if (entity != null) { //按指定编码转换结果实体为String类型
        body = EntityUtils.toString(entity, "UTF-8")
      }
      //释放链接
//      response.close()
      body
    } finally {
//      client.close()
    }
  }
}
