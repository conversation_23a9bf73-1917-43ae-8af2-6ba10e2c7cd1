package com.che.hadoop.logclean.activity.discard

import com.che.hadoop.logclean.bo.LogCleanMiniProgram
import com.che.hadoop.underlay.dao.MySparkSession
import com.che.hadoop.logclean.utils.Common
import org.apache.spark.sql.SparkSession

/**
 * 清洗司机招聘小程序基础数据
 */
object LogCleanMiniProgramJob {
  def main(args: Array[String]): Unit = {
    var day=Common.getDate(1)
    if(args(0)!="0"){
      day = args(1)
    }
    val spark = MySparkSession.conn("LogClean_LogCleanMiniProgram")
    LogCleanMiniProgram.cleanMiniProgramLog(spark:SparkSession,day)
    spark.stop()
    //      LogCleanMiniProgram.delKuduLog(day)
    //埋点已切换神策平台，任务停止
    //      LogCleanKSMiniProgram.cleanKSMiniProgramLog(day)
  }

}
