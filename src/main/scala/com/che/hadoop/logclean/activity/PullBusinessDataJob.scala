package com.che.hadoop.logclean.activity

import com.che.hadoop.logclean.bo.{FetchLogData, FetchThbalogData, PullBBSPost, PullBusinessData, PullCommentData, PullDiscountOil, PullDriverJobData, PullHelpData, PullInquiryClues, PullKyqData, PullUserInfo, TopicPull, pullDealerProduct}
import com.che.hadoop.underlay.dao.{MySparkSession, MySqlBaseDao}
import com.che.hadoop.underlay.tool.date.DateTranslate

/**
 * 业务数据采集，包含ba业务数据和通过直接读取外部数据库采集的数据(ods层：原始数据，dwd层业务清洗数据，dws层，关联其它数据宽表)
 * 1>业务日志采集
 * 违章查询数据 t_illegal_query
 * 违章查询 车型认证数据  t_car_certification
 * 优惠加油 db_hitsstatistic.t_discount_oil(mysql)
 * 01帖子帖子打标签 t_ods_post_tag
 * 02发帖数据 t_ods_bbs_post
 * 03帖子加精数据 t_ods_post_excellent t_dwd_bbs_post,t_dws_bbs_post
 * 孵化失败 需要取消，司机招聘 t_driver_info，t_dwd_driver_info
 * 客服系统-用户基本信息，资讯信息 司机招聘 发布职位数据表 t_ods_kf_user_info，司机招聘 求职数据表 t_ods_kf_ask_record
 * 2>外部数据据采集
 * 经销商关联车型信息 t_ods_dealer_product （从sqlserver读取）
 */
object PullBusinessDataJob {
  def main(args: Array[String]): Unit = {
    var startDay,endDay=DateTranslate.getDate(1)
    var dbEnv="default."
    if(args.length>=1){
      dbEnv=args(0)
      if(args.length==2){
        startDay=args(1)
        endDay=startDay
      }
      if(args.length==3){
        startDay=args(1)
        endDay=args(2)
      }
    }
    val spark = MySparkSession.conn()
    val sqlBase=new MySqlBaseDao()
    DateTranslate.getDatesBetween(startDay,endDay).foreach(day=>{
      println(s"计算日期：$day")
      FetchThbalogData.cleanThbaLog(spark,day)
      if(dbEnv=="default."){
        spark.sql(
          s"""
             |select data, billtype,timestamp,operation_state,env from t_ods_thba_logdata
             |where day='$day' and (env != 'test' or env is null)
             |""".stripMargin).createOrReplaceTempView("t_biz_data_temp")
      }else{
        spark.sql(
          s"""
             |select data, billtype,timestamp,operation_state,env from t_ods_thba_logdata
             |where day='$day'
             |""".stripMargin).createOrReplaceTempView("t_biz_data_temp")
      }
      spark.catalog.cacheTable("t_biz_data_temp")
      /*01业务数据*/
      //    配件 t_accessory 牛人标签 t_stg_superman_label 用户关注 t_dwd_superman_user_focus 论坛主贴 t_bbs_post 论坛回帖 t_stg_recommend_bbs_post_reply
      //    文章 - db_userportrait.t_article(mysql)
      //todo 论坛主帖  report工程下报表数据切换后停用
      FetchLogData.go(spark,dbEnv, day, true, true, true,true,true,true,true,  false)
      //优惠加油数据采集 https://360che.yuque.com/technology_team/rof05c/158171193
      PullDiscountOil.saveData(spark,day)
      //帖子数据，语雀https://360che.yuque.com/technology_team/rof05c/197623986
      //todo 报表数据切换后pullPostToODSOld方法作废
      PullBBSPost.pullPostToODSOld(spark,day)
      PullBBSPost.pullPostToODS(spark,day)
      PullBBSPost.pullPostToDWD(spark,day)
      PullBBSPost.pullPostToDWS(spark,day)
      //论坛圈子
      PullBBSPost.getBBSCircle(spark,sqlBase,day)
      //客服系统-用户基本信息，资讯信息，语雀https://360che.yuque.com/technology_team/rof05c/qzfs22
      PullBusinessData.kfSystem(spark,day)
      //文章视频评论数据，语雀地址https://360che.yuque.com/technology_team/rof05c/158171193
      PullCommentData.saveOdsData(spark,day)
      PullCommentData.saveDwdData(spark,day)
      //清洗求助数据，语雀地址https://360che.yuque.com/technology_team/rof05c/194642150
      PullHelpData.cleanOdsData(spark,day,dbEnv)
      PullHelpData.saveHelpDetail(spark,day,dbEnv)
      PullHelpData.saveForHelpDiscussDetail(spark,day)
      //卡友圈数据，语雀地址https://360che.yuque.com/technology_team/rof05c/mn5iuo
      PullKyqData.pullKyq(spark,day)
      //图集数据,语雀未找到文档
      PullKyqData.pullPicture(spark,day)
      //资讯栏目数据，语雀地址https://360che.yuque.com/technology_team/rof05c/ykzkm7
      //pullSection没有按天分区，重复执行数据会重复
      PullKyqData.pullSection(spark,day)
      //口碑数据，语雀地址https://360che.yuque.com/technology_team/rof05c/lwdfhl
      PullKyqData.pullMouth(spark,day)
      //用户数据采集，语雀地址https://360che.yuque.com/technology_team/rof05c/scsgqz
      //todo *pullRegisterUser没有按天分区，重复执行数据会重复
      PullUserInfo.start(spark,sqlBase,day)
      //拉取专题数据(迁移阿里云后上线)
      TopicPull.topicPull(spark,day)
      // 大车试订单，车型收藏，用户购车周期
      PullInquiryClues.getTruckMarketOrders(spark,sqlBase,day)
      PullInquiryClues.getFavoriteTruck(spark,sqlBase,day)
      PullInquiryClues.getDealCycle(spark,sqlBase,day)
      //司机招聘发布职位的数据
      PullDriverJobData.getRecruitPublish(spark,sqlBase,day)
      /*02从其它数据源拉取数据*/
      //同步用户互动数据（论坛，文章，视频，求助点赞收藏,订阅,分享）
      PullBusinessData.pullContentIteract(spark,sqlBase,day)
      //同步用户关注数据
      PullBusinessData.pullUserFocus(spark, sqlBase, day)
      //将400电话语音解析成文字内容，并且通过大模型解析子类车系和场景
      PullBusinessData.saveProduct400Dwd(spark,sqlBase,day)
      spark.catalog.uncacheTable("t_biz_data_temp")
    })
    spark.stop()
  }
}
