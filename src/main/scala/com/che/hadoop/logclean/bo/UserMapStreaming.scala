package com.che.hadoop.logclean.bo

import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

import com.che.hadoop.underlay.dao.{MyKudu, MySparkSession, MySqlBaseDao}
import org.apache.spark.sql.SparkSession

/**
  * 计算用户vid和uid的映射数据，伪实时
* */
object UserMapStreaming {
    def main(args: Array[String]): Unit = {
      val spark=MySparkSession.conn()
      val sqlBase=new MySqlBaseDao()
      test(spark,sqlBase)
    }


  /**
   *
   * @param spark spark实例
   * @param exec mysql实例
   */
  def cookieUserStreaming(spark:SparkSession,exec:MySqlBaseDao): Unit = {
    while (true){
      try {
        //1、从mysql中获取最大时间戳,读取kudu数据并创建临时表
        val maxTimestamp = exec.executeQuery("select max(max_timestamp) as timestamp from t_max_timestamp where data_name='vid_uid_streaming_ali2'", null)
        maxTimestamp.next()
        val time = maxTimestamp.getString("timestamp")
        val myKudu=new MyKudu(spark)
        myKudu.select("t_logdata_websc_streaming").createOrReplaceTempView("t_logdata_websc_view")
        myKudu.select("t_logdata_appsc_streaming2").createOrReplaceTempView("t_logdata_appsc_view")
        myKudu.select("t_logdata_mpsc_streaming").createOrReplaceTempView("t_logdata_mpsc_view")
        exec.getCacheTable("t_recruit_publish_view",
          s"""
             |(select vid,uid,date_format(create_time,"%Y%m%d") as updatetime,cast(unix_timestamp(create_time) as SIGNED) as ts,'Business' as cs
             |from db_userportrait.t_recruit_publish where uid is not null and uid <> '' AND date_format(create_time,'%Y%m%d')=FROM_UNIXTIME($time,'%Y%m%d')
             |)as t
             |""".stripMargin,spark)
        exec.getCacheTable("t_inquiry_clue",
          """
            |(
            |select vid,create_date,tel,day from db_hitsstatistic.t_clues_realtime
            |)t
            |""".stripMargin,spark)
        //询价用户关联
        spark.sql(
          s"""
             |select if(vid='',sjyz,vid) as vid ,b.uid,date_format(create_date,"yyyyMMdd") as updatetime,unix_timestamp(create_date , 'yyyy-MM-dd HH:mm:ss')as ts,'Clues' as cs
             |from
             |(SELECT * from t_inquiry_clue where day>=FROM_UNIXTIME($time/1000,'yyyyMMdd')) as a
             |INNER JOIN t_user as b on base64(a.tel)=b.sjyz
             |""".stripMargin).createOrReplaceTempView("t_clues_view")
        //2、根据数据源划分并聚合得到需要更新的数据
        val resultDF=spark.sql(
          s"""
             |select vid,uid,cs,max(updatetime) as updatetime,max(ts) as ts from (
             |-- 神策web
             |select vid,uid,day as updatetime, vtc as ts,'bbs' as cs
             |from t_logdata_websc_view where day>=FROM_UNIXTIME($time/1000,'yyyyMMdd')
             |and daytime>='$time' and uid rlike '^[1-9][0-9]*$$'
             |and vid rlike '^[0-9]*$$'
             |-- 神策app
             |union all
             |SELECT cid as vid,uid,day as updatetime ,substr(`time`,0,10) as ts,if(os='iOS','iOS','android') as cs
             |from t_logdata_appsc_view where day>=FROM_UNIXTIME($time/1000,'yyyyMMdd')
             |and `time`>='$time' and uid rlike '^[1-9][0-9]*$$' and cid <>''
             |-- 小程序
             |union all
             |SELECT anonymous_id as vid,uid,day as updatetime,substr(`time`,0,10) as ts,'MiniProgram' as cs
             |from t_logdata_mpsc_view where day>=FROM_UNIXTIME($time/1000,'yyyyMMdd')
             |and `time`>='$time' and uid rlike '^[1-9][0-9]*$$' and anonymous_id <>''
             |-- 司机招聘业务数据
             |union all
             |select vid,uid,updatetime,ts,cs from t_recruit_publish_view
             |--询价数据
             |union all
             |select vid,uid,updatetime,ts,cs from t_clues_view
             |) as a
             |group by vid,uid,cs
             |""".stripMargin)
        resultDF.cache()
        //3、upsert方式写进kudu表中（t_vid_uid_streaming）
        myKudu.upsert(resultDF,"t_vid_uid_streaming")
        //4、获取更新数据的最大时间戳，为下次计算做准备(后退3分钟)
        resultDF.createOrReplaceTempView("t_result")
        val newTime = (spark.sql(
          """
            |select cs,max(ts) as ts from t_result group by cs order by ts limit 1
            |""".stripMargin).collect()(0).getAs[String]("ts").toLong - 3*60).toString+"000"
        exec.execute(s"update t_max_timestamp set max_timestamp=$newTime,day=FROM_UNIXTIME($newTime/1000,'%Y%m%d') where data_name='vid_uid_streaming_ali2'")
      }catch {
        case e:Exception =>{
          e.printStackTrace()
        }
      }
      }
     }

  def test(spark:SparkSession,exec:MySqlBaseDao): Unit ={
    val myKudu=new MyKudu(spark)
    val resultDF=spark.sql(
      """
        |select vid,uid,cast(updatetime as String) as updatetime,ts,cs from t_vid_uid
        |""".stripMargin)
    myKudu.upsert(resultDF,"t_vid_uid_streaming")
  }



}
