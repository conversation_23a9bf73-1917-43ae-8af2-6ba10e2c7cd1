package com.che.hadoop.logclean.utils

import java.io.{ByteArrayInputStream, ByteArrayOutputStream}
import java.text.SimpleDateFormat
import java.util
import java.util.{Calendar, Collections, Date, UUID}

import scala.collection.mutable.ListBuffer
import javax.crypto.Mac
import javax.crypto.spec.SecretKeySpec
import org.apache.commons.codec.binary.Base64

import scala.util.matching.Regex

object Common {
  def main(args: Array[String]): Unit = {
    println(getCurrentTimestamp("20200713", length = 13))
  }

  /*得到当前日期前一天*/
  def getDate(day: Int): String = {
    val dateFormat: SimpleDateFormat = new SimpleDateFormat("yyyyMMdd")
    var cal: Calendar = Calendar.getInstance()
    cal.add(Calendar.DATE, -day)
    val date = dateFormat.format(cal.getTime())
    return date

  }
  /**
   * 转换日期格式
   * @param strdate
   * @param inputPattern
   * @param outputPattern
   * @return 指定格式的日期
   */
  def transFormat(strdate: String, inputPattern: String, outputPattern: String): String = {
    val dateFormatInput: SimpleDateFormat = new SimpleDateFormat(inputPattern)
    val dateFormatOutPut: SimpleDateFormat = new SimpleDateFormat(outputPattern)
    val cal: Calendar = Calendar.getInstance()
    cal.setTime(dateFormatInput.parse(strdate))
    cal.add(Calendar.DATE, 0)
    val date = dateFormatOutPut.format(cal.getTime())
    return date
  }

  /**
   * 获取指定日期的前n天
   * @param specify 指定日期
   * @param before 前N天
   * @return
   */
  def getSpecifyDate(specify:String, before:Int): String ={
    val dateFormat: SimpleDateFormat = new SimpleDateFormat("yyyyMMdd")
    var cal: Calendar = Calendar.getInstance()
    cal.setTime(dateFormat.parse(specify))
    cal.add(Calendar.DATE, -before)
    dateFormat.format(cal.getTime())
  }

  /*得到当前日期前一天yyyy-MM-dd格式*/
  def getShortDateTime(day: Int): String = {
    var dateFormat: SimpleDateFormat = new SimpleDateFormat("yyyy-MM-dd")
    var cal: Calendar = Calendar.getInstance()
    cal.add(Calendar.DATE, -day)
    var date = dateFormat.format(cal.getTime())
    return date
  }

  def getUUID(a: String = ""): String = {
    val hashCodeV = UUID.randomUUID().toString.replace("-", "")
    //    val string = hashCodeV formatted  "%016d"
    hashCodeV
  }


  def getUnionKeyByTime(timestamp: String): Long = { //随机生成一位整数
    val value = ((Math.random() * 9 + 1) * 100000).toInt.toString
    (value + timestamp).toLong
  }

  def getCurrentTimestamp(day: String = "", length: Int = 10): Long = {
    synchronized {
      var now = new Date()
      if (day != "") {
        val dateFormat: SimpleDateFormat = new SimpleDateFormat("yyyyMMdd")
        now = dateFormat.parse(day)
      }
      val a = now.getTime
      val str = a + ""
      str.substring(0, length).toLong
    }

  }

  /*
时间戳转换为日期类型
*/
  def tranTimestamp(tm: String, format: String = "yyyyMMdd"): String = {
    val fm = new SimpleDateFormat(format)
    var newTm = tm
    if (tm.length == 10) {
      newTm = tm + "000"
    }
    val tim = fm.format(new Date(newTm.toLong))
    tim
  }


  //返回参数值
  def getPValueByName(args: String,value: String): String = {
    var str:String=""
    val a1 =args
    val p1 = ("""(\?|\&)?"""+value+"""=([^\&]+)""").r
    val arg=(p1 findAllIn a1 toList)
    if(arg.length>0&&arg(0).split("=").length>1)
    {
      str= arg(0).split("=")(1)
    }
     str
  }

  //返回res参数值
  def getResByName(args: String, value: String): String = {
    var str: String = ""
    val a1 = args
    val p1 = ("""(\\?|\\&)""" + value + """=([^\\&]+)""").r
    val arg = (p1 findAllIn a1 toList)
    if (arg.length > 0 && arg(0).split("=").length > 1) {
      str = arg(0).split("=")(1)
    }
     str
  }

  /**
   * 获取两个时间之间的所有日期
   * @param start 开始时间
   * @param end 结束时间
   * @return 返回日期列表
   */
  def getBetweenDates(start: String, end: String):List[String] = {
    val startData = new SimpleDateFormat("yyyyMMdd").parse(start); //定义起始日期
    val endData = new SimpleDateFormat("yyyyMMdd").parse(end); //定义结束日期

    val dateFormat: SimpleDateFormat = new SimpleDateFormat("yyyyMMdd")
    var buffer = new ListBuffer[String]
    buffer += dateFormat.format(startData.getTime())
    if(start==end){return buffer.toList}
    val tempStart = Calendar.getInstance()

    tempStart.setTime(startData)
    tempStart.add(Calendar.DAY_OF_YEAR, 1)

    val tempEnd = Calendar.getInstance()
    tempEnd.setTime(endData)
    while (tempStart.before(tempEnd)) {
      // result.add(dateFormat.format(tempStart.getTime()))
      buffer += dateFormat.format(tempStart.getTime())
      tempStart.add(Calendar.DAY_OF_YEAR, 1)
    }
    buffer += dateFormat.format(endData.getTime())
    buffer.toList
  }


  //字符串转时间戳
  def tranTimeToLong(timeStr:String,pattern:String="yyyyMMdd") :Long={
    new SimpleDateFormat(pattern).parse(timeStr).getTime()/1000
  }

  /**
   * 加密算法签名
   */
  def sign(message:String): String ={
    //测试环境
//    val secret="job_server_t!d2adb5@e"
    //正式环境
    val secret="job_server_p!c3oan@e"
//    val message=java.net.URLEncoder.encode(message,"utf-8")
    val sha256_HMAC=Mac.getInstance("HmacSHA256")
    val secretKey =new SecretKeySpec(secret.getBytes(), "HmacSHA256")
    sha256_HMAC.init(secretKey)
    Base64.encodeBase64String(sha256_HMAC.doFinal(message.getBytes()))
  }

  /**
   * 根据UA获取客户端类型
   *
   * @param ua user-agent
   * @return
   */
  def getClientByUa(ua: String): String = {
    var client = ""
    if (ua.contains("Windows") || ua.contains("Macintosh"))
      client = "pc"
    else if (ua.contains("MicroMessenger"))
      client = "wx"
    else if (!ua.contains("MicroMessenger") && !ua.contains("360CHE") && !ua.contains("Windows") && !ua.contains("Macintosh"))
      client = "m"
    else if (ua.contains("iPhone") || ua.contains("iPad")) {
      client = "iOS_c"
      if (ua.contains("360CHESPEED") && !ua.contains("STANDARDAPP")) {
        client = "iOS_s"
      }
    } else if (ua.contains("Android")) {
      client = "android_c"
      if (ua.contains("360CHESPEED") && !ua.contains("STANDARDAPP")) {
        client = "android_s"
      }
    }
    client
  }

  /*正则判断匹配*/
  def GetRegular(value:String,regular:String):Boolean=
  {
    var k=value.toString
    val pattern = new Regex(regular)
    if((pattern findAllIn value.toString()).mkString(",")!="")
      return true
    else
      return false
  }
}
