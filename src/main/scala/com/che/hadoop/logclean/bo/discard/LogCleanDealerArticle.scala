package com.che.hadoop.logclean.bo.discard

import com.che.hadoop.underlay.dao.{MySparkSession, MySqlBaseDao}
import com.che.hadoop.logclean.utils.UrlDecodeUtil
import com.sun.jndi.toolkit.url.UrlUtil

/**
 * 将经销商文章id清洗到MySQL
 * 已废弃
  *
 */
object LogCleanDealerArticle {

  def main(args: Array[String]): Unit = {
    etlDealerArticle("20200501")
  }

  def etlDealerArticle(day: String): Unit ={

    val logPath = "hdfs://nameservice1/pv-nginx-app/"+day
    val billType = "article_t_article"

    val spark = MySparkSession.conn("Recommend_DealerArticleJob")

    val exec = new MySqlBaseDao()

    spark.udf.register("urlDecode", UrlDecodeUtil.urlDecode(_:String))
    val logDF = spark.read.json(logPath).filter("status=200 and http_host='thba.360che.com'").selectExpr("urlDecode(request_body) as request_body")

    // 获取经销商文章id
    val aidDf =logDF.selectExpr("get_json_object(request_body, '$.billType') as billType", "get_json_object(get_json_object(request_body, '$.data'), '$.id') as article_id", "get_json_object(get_json_object(request_body, '$.data'), '$.isDealer') as is_dealer")
                    .filter(s"billType='$billType' and is_dealer=1")
                    .select("article_id")
                    .distinct()
    /*
    val aidDf = Seq(
      (1),F
      (53997)
    ).toDF("article_id")*/

    // 保存到MySQL
    try{
      exec.saveMysql(aidDf, "db_userportrait.t_dealer_article")
    }catch {
      case e: Exception =>
        val aids = aidDf.collect().map(_.get(0)).mkString(",")
        println("经销商文章id: " + aids)
        e.printStackTrace()
    }

    spark.stop()

  }

}
