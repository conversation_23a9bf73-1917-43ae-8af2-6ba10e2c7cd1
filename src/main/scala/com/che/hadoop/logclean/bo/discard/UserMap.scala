package com.che.hadoop.logclean.bo.discard

import com.che.hadoop.underlay.dao.{MySparkSession, MySqlBaseDao}

/**
  * cookies用户唯一标识映射
* */
object UserMap {
    def main(args: Array[String]): Unit = {

    }

  /**
    * cookies映射userid
    **/
  def cookieUser(day:String): Unit = {
    val spark= MySparkSession.conn("Report_UserMapJob")
    val exec = new MySqlBaseDao()
    try {
      spark.conf.set("spark.sql.shuffle.partitions","10")

      val vidtableName="t_vid" //
      val webtableName="t_logdata" //web日志
      val apptableName="t_logdata_app" //app日志

      val vtfTime="from_unixtime(cast(vtf as bigint),'yyyyMMdd')" //第一次访问时间
      val vtlTime="from_unixtime(cast(vtl as bigint),'yyyyMMdd')" //上一次访问时间

      //写入vid用户
      val sqlVid = "insert overwrite table "+vidtableName+" PARTITION(cs='bbs') select coalesce(a.vid,b.vid) as vid" +
        ",coalesce(a.updatetime,b.updatetime) as updatetime" +
        ",coalesce(b.firsttime,a.firsttime) as firsttime" +
        ",coalesce(a.lasttime,b.lasttime) as lasttime" +
        " from (select vid,updatetime,firsttime,lasttime" +
        " from (select row_number() over (partition by vid order by "+vtfTime+","+vtlTime+") as num" +
        ",vid,'"+day+"' as updatetime, "+vtfTime+" as firsttime,"+vtlTime+" as lasttime " +
        " from "+webtableName+"  where day=" + day + " and length(regexp_replace(vid,'-',''))<32 group by vid,"+vtfTime+","+vtlTime+")" +
        " as webData where num=1) a " +
        " full outer join (select vid,updatetime,firsttime" +
        ",lasttime  from "+vidtableName+" where cs='bbs' ) b on a.vid=b.vid"
      //      println(sqlVid)
      spark.sql(sqlVid)

      //写入android用户
      val sqlAndroid = "insert overwrite table "+vidtableName+" PARTITION(cs='android') select coalesce(a.vid,b.vid) as vid" +
        ",coalesce(a.updatetime,b.updatetime) as updatetime" +
        ",coalesce(b.firsttime,a.firsttime) as firsttime" +
        ",coalesce(b.updatetime,a.updatetime) as lasttime from " +
        "(select cid as vid,'"+day+"' as updatetime,"+day+" as firsttime,"+day+" as lasttime from "+apptableName+"  where day=" + day + " and cs='android' and length(regexp_replace(cid,'-',''))=32 group by cid) a " +
        "full outer join (select vid,updatetime,firsttime,lasttime from "+vidtableName+" where cs='android' ) b on a.vid=b.vid"
      spark.sql(sqlAndroid)

      //写入iOS用户
      val sqlIos = "insert overwrite table "+vidtableName+" PARTITION(cs='iOS') select coalesce(a.vid,b.vid) as vid" +
        ",coalesce(a.updatetime,b.updatetime) as updatetime" +
        ",coalesce(b.firsttime,a.firsttime) as firsttime" +
        ",coalesce(b.updatetime,a.updatetime) as lasttime from " +
        "(select cid as vid,'"+day+"' as updatetime,"+day+" as firsttime,"+day+" as lasttime from "+apptableName+"   where day=" + day + " and cs='iOS' and length(regexp_replace(cid,'-',''))=32 group by cid) a " +
        "full outer join (select vid,updatetime,firsttime,lasttime from "+vidtableName+" where cs='iOS' ) b on a.vid=b.vid"
      spark.sql(sqlIos)



      //写入android标准用户
      val sqlAndroid_c = "insert overwrite table "+vidtableName+" PARTITION(cs='android_c') select coalesce(a.vid,b.vid) as vid" +
        ",coalesce(a.updatetime,b.updatetime) as updatetime" +
        ",coalesce(b.firsttime,a.firsttime) as firsttime" +
        ",coalesce(b.updatetime,a.updatetime) as lasttime from " +
        "(select cid as vid,'"+day+"' as updatetime,"+day+" as firsttime,"+day+" as lasttime from "+apptableName+"  where day=" + day + " and cs='android' and (appid!='ches' or appid is NULL) and length(regexp_replace(cid,'-',''))=32 group by cid) a " +
        "full outer join (select vid,updatetime,firsttime,lasttime from "+vidtableName+" where cs='android_c' ) b on a.vid=b.vid"
      spark.sql(sqlAndroid_c)


      //写入iOS用户标准用户
      val sqlIos_c = "insert overwrite table "+vidtableName+" PARTITION(cs='iOS_c') select coalesce(a.vid,b.vid) as vid" +
        ",coalesce(a.updatetime,b.updatetime) as updatetime" +
        ",coalesce(b.firsttime,a.firsttime) as firsttime" +
        ",coalesce(b.updatetime,a.updatetime) as lasttime from " +
        "(select cid as vid,'"+day+"' as updatetime,"+day+" as firsttime,"+day+" as lasttime from "+apptableName+"   " +
        "where day=" + day + " and cs='iOS' and (appid!='ches' or appid is NULL) and length(regexp_replace(cid,'-',''))=32 group by cid) a " +
        "full outer join (select vid,updatetime,firsttime,lasttime from "+vidtableName+" where cs='iOS_c' ) b on a.vid=b.vid"
      spark.sql(sqlIos_c)



      //写入android极速用户
      val sqlAndroid_s = "insert overwrite table "+vidtableName+" PARTITION(cs='android_s') select coalesce(a.vid,b.vid) as vid" +
        ",coalesce(a.updatetime,b.updatetime) as updatetime" +
        ",coalesce(b.firsttime,a.firsttime) as firsttime" +
        ",coalesce(b.updatetime,a.updatetime) as lasttime from " +
        "(select cid as vid,'"+day+"' as updatetime,"+day+" as firsttime,"+day+" as lasttime from "+apptableName+
        " where day=" + day + " and cs='android' and appid='ches' and length(regexp_replace(cid,'-',''))=32 group by cid) a " +
        "full outer join (select vid,updatetime,firsttime,lasttime from "+vidtableName+" where cs='android_s' ) b on a.vid=b.vid"
      spark.sql(sqlAndroid_s)


      //写入iOS用户极速用户
      val sqlIos_s = "insert overwrite table "+vidtableName+" PARTITION(cs='iOS_s') select coalesce(a.vid,b.vid) as vid" +
        ",coalesce(a.updatetime,b.updatetime) as updatetime" +
        ",coalesce(b.firsttime,a.firsttime) as firsttime" +
        ",coalesce(b.updatetime,a.updatetime) as lasttime from " +
        "(select cid as vid,'"+day+"' as updatetime,"+day+" as firsttime,"+day+" as lasttime from "+apptableName+
        "  where day=" + day + " and cs='iOS' and appid='ches' and length(regexp_replace(cid,'-',''))=32 group by cid) a " +
        "full outer join (select vid,updatetime,firsttime,lasttime from "+vidtableName+" where cs='iOS_s' ) b on a.vid=b.vid"
      spark.sql(sqlIos_s)


      //写入vid映射uid用户
      val sql = "insert overwrite table t_vid_uid PARTITION(cs='bbs')select vid,uid,max(updatetime) as updatetime,max(ts) as ts from" +
        "(select coalesce(a.vid,b.vid) as vid,coalesce(a.uid,b.uid) as uid,coalesce(a.updatetime,b.updatetime) as updatetime,coalesce(a.ts,b.ts) as ts from " +
        "(select vid,uid,'"+day+"' as updatetime,max(vtc) as ts from t_logdata where day=" + day + " and uid!='' and uid!=0 and length(regexp_replace(vid,'-',''))<32 group by vid,uid) a " +
        "full outer join (select vid,uid,updatetime,ts from t_vid_uid where cs='bbs' ) b on a.vid=b.vid and a.uid=b.uid) where vid!='' and uid !='' and uid !='deleted' and uid!='null' group by vid,uid"
      spark.sql(sql)

      //写入vid映射uid用户(dealersid)
      val sql_dealers = "insert overwrite table t_vid_uid PARTITION(cs='dealersid') select vid,uid,max(updatetime) as updatetime,max(ts) as ts from" +
        "(select coalesce(a.vid,b.vid) as vid,coalesce(a.uid,b.uid) as uid,coalesce(a.updatetime,b.updatetime) as updatetime,coalesce(a.ts,b.ts) as ts from " +
        "(select vid,dealers_uid as uid,'"+day+"' as updatetime,max(vtc) as ts from t_logdata where day=" + day + " and dealers_uid!='' group by vid,dealers_uid) a " +
        "full outer join (select vid,uid,updatetime,ts from t_vid_uid where cs='dealersid' ) b on a.vid=b.vid and a.uid=b.uid) where vid!='' and uid !='' and uid!='null' group by vid,uid"
      spark.sql(sql_dealers)

      //写入vid映射uid用户(android)
      val sql_android = "insert overwrite table t_vid_uid PARTITION(cs='android') select vid,uid,max(updatetime) as updatetime,max(ts) as ts from " +
        "(select coalesce(a.vid,b.vid) as vid,coalesce(a.uid,b.uid) as uid,coalesce(a.updatetime,b.updatetime) as updatetime,coalesce(a.ts,b.ts) as ts from " +
        "(select cid as vid,uid,'"+day+"' as updatetime,max(time)as ts from t_logdata_app where day=" + day + " and uid!='' and cs='android' and length(regexp_replace(cid,'-',''))=32 group by cid,uid) a " +
        "full outer join (select vid,uid,updatetime,ts from t_vid_uid where cs='android' ) b on a.vid=b.vid and a.uid=b.uid) where vid!='' and uid !='' and uid!='null' group by vid,uid"
      spark.sql(sql_android)

      //写入vid映射uid用户(ios)
      val sql_ios = "insert overwrite table t_vid_uid PARTITION(cs='iOS') select vid,uid,max(updatetime) as updatetime,max(ts) as ts from " +
        "(select coalesce(a.vid,b.vid) as vid,coalesce(a.uid,b.uid) as uid,coalesce(a.updatetime,b.updatetime) as updatetime,coalesce(a.ts,b.ts) as ts from " +
        "(select cid as vid,uid,'"+day+"' as updatetime,max(time)as ts from t_logdata_app where day=" + day + " and uid!='' and cs='iOS' and length(regexp_replace(cid,'-',''))=32 group by cid,uid) a " +
        "full outer join (select vid,uid,updatetime,ts from t_vid_uid where cs='iOS' ) b on a.vid=b.vid and a.uid=b.uid) where vid!='' and uid !='' and uid!='null' group by vid,uid"
      spark.sql(sql_ios)
      exec.executeInsertOrUpdate(s"update t_check set flag=1, message='$day:运行成功' where name='aliyun_t_vid_uid'",null)
      spark.stop()
    } catch {
      case e: Exception => {
        e.printStackTrace()
        try{
          exec.executeInsertOrUpdate(s"update t_check set flag=2, message='$day:运行失败' where name='aliyun_t_vid_uid'",null)
        }catch {
          case e:Throwable=>{
            e.printStackTrace()
          }
        }
      }
    }finally {
      spark.stop()
    }

  }
}
