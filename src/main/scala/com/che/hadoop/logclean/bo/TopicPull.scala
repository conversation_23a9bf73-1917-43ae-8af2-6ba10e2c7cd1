package com.che.hadoop.logclean.bo

import com.che.hadoop.underlay.dao.{MySparkSession, MySqlBaseDao}
import org.apache.spark.sql.SparkSession
import com.alibaba.fastjson.JSON
/**
 * 专题拉取接口。
 * 迁移到logclean工程下，跟其它业务日志同步清洗
 * */
object TopicPull {

  def main(args: Array[String]): Unit = {
    topicPull(MySparkSession.conn("Report_TopicPullJob"),"20220905")
  }
  /*专题拉取接口*/
  def topicPull(spark:SparkSession,day:String):Unit= {
    val t_check_pre="aliyun_"
    try{
      spark.sql(
        s"""
           |select json_tuple(data, 'id', 'name', 'shortName', 'createDateTime', 'publishTime',
           | 'topicState', 'topicUrl', 'topicTypeId', 'relationId','endDate'),
           |  `timestamp`, operation_state,from_unixtime(substr(`timestamp`,0,10), 'yyyyMMdd'),`timestamp`
           |from t_biz_data_temp where billType='topic'""".stripMargin)
        .toDF("id","name","shortName","createDateTime","publishTime",
          "topicState","topicUrl","topicTypeId","relationId","endDate","timestamp", "operationState", "day","f_timestamp")
        .createOrReplaceTempView("t_topic")
      /*保存数据到mysql*/
      //连接线上数据库mysql工具类实例
      val sqlBase=new MySqlBaseDao("bigdata2")
      spark.sql("""
                  |select id as f_topicId,name as f_name,shortName as f_shortname,createDateTime as f_createtime, publishTime as f_publishTime,topicState as f_topicState,
                  | split(topicUrl,'\\|')[0] as f_topicUrl_pc,split(topicUrl,'\\|')[1] as f_topicUrl_m,topicTypeId as f_topicTypeId, relationId as f_relationId,endDate AS f_endDate,
                  | case when operationState='delete' or topicState=0 then 0 else  1 end  as f_enable,
                  | day as f_day,from_unixtime(cast(substring(f_timestamp,0,10) as bigint),'yyyy-MM-dd HH:mm:ss') as f_timestamp
                  |from t_topic
                  |""".stripMargin).createOrReplaceTempView("t_topic_temp")
      spark.sql(
        """
          |select * from(
          | select *,ROW_NUMBER() OVER(PARTITION BY f_topicId ORDER BY f_timestamp DESC) as num from t_topic_temp
          | ) t
          | where t.num = 1
          |""".stripMargin)
        .drop("num")
        .createOrReplaceTempView("t_topic_distinct")
      sqlBase.getCacheTable("t_topic_history","(select f_topicId from db_hitsstatistic.t_topic_info) as topic",spark)
      /*新增*/
      val insertDF=spark.sql(
        """
          |select a.* from t_topic_distinct as a
          |left join t_topic_history as b on a.f_topicId=b.f_topicId
          |where b.f_topicid is null
          |""".stripMargin)
      sqlBase.saveMysql(insertDF,"db_hitsstatistic.t_topic_info")
      /*更新updatre*/
      val updateTopicSql="select a.* from t_topic_distinct as a inner join t_topic_history as b on a.f_topicId=b.f_topicId"
      val updateDF= spark.sql(updateTopicSql).toJSON.collect()
      /*循环插入数据*/
      updateDF.foreach(t=>
      {
        val json= JSON.parseObject(t)
        val topicid=json.getString("f_topicId")
        val name=json.getString("f_name")
        val shortname=json.getString("f_shortname")
        val createtime=json.getString("f_createtime")
        val publishTime=json.getString("f_publishTime")
        val topicState=json.getString("f_topicState")
        val topicUrl_pc=json.getString("f_topicUrl_pc")
        val topicUrl_m=json.getString("f_topicUrl_m")
        val topicTypeId=json.getString("f_topicTypeId")
        val relationId=json.getString("f_relationId")
        val endDate=json.getString("f_endDate")
        val day=json.getString("f_day")
        val timestamp=json.getString("f_timestamp")
        val updateSql=s"update db_hitsstatistic.t_topic_info set f_name='$name',f_shortname='$shortname',f_createtime='$createtime',f_publishTime='$publishTime'," +
          s"f_topicState=$topicState,f_topicUrl_pc='$topicUrl_pc',f_topicUrl_m='$topicUrl_m',f_topicTypeId=$topicTypeId,f_relationId='$relationId',f_endDate='$endDate',f_day='$day',f_timestamp='$timestamp',f_modifytime=CURRENT_TIMESTAMP" +
          s" where f_topicid=$topicid"
        println(updateSql)
        sqlBase.execute(updateSql)
      })
      val exec = new MySqlBaseDao()
      exec.executeInsertOrUpdate(s"update t_check set flag=1 , message='$day:运行成功' where name='${t_check_pre}t_topic_info'",null)
    }catch {
      case e: Exception => {
        e.printStackTrace()
        try{
          val exec = new MySqlBaseDao()
          exec.executeInsertOrUpdate(s"update t_check set flag=2, message='$day:${t_check_pre}t_topic_info 运行失败' where name='${t_check_pre}t_topic_info'",null)
        }catch {
          case e:Throwable=>{
            e.printStackTrace()
          }
        }
      }
    }

  }
}

