package com.che.hadoop.logclean.bo

import scala.collection.JavaConverters._
import java.util.ResourceBundle
import scala.collection.mutable.{Map, Set}
import com.che.hadoop.logclean.utils.JsonQuery
import com.che.hadoop.logclean.utils.Common
import org.apache.spark.sql.SparkSession
import com.che.hadoop.underlay.dao.{MySparkSession, MySqlBaseDao}
import scala.collection.mutable
import scala.reflect.runtime.universe._

/*
* author:张松伟
* 从日志里提取各业务数据
*/
object FetchLogData {

    //社区
    case class Bbs(uid: String, nickName: String, bbs_tid: String, bbs_pid: String,
                   subject: String, message: String, bbs_publish_time: String,
                   client: String, bbs_type: String, isExcellent: String,
                   imgUrl: String, coverUrl: String, videoUrl: String,
                   position: String, bbs_domain: String,
                   bbs_subcate: String, bbs_brand: String, bbs_series: String,
                   bbs_first_label: String, bbs_second_label: String, bbs_third_label: String,
                   timestamp: String, operationState: String, update_time: String)
    //回帖
    case class PostReply(uid: String, nickName: String, bbs_tid: String, bbs_pid: String, parentId: String, message: String, bbs_publish_time: String, client: String, imgUrl: String, timestamp: String, is_del: String, update_time: String)

    //用户关注
    case class UserFocus(fanUid: Long, uid: Long, focusState: Int, timestamp: Long, day: String, year: Int)

    //牛人标签
    case class SupermanLabel(tid: Long, labelId: Long, isActive: Int, timestamp: Long, day: String)

    //日志文章数据
    case class ArticleLog(id: String, title: String, mTitle: String, publishTime: String, createDateTime: String,
                          first_classifyId: String, first_classifyName: String, second_classifyId: String, second_classifyName: String,
                          content: String, relationId: String, relationType: String,
                          lable: String, sourceType: String, proName: String, citName: String,
                          firstLabel: String, secondLabel: String, thirdLabel: String, isDealer: String,
                          timestamp: Long, operationState: String)


    def main(args: Array[String]): Unit = {
        val hiveDB = "default."
        val spark=MySparkSession.conn()
        Common.getBetweenDates("20230419","20230419").foreach(day=>{
            spark.sql(
                s"""
                   |select data, billtype,timestamp,operation_state,env from t_ods_thba_logdata
                   |where day='$day' and (env != 'test' or env is null)
                   |""".stripMargin).createOrReplaceTempView("t_biz_data_temp")
            go(spark,hiveDB, day, false, false, true, false, true, false, false,  false)
        })
    }

    def go(spark:SparkSession,hiveDB: String, day: String, accessorySwitch: Boolean, supermanSwitch: Boolean, userFocusSwitch: Boolean, bbsPostSwitch: Boolean, bbsPostReplySwitch: Boolean, articleSwitch: Boolean, commentSwitch: Boolean,  rerun: Boolean): Unit = {
        val t_check_pre="aliyun_"
        var userTable = "default.t_user"
        var bbsTable = "default.t_bbs_post"
        var lableTable = "default.t_stg_superman_label"
        if (hiveDB == "db_test.") {
            userTable = "db_test.t_user_back"
            bbsTable = "db_test.t_bbs_post_2020"
            lableTable = "db_test.t_stg_superman_label_test"
        }
        try {
            spark.conf.set("spark.serializer", "org.apache.spark.serializer.KryoSerializer")
            spark.sparkContext.getConf.registerKryoClasses(Array(classOf[Bbs], classOf[PostReply], classOf[UserFocus], classOf[SupermanLabel], classOf[ArticleLog]))
            val exec = new MySqlBaseDao()
            import spark.implicits._
            import spark.sql
            //配件
            if (accessorySwitch) {
                sql(s"alter table ${hiveDB}t_accessory drop if exists partition(day='$day')")
                val t = sql("select json_tuple(data, 'userPhone', 'userName', 'userAddress', 'userAddressCode', 'lon', 'lat', 'userIsDelete', 'orderId', 'orderStatus', 'orderCreateTime', 'goods'),`timestamp`, operation_state,from_unixtime(substr(`timestamp`,0,10), 'yyyyMMdd') from t_biz_data_temp where billtype='mall'").toDF("userPhone", "userName", "userAddress", "userAddressCode", "lon", "lat", "userIsDelete", "orderId", "orderStatus", "orderCreateTime", "goods", "timestamp", "operationState", "day")
                t.cache()
                val count = t.count()
                if (count > 0) {
                    t.dropDuplicates().write.mode("append").partitionBy("day").format("hive").saveAsTable(hiveDB + "t_accessory")
                    exec.executeInsertOrUpdate(s"update t_check set flag=1 , message='$day:运行成功' where name = '${t_check_pre}t_accessory'", null)
                } else {
                    exec.executeInsertOrUpdate(s"update t_check set flag=1, message='$day:${t_check_pre}t_accessory num is 0' where name = '${t_check_pre}t_accessory'", null)
                }
                t.unpersist()
            }
            //      牛人标签
            if (supermanSwitch) {
                val t = sql(s"select json_tuple(data, 'tid', 'labelId', 'isActive'),`timestamp`,from_unixtime(substr(`timestamp`,0,10), 'yyyyMMdd') from t_biz_data_temp where billtype='superman_label'")
                    .toDF("tid", "labelId", "isActive", "timestamp", "day")
                    .selectExpr("cast(nvl(tid,'0') as long) tid", "cast(nvl(labelId,'0') as long) labelId", "cast(nvl(isActive,'0') as int) isActive", "cast(nvl(timestamp,'0') as long) timestamp", "day")
                    .as[SupermanLabel]
                    .groupByKey(f => f.tid)
                    .mapGroups((ids, attrs) => {
                        attrs.maxBy(f => f.timestamp)
                    })
                t.cache()
                t.createOrReplaceTempView("superman_label_today")
                val count = t.count()
                if (count > 0) {
                    sql("set spark.sql.shuffle.partitions=1")
                    sql(
                        s"""insert overwrite table $lableTable select coalesce(a.tid, b.tid) as tid ,coalesce(a.labelId, b.labelId) as labelId, coalesce(a.isActive, b.isActive) as isActive
                           | , coalesce(a.timestamp, b.timestamp) as timestamp,coalesce(a.day, b.day) as day
                           | from $lableTable b full join superman_label_today a on b.tid=a.tid""".stripMargin)
                    exec.executeInsertOrUpdate(s"update t_check set flag=1 , message='$day:运行成功' where name = '${t_check_pre}t_stg_superman_label'", null)
                } else {
//                    不是每天该标签都有数据，设置flag=1
                    exec.executeInsertOrUpdate(s"update t_check set flag=1, message='$day:${t_check_pre}t_stg_superman_label num is 0' where name = '${t_check_pre}t_stg_superman_label'", null)
                }
                t.unpersist()
            }

            //      用户关注
            if (userFocusSwitch) {

                val t = sql("select json_tuple(data, 'fanUid', 'uid', 'focusState'),`timestamp`,from_unixtime(substr(`timestamp`,0,10), 'yyyyMMdd'),cast(from_unixtime(substr(`timestamp`,0,10), 'yyyy') as int) from t_biz_data_temp where billtype='bbs_user_focus' and env = 'prod' ")
                    .toDF("fanUid", "uid", "focusState", "timestamp", "day", "year")
                    .selectExpr("cast(fanUid as long) fanUid", "cast(uid as long) uid", "cast(focusState as int) focusState", "cast(timestamp as long) timestamp", "day", "year")
                t.cache()
                if (!rerun) {
                    t.write.mode("append").partitionBy("year").format("hive").saveAsTable(hiveDB + "t_ods_superman_user_focus")
                }
                t.as[UserFocus]
                    .groupByKey(f => (f.fanUid, f.uid))
                    .mapGroups((ids, attrs) => {
                        attrs.maxBy(f => f.timestamp)
                    })
                t.createOrReplaceTempView("user_focus_attrs_today")
                val count = t.count()
                if (count > 0) {
                    sql(
                      s"""
                        |insert overwrite table ${hiveDB}t_dwd_superman_user_focus
                        |(select fanUid,uid,timestamp,day,year from user_focus_attrs_today
                        |union
                        |select b.fanUid ,b.uid,b.timestamp,b.day,b.year
                        |from user_focus_attrs_today a
                        |right join ${hiveDB}t_dwd_superman_user_focus b on b.fanUid=a.fanUid and b.uid=a.uid
                        |having a.uid is null)
                        |""".stripMargin)
                    exec.executeInsertOrUpdate(s"update t_check set flag=1 , message='$day:运行成功' where name = '${t_check_pre}t_dwd_superman_user_focus'", null)
                } else {
                    exec.executeInsertOrUpdate(s"update t_check set flag=2, message='$day:${t_check_pre}t_dwd_superman_user_focus num is 0' where name = '${t_check_pre}t_dwd_superman_user_focus'", null)
                }
                t.unpersist()
            }
            //论坛主贴
            if (bbsPostSwitch) {
                val portraitExe = new MySqlBaseDao()
                val seriesBrandM = Map[Int, Int]()
                val seriesTonnageM = Map[Int, Int]()
                val seriesBrandRs = portraitExe.executeQuery("select distinct brandId, seriesId from db_userportrait.t_brand_series_subcate where enable=1", null)
                while (seriesBrandRs.next()) {
                    seriesBrandM.+=((seriesBrandRs.getInt("seriesId") -> seriesBrandRs.getInt("brandId")))
                }

                val seriesTonnageRs = portraitExe.executeQuery("select distinct seriesId, tonnageType from db_userportrait.t_series_tonnage", null)
                while (seriesTonnageRs.next()) {
                    seriesTonnageM.+=((seriesTonnageRs.getInt("seriesId") -> seriesTonnageRs.getInt("tonnageType")))
                }
                spark.udf.register("seriesBrand", (seriesIds: String) => {
                    var result = ""
                    if (seriesIds != null) {
                        for (seriesId <- seriesIds.split(",")) {
                            if (seriesBrandM.contains(seriesId.toInt)) {
                                result += seriesBrandM(seriesId.toInt)
                                result += ","
                            }
                        }
                        if (result.endsWith(",")) {
                            result = result.substring(0, result.length - 1)
                        }
                    }
                    result
                })
                spark.udf.register("seriesTonnage", (seriesIds: String) => {
                    var result = ""
                    if (seriesIds != null) {
                        for (seriesId <- seriesIds.split(",")) {
                            if (seriesTonnageM.contains(seriesId.toInt)) {
                                result += seriesTonnageM(seriesId.toInt)
                                result += ","
                            }
                        }
                        if (result.endsWith(",")) {
                            result = result.substring(0, result.length - 1)
                        }
                    }
                    result
                })

                val t = sql(
                    s"""select json_tuple(data, 'uid', 'nickName', 'tid', 'pid',
                       |'title', 'content', 'postTime',
                       |'client', 'type', 'isExcellent',
                       |'imgUrl', 'coverUrl', 'videoUrl',
                       |'position', 'domain',
                       |'subcate', 'brand', 'series',
                       |'firstLabel', 'secondLabel', 'thirdLabel')
                       |,`timestamp`, operation_state,from_unixtime(substr(`timestamp`,0,10), 'yyyyMMdd') from t_biz_data_temp where billtype='bbs_post' """.stripMargin)
                    .toDF("uid", "nickName", "bbs_tid", "bbs_pid",
                        "subject", "message", "bbs_publish_time",
                        "client", "bbs_type", "isExcellent",
                        "imgUrl", "coverUrl", "videoUrl",
                        "position", "bbs_domain",
                        "bbs_subcate", "bbs_brand", "bbs_series",
                        "bbs_first_label", "bbs_second_label", "bbs_third_label",
                        "timestamp", "operationState", "update_time")
                    .as[Bbs]
                    .groupByKey(f => f.bbs_tid)
                    .mapGroups((bbs_tid, bbs_attrs) => {
                        val result = new Array[String](23)
                        var timestampMax = 0l
                        for (bbs_attr <- bbs_attrs) {
                            if (bbs_attr.timestamp.toLong > timestampMax) {
                                timestampMax = bbs_attr.timestamp.toLong
                                if (bbs_attr.uid != null && !bbs_attr.uid.isEmpty) {
                                    result(0) = bbs_attr.uid
                                }
                                if (bbs_attr.nickName != null && !bbs_attr.nickName.isEmpty) {
                                    result(1) = bbs_attr.nickName
                                }
                                if (bbs_attr.subject != null && !bbs_attr.subject.isEmpty) {
                                    result(2) = bbs_attr.subject
                                }
                                if (bbs_attr.message != null && !bbs_attr.message.isEmpty) {
                                    result(3) = bbs_attr.message
                                }
                                if (bbs_attr.bbs_publish_time != null && bbs_attr.bbs_publish_time.startsWith("2")) {
                                    result(4) = bbs_attr.bbs_publish_time
                                }
                                if (bbs_attr.client != null && !bbs_attr.client.isEmpty) {
                                    result(5) = bbs_attr.client
                                }
                                if (bbs_attr.bbs_type != null && !bbs_attr.bbs_type.isEmpty) {
                                    result(6) = bbs_attr.bbs_type
                                }
                                if (bbs_attr.isExcellent != null && !bbs_attr.isExcellent.isEmpty) {
                                    result(7) = bbs_attr.isExcellent
                                }
                                if (bbs_attr.imgUrl != null && !bbs_attr.imgUrl.isEmpty) {
                                    result(8) = bbs_attr.imgUrl
                                }
                                if (bbs_attr.coverUrl != null && !bbs_attr.coverUrl.isEmpty) {
                                    result(9) = bbs_attr.coverUrl
                                }
                                if (bbs_attr.videoUrl != null && !bbs_attr.videoUrl.isEmpty) {
                                    result(10) = bbs_attr.videoUrl
                                }
                                if (bbs_attr.position != null && !bbs_attr.position.isEmpty) {
                                    result(11) = bbs_attr.position
                                }
                                if (bbs_attr.bbs_domain != null && !bbs_attr.bbs_domain.isEmpty) {
                                    result(12) = bbs_attr.bbs_domain
                                }
                                if (bbs_attr.bbs_subcate != null && !bbs_attr.bbs_subcate.isEmpty) {
                                    result(13) = bbs_attr.bbs_subcate
                                }
                                if (bbs_attr.bbs_brand != null && !bbs_attr.bbs_brand.isEmpty) {
                                    result(14) = bbs_attr.bbs_brand
                                }
                                if (bbs_attr.bbs_series != null && !bbs_attr.bbs_series.isEmpty) {
                                    result(15) = bbs_attr.bbs_series
                                }
                                if (bbs_attr.bbs_first_label != null && !bbs_attr.bbs_first_label.isEmpty) {
                                    result(16) = bbs_attr.bbs_first_label
                                }
                                if (bbs_attr.bbs_second_label != null && !bbs_attr.bbs_second_label.isEmpty) {
                                    result(17) = bbs_attr.bbs_second_label
                                }
                                if (bbs_attr.bbs_third_label != null && !bbs_attr.bbs_third_label.isEmpty) {
                                    result(18) = bbs_attr.bbs_third_label
                                }
                                if (bbs_attr.timestamp != null && !bbs_attr.timestamp.isEmpty) {
                                    result(19) = bbs_attr.timestamp
                                }
                                if (bbs_attr.operationState != null && !bbs_attr.operationState.isEmpty) {
                                    result(20) = bbs_attr.operationState
                                }
                                if (bbs_attr.update_time != null && !bbs_attr.update_time.isEmpty) {
                                    result(21) = bbs_attr.update_time
                                }
                                if (bbs_attr.bbs_pid != null && !bbs_attr.bbs_pid.isEmpty) {
                                    result(22) = bbs_attr.bbs_pid
                                }
                            } else {
                                if (bbs_attr.uid != null && !bbs_attr.uid.isEmpty && (result(0) == null || result(0).isEmpty)) {
                                    result(0) = bbs_attr.uid
                                }
                                if (bbs_attr.nickName != null && !bbs_attr.nickName.isEmpty && (result(1) == null || result(1).isEmpty)) {
                                    result(1) = bbs_attr.nickName
                                }
                                if (bbs_attr.subject != null && !bbs_attr.subject.isEmpty && (result(2) == null || result(2).isEmpty)) {
                                    result(2) = bbs_attr.subject
                                }
                                if (bbs_attr.message != null && !bbs_attr.message.isEmpty && (result(3) == null || result(3).isEmpty)) {
                                    result(3) = bbs_attr.message
                                }
                                if (bbs_attr.bbs_publish_time != null && bbs_attr.bbs_publish_time.startsWith("2") && (result(4) == null || result(4).isEmpty)) {
                                    result(4) = bbs_attr.bbs_publish_time
                                }
                                if (bbs_attr.client != null && !bbs_attr.client.isEmpty && (result(5) == null || result(5).isEmpty)) {
                                    result(5) = bbs_attr.client
                                }
                                if (bbs_attr.bbs_type != null && !bbs_attr.bbs_type.isEmpty && (result(6) == null || result(6).isEmpty)) {
                                    result(6) = bbs_attr.bbs_type
                                }
                                if (bbs_attr.isExcellent != null && !bbs_attr.isExcellent.isEmpty && (result(7) == null || result(7).isEmpty)) {
                                    result(7) = bbs_attr.isExcellent
                                }
                                if (bbs_attr.imgUrl != null && !bbs_attr.imgUrl.isEmpty && (result(8) == null || result(8).isEmpty)) {
                                    result(8) = bbs_attr.imgUrl
                                }
                                if (bbs_attr.coverUrl != null && !bbs_attr.coverUrl.isEmpty && (result(9) == null || result(9).isEmpty)) {
                                    result(9) = bbs_attr.coverUrl
                                }
                                if (bbs_attr.videoUrl != null && !bbs_attr.videoUrl.isEmpty && (result(10) == null || result(10).isEmpty)) {
                                    result(10) = bbs_attr.videoUrl
                                }
                                if (bbs_attr.position != null && !bbs_attr.position.isEmpty && (result(11) == null || result(11).isEmpty)) {
                                    result(11) = bbs_attr.position
                                }
                                if (bbs_attr.bbs_domain != null && !bbs_attr.bbs_domain.isEmpty && (result(12) == null || result(12).isEmpty)) {
                                    result(12) = bbs_attr.bbs_domain
                                }
                                if (bbs_attr.bbs_subcate != null && !bbs_attr.bbs_subcate.isEmpty && (result(13) == null || result(13).isEmpty)) {
                                    result(13) = bbs_attr.bbs_subcate
                                }
                                if (bbs_attr.bbs_brand != null && !bbs_attr.bbs_brand.isEmpty && (result(14) == null || result(14).isEmpty)) {
                                    result(14) = bbs_attr.bbs_brand
                                }
                                if (bbs_attr.bbs_series != null && !bbs_attr.bbs_series.isEmpty && (result(15) == null || result(15).isEmpty)) {
                                    result(15) = bbs_attr.bbs_series
                                }
                                if (bbs_attr.bbs_first_label != null && !bbs_attr.bbs_first_label.isEmpty && (result(16) == null || result(16).isEmpty)) {
                                    result(16) = bbs_attr.bbs_first_label
                                }
                                if (bbs_attr.bbs_second_label != null && !bbs_attr.bbs_second_label.isEmpty && (result(17) == null || result(17).isEmpty)) {
                                    result(17) = bbs_attr.bbs_second_label
                                }
                                if (bbs_attr.bbs_third_label != null && !bbs_attr.bbs_third_label.isEmpty && (result(18) == null || result(18).isEmpty)) {
                                    result(18) = bbs_attr.bbs_third_label
                                }
                                if (bbs_attr.timestamp != null && !bbs_attr.timestamp.isEmpty && (result(19) == null || result(19).isEmpty)) {
                                    result(19) = bbs_attr.timestamp
                                }
                                if (bbs_attr.operationState != null && !bbs_attr.operationState.isEmpty && (result(20) == null || result(20).isEmpty)) {
                                    result(20) = bbs_attr.operationState
                                }
                                if (bbs_attr.update_time != null && !bbs_attr.update_time.isEmpty && (result(21) == null || result(21).isEmpty)) {
                                    result(21) = bbs_attr.update_time
                                }
                                if (bbs_attr.bbs_pid != null && !bbs_attr.bbs_pid.isEmpty && (result(22) == null || result(22).isEmpty)) {
                                    result(22) = bbs_attr.bbs_pid
                                }
                            }
                        }
                        Bbs(result(0), result(1), bbs_tid, result(22),
                            result(2), result(3), result(4),
                            result(5), result(6), result(7),
                            result(8), result(9), result(10),
                            result(11), result(12),
                            result(13), result(14), result(15),
                            result(16), result(17), result(18),
                            result(19), result(20), result(21))
                    })
                t.cache()
                t.createOrReplaceTempView("bbs_attrs_today")
                val count = t.count().toInt
                if (count > 0) {

                    spark.sql(
                        s"""
                           |select
                           | a.uid ,a.nickName, a.bbs_tid, a.bbs_pid
                           | ,a.subject ,a.message, a.bbs_publish_time
                           | ,a.client ,a.bbs_type, a.isExcellent
                           | ,a.imgUrl ,a.coverUrl ,a.videoUrl
                           | ,a.position ,a.bbs_domain
                           | ,a.bbs_subcate, a.bbs_brand, a.bbs_series
                           | ,a.bbs_first_label,a.bbs_second_label,a.bbs_third_label
                           | , a.operationState , a.update_time, a.bbs_publish_time,b.niuren_level
                           | from bbs_attrs_today a
                           |left join ${userTable} b
                           |on a.uid = b.uid
                 """.stripMargin).createOrReplaceTempView("bbs_attrs_level_today")
                    sql("set spark.sql.shuffle.partitions=1")
                    sql(
                        s"""insert overwrite table ${bbsTable} select coalesce(a.uid,b.uid) as uid ,coalesce(a.nickName,b.nickName) as nickName, coalesce(a.bbs_tid,b.bbs_tid) as bbs_tid, coalesce(a.bbs_pid,b.bbs_pid) as bbs_pid
                           | ,coalesce(a.subject,b.subject) as subject ,coalesce(a.message,b.message) as message, coalesce(a.bbs_publish_time,b.bbs_publish_time) as bbs_publish_time
                           | ,coalesce(a.client,b.client) as client ,coalesce(a.bbs_type,b.bbs_type) as bbs_type, coalesce(a.isExcellent,b.isExcellent) as isExcellent
                           | ,coalesce(a.imgUrl,b.imgUrl) as imgUrl ,coalesce(a.coverUrl,b.coverUrl) as coverUrl ,coalesce(a.videoUrl,b.videoUrl) as videoUrl
                           | ,coalesce(a.position,b.position) as position ,coalesce(a.bbs_domain,b.bbs_domain) as bbs_domain
                           | ,coalesce(a.bbs_subcate,b.bbs_subcate) as bbs_subcate,coalesce(a.bbs_brand,b.bbs_brand, if(a.bbs_series is null, null, seriesBrand(a.bbs_series))) as bbs_brand,coalesce(a.bbs_series,b.bbs_series) as bbs_series,coalesce(if(a.bbs_series is null, null, seriesTonnage(a.bbs_series)), b.bbs_tonnage) as bbs_tonnage
                           | ,coalesce(a.bbs_first_label,b.bbs_first_label) as bbs_first_label,coalesce(a.bbs_second_label,b.bbs_second_label) as bbs_second_label,coalesce(a.bbs_third_label,b.bbs_third_label) as bbs_third_label
                           | ,case a.operationState when null then b.is_del when 'delete' then '1' else '0' end is_del, coalesce(a.update_time,b.update_time) as update_time, replace(substr(coalesce(a.bbs_publish_time, b.bbs_publish_time), 0, 10), '-', '') day
                           | ,coalesce(a.niuren_level,b.niuren_level) as niuren_level, substr(coalesce(a.bbs_publish_time,b.bbs_publish_time), 0, 4) as bbs_publish_time_year
                           | from ${bbsTable} b full join bbs_attrs_level_today a on b.bbs_tid=a.bbs_tid""".stripMargin)
                    exec.executeInsertOrUpdate(s"update t_check set flag=1 , message='$day:运行成功' where name = '${t_check_pre}t_bbs_post'", null)
                } else {
                    exec.executeInsertOrUpdate(s"update t_check set flag=2, message='$day:${t_check_pre}t_bbs_post num is 0' where name = '${t_check_pre}t_bbs_post'", null)
                }
                t.unpersist()
            }
            //论坛回帖
            if (bbsPostReplySwitch) {
                //获取一年内被删除的帖子,用来删除回帖.为了效率忽略一年外被删除的帖子
                val oneYearDays = com.che.hadoop.logclean.utils.Common.getDate(365)
                val tids = Set[String]()
                sql(s"select bbs_tid from ${hiveDB}t_bbs_post where is_del=1 and day > $oneYearDays").collect().foreach(attrs => {
                    tids.+(attrs.getLong(0).toString)
                })
                spark.udf.register("judge_del", (tid: String) => {
                    tids.contains(tid)
                })
                val t = sql("select json_tuple(data, 'uid', 'nickName', 'tid', 'pid', 'parentId', 'content', 'postTime','client','imgUrl'),`timestamp`, if(operation_state=='delete',1,0),from_unixtime(substr(`timestamp`,0,10), 'yyyyMMdd') from t_biz_data_temp where billtype='bbs_post_reply'")
                    .toDF("uid", "nickName", "bbs_tid", "bbs_pid", "parentId", "message", "bbs_publish_time", "client", "imgUrl", "timestamp", "is_del", "update_time")
                    .as[PostReply]
                    .groupByKey(f => (f.bbs_pid))
                    .mapGroups((ids, attrs) => {
                        attrs.maxBy(f => f.timestamp)
                    })
                t.cache()
                t.createOrReplaceTempView("bbs_post_reply_today")
                val count = t.count()
                if (count > 0) {
                    sql("set spark.sql.shuffle.partitions=1")
                    sql(
                        s"""insert overwrite table ${hiveDB}t_stg_recommend_bbs_post_reply select coalesce(a.uid,b.uid) as uid, coalesce(a.nickName,b.nickName) as nickName, coalesce(a.bbs_tid,b.bbs_tid) as bbs_tid
                           | ,coalesce(a.bbs_pid,b.bbs_pid) as bbs_pid, coalesce(a.parentId,b.parentId) as parentId, coalesce(a.message,b.message) as message, coalesce(a.bbs_publish_time,b.bbs_publish_time) as bbs_publish_time
                           | ,coalesce(a.client,b.client) as client, coalesce(a.imgUrl,b.imgUrl) as imgUrl, coalesce(a.is_del,if(judge_del(coalesce(a.bbs_tid,b.bbs_tid)), 1,b.is_del)) as is_del
                           | ,coalesce(a.update_time,b.update_time) as update_time, replace(substr(coalesce(a.bbs_publish_time, b.bbs_publish_time), 0, 10), '-', '')
                           | from ${hiveDB}t_stg_recommend_bbs_post_reply b full join bbs_post_reply_today a on b.bbs_pid=a.bbs_pid""".stripMargin
                    )
                    exec.executeInsertOrUpdate(s"update t_check set flag=1 , message='$day:运行成功' where name = '${t_check_pre}t_stg_recommend_bbs_post_reply'", null)
                } else {
                    exec.executeInsertOrUpdate(s"update t_check set flag=2, message='$day:${t_check_pre}t_stg_recommend_bbs_post_reply num is 0' where name = '${t_check_pre}t_stg_recommend_bbs_post_reply'", null)
                }
                t.unpersist()
            }
            //文章
//            if (articleSwitch) {
//                import spark.implicits._
//                val dao = new PortraitMySqlBaseDao()
//                val seriesTonnageM = mutable.Map[String, String]()
//                val rs = dao.executeQuery("select seriesId, tonnageType from t_series_tonnage", null)
//                while (rs.next()) {
//                    seriesTonnageM += (rs.getInt("seriesId").toString -> rs.getInt("tonnageType").toString)
//                }
//                //文章的where条件由于业务方上线不小心加了空格, 改为rlike
//                val ar = sql("select json_tuple(data, 'id', 'title', 'mTitle', 'publishTime', 'createDateTime', 'first_classifyId', 'first_classifyName', 'second_classifyId', 'second_classifyName','content','relationId','relationType','lable','sourceType','proName','citName','firstLabel','secondLabel','thirdLabel','isDealer'),cast(if(length(`timestamp`)=17,substr(`timestamp`,0,8),substr(`timestamp`,0,10)) as long), operation_state from t_biz_data_temp where billtype rlike 'article_t_article'")
//                    .toDF("id", "title", "mTitle", "publishTime", "createDateTime",
//                        "first_classifyId", "first_classifyName", "second_classifyId", "second_classifyName",
//                        "content", "relationId", "relationType",
//                        "lable", "sourceType", "proName", "citName",
//                        "firstLabel", "secondLabel", "thirdLabel", "isDealer",
//                        "timestamp", "operationState")
//                    .as[ArticleLog]
//                ar.cache()
//
//                ar.groupByKey(f => (f.id))
//                    .mapGroups((ids, items) => {
//                        //一天内出现多条合并多条的值
//                        val rm = runtimeMirror(scala.reflect.runtime.universe.getClass.getClassLoader)
//                        val itemsSort = items.toList.sortBy(f => f.timestamp)
//                        val instmFirst = rm.reflect(itemsSort(0))
//                        for (index <- 1 until itemsSort.length) {
//                            val instm = rm.reflect(itemsSort(index))
//                            for (item <- Seq("title", "mTitle", "publishTime", "createDateTime",
//                                "first_classifyId", "first_classifyName", "second_classifyId", "second_classifyName",
//                                "content", "relationId", "relationType",
//                                "lable", "sourceType", "proName", "citName",
//                                "firstLabel", "secondLabel", "thirdLabel", "isDealer",
//                                "timestamp", "operationState")) {
//                                val v = instm.reflectField(typeOf[ArticleLog].declaration(newTermName(item)).asTerm).get
//                                if (v != null) {
//                                    instmFirst.reflectField(typeOf[ArticleLog].declaration(newTermName(item)).asTerm).set(v)
//                                }
//                            }
//                        }
//                        itemsSort(0)
//                    }).foreachPartition(articleLogs => {
//                    val portrait_exe = new PortraitMySqlBaseDao()
//                    while (articleLogs.hasNext) {
//                        val articleLog = articleLogs.next()
//                        val relations = Array(Set[String](), Set[String](), Set[String](), Set[String](), Set[String](), Set[String]())
//                        var i = 0
//                        for (item <- articleLog.relationId.split("\\|")) {
//                            for (item1 <- item.split("#")) {
//                                relations(i).add(item1)
//                                if (i == 3 && seriesTonnageM.contains(item1)) {
//                                    relations(5).add(seriesTonnageM(item1))
//                                }
//                                i += 1
//                            }
//                            i = 0
//                        }
//                        if (articleLog.operationState == "delete") {
//                            val sql = s"update t_article set is_del = 1 where article_id=${articleLog.id}"
//                            portrait_exe.execute(sql)
//                        } else {
//                            var label_id = ""
//                            val label_type_id = Set[String]()
//                            if (articleLog.lable != null && articleLog.lable.length != 0) {
//                                val l = new JsonQuery(articleLog.lable)
//                                for (item <- l) {
//                                    val m = new JsonQuery(item)
//                                    label_id += m.ms("lableId") + ','
//                                    if (!label_type_id.contains(m.ms("typeId"))) {
//                                        label_type_id.add(m.ms("typeId"))
//                                    }
//                                }
//                            }
//                            //动态拼接sql
//                            val rm = runtimeMirror(scala.reflect.runtime.universe.getClass.getClassLoader)
//                            val instm = rm.reflect(articleLog)
//
//                            val fields = new mutable.MutableList[String]()
//                            val insertValues = new mutable.MutableList[String]()
//                            val updateValues = new mutable.MutableList[String]()
//                            //不在类中但属于hive字段的属性
//                            val newFields = Seq("day", "cate_id", "sub_cate_id", "brand_id", "series_id", "product_id", "tonnage_id", "lable_id", "lable_type_id", "is_del")
//                            val newValues = Seq(s"'${day}'", s"'${relations(0).mkString(",")}'",
//                                s"'${relations(1).mkString(",")}'", s"'${relations(2).mkString(",")}'", s"'${relations(3).mkString(",")}'", s"'${relations(4).mkString(",")}'", s"'${relations(5).mkString(",")}'",
//                                s"'${if (label_id.length == 0) "" else label_id.substring(0, label_id.length - 1)}'", s"'${label_type_id.mkString(",")}'", "0"
//                            )
//                            fields.++=(newFields)
//                            insertValues.++=(newValues)
//
//                            for (index <- 0 until newFields.length) {
//                                updateValues.+=(s"${newFields(index)}=${newValues(index)}")
//                            }
//                            //日志中的字段名和数据库中的字段名不一致,增加替换
//                            val m = Map("id" -> "article_id", "publishTime" -> "publish_time", "first_classifyId" -> "first_classify_id", "first_classifyName" -> "first_classify_name",
//                                "second_classifyId" -> "second_classify_id", "second_classifyName" -> "second_classify_name", "sourceType" -> "source_type", "proName" -> "province", "citName" -> "city")
//                            for (item <- Seq("id", "title", "mTitle", "publishTime",
//                                "first_classifyId", "first_classifyName", "second_classifyId", "second_classifyName",
//                                "content",
//                                "sourceType", "proName", "citName",
//                                "firstLabel", "secondLabel", "thirdLabel", "isDealer")) {
//                                val v = instm.reflectField(typeOf[ArticleLog].declaration(newTermName(item)).asTerm).get
//                                if (v != null) {
//                                    fields.+=(m.getOrElse(item, item))
//                                    if (v == "title" || v == "mTitle" || v == "content") {
//                                        insertValues.+=(s"'${v.toString.replace("'", "")}'")
//                                        updateValues.+=(s"${m.getOrElse(item, item)}='${v.toString.replace("'", "")}'")
//                                    } else if (v == "publishTime") {
//                                        insertValues.+=(s"'${v.toString.substring(0, 10)}'")
//                                        updateValues.+=(s"${m.getOrElse(item, item)}='${v.toString.substring(0, 10)}'")
//                                    } else {
//                                        insertValues.+=(s"'${v.toString}'")
//                                        updateValues.+=(s"${m.getOrElse(item, item)}='${v.toString}'")
//                                    }
//                                }
//                            }
//                            val sql = s"""INSERT INTO `t_article`(${fields.mkString(",")})VALUES(${insertValues.mkString(",")})ON DUPLICATE KEY UPDATE ${updateValues.mkString(",")};"""
//                            portrait_exe.execute(sql)
//                        }
//                    }
//                })
//                //报告运行信息
//                val count = ar.count()
//                if (count > 0) {
//                    exec.executeInsertOrUpdate(s"update t_check set flag=1 , message='$day:运行成功' where name = '${t_check_pre}t_article'", null)
//                } else {
//                    exec.executeInsertOrUpdate(s"update t_check set flag=2, message='$day:${t_check_pre}t_article num is 0' where name = '${t_check_pre}t_article'", null)
//                }
//                ar.unpersist()
//            }
            spark.catalog.uncacheTable("t_biz_data_temp")
        } catch {
            case e: Exception => {
                e.printStackTrace
                try {
                    val exec = new MySqlBaseDao()
//                    exec.executeInsertOrUpdate(s"update t_check set flag=2, message='$day:FetchLogData.go程序运行失败'  where name in ('${t_check_pre}t_accessory', '${t_check_pre}t_stg_superman_label', '${t_check_pre}t_dwd_superman_user_focus', '${t_check_pre}t_bbs_post', '${t_check_pre}t_stg_recommend_bbs_post_reply')", null)
                } catch {
                    case e: Throwable => {
                        e.printStackTrace()
                    }
                }
            }
        }
    }
}
