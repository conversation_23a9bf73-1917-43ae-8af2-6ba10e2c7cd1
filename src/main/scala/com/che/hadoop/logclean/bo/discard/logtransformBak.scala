package com.che.hadoop.logclean.bo.discard

import com.che.hadoop.logclean.dao.mysql_conn
import com.che.hadoop.underlay.dao
import org.apache.spark.sql.{DataFrame, Row, SparkSession}

object logtransformBak {

  def main(args: Array[String]): Unit = {
    val spark=dao.MySparkSession.conn()
     savehive("20191108",spark)
  }

    /*转换mysql t_type.f_code数据保存到Hive*/
   def savehive(day:String,spark:SparkSession):Unit={
     val mysqljson=GetType_M(dao.MySparkSession.conn()).collect()
     spark.udf.register("getcode", GetCode(_:String,_:String,mysqljson))
     dao.MySparkSession.conn().
       sql(s"select res,k,vtc,i,vtf,vtl,uid,vid,sid,ip,ua,daytime,eventid,action,lable,ga_vid,dealers_uid,hours,day from t_logdata where day=$day")
       .repartition(1000)
       .createOrReplaceTempView("t_oldata")
     spark.sql("select  getcode(ua,k) as code,* from t_oldata").createOrReplaceTempView("t_logresult")
//     spark.sql("select * from t_logresult").show()
     /*合并小文件*/
     spark.sql("set mapred.reduce.tasks=1")
     val sql =
       s"""insert overwrite table t_logdata partition(day=$day)
          |select res,k,vtc,i,vtf,vtl,uid,vid,sid,ip,ua,daytime,eventid,action,lable,code,ga_vid,dealers_uid, hours
          |from  t_logresult   DISTRIBUTE BY rand()""".stripMargin
     spark.sql(sql)
   }



  /* 得到t_type code码*/
  def GetCode(ua:String,value:String,arrays: Array[Row]):String=
  {
    var code="0"
    var array = arrays.filter(it =>com.che.hadoop.logclean.utils.function_.GetRegular(value, it.getAs("urlregular"))) //正则匹配channl


    if(array!=null &&array.length>0)//是否正则匹配成功
    {
      if(array.length>1) //如果正则匹配数量>1
      {
        var vt=0
        if(ua.contains("Windows")|| ua.contains("Macintosh"))
          vt= 11
        else if(ua.contains("MicroMessenger"))
          vt= 15
        else if(!ua.contains("MicroMessenger")&& !ua.contains("360CHE")&& !ua.contains("Windows")&& !ua.contains("Macintosh"))
          vt= 12
        else if(ua.contains("iPhone")&&ua.contains("360CHE"))
          vt= 13
        else if(ua.contains("Android")&&ua.contains("360CHE"))
          vt= 14
        else
          vt= 0

        var array_1=array.filter(it=>it.getAs("parentid")==vt)//匹配ua
        if(array_1.length>0)
        {
          val array_sort=  array_1.sortBy(r => (r.getAs[Long]("code"))).reverse //降序排序， 然后取第一条赋值
          code=array_sort(0).getAs[Long]("code").toString
        }
        else
        {
          var array_2=array.filter(it=>it.getAs("defaultvalue")==1) //匹配默认值
          if(array_2.length>0)
          {
            val array_sort= array_2. sortBy(r => (r.getAs[Long]("code"))).reverse //降序排序， 然后取第一条赋值
            code=array_sort(0).getAs[Long]("code").toString
          }
        }
      }
      else
      {
        code=array(0).getAs[Long]("code").toString
      }
    }
    return  code
  }

  /*匹配user_agent判断vt 1 pc,2,ios,3.anrod,4 m站 5 微信端*/
  def  GetUa(ua:String):Int={
    if(ua.contains("Windows")|| ua.contains("Macintosh"))
      return 11
    else if(ua.contains("MicroMessenger"))
      return 15
    else if(!ua.contains("MicroMessenger")&& !ua.contains("360CHE")&& !ua.contains("Windows")&& !ua.contains("Macintosh"))
      return 12
    else if(ua.contains("iPhone")&&ua.contains("360CHE"))
      return 13
    else if(ua.contains("Android")&&ua.contains("360CHE"))
      return 14
    else
      return 0
  }

  /*从mysql查询得到t_type数据*/
  def GetType_M(ss:SparkSession): DataFrame =
  {
    //          val mysqlurl="jdbc:mysql://************:3306/db_hitsstatistic?serverTimezone=Asia/Shanghai"
    val rdd= dao.MySparkSession.conn().read.format("jdbc").option("url",mysql_conn.mysqlurl)
      .option("dbtable","(select " +
        "  cast(substr(f_code,1,2) as SIGNED) as parentid,b.f_isdefault as defaultvalue" +
        // ",cast(substr(f_code,6,4) as SIGNED) as channlPagecode" +
        // ",cast(substr(f_code,3,3) as SIGNED) as channlcode" +
        " ,b.f_urlregular as urlregular" +
        " ,cast(a.F_code as SIGNED) as code from " +
        " t_type as a join t_typeregular as b on a.f_id=b.f_typeid " +
        " where a.f_isdelete=0 and b.f_isdelete=0 ) as t")
      .option("user",mysql_conn.user)
      .option("password",mysql_conn.password)
      .option("driver","com.mysql.jdbc.Driver")
      .load()
    return rdd
  }
}
