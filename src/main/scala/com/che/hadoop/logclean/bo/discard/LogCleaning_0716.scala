package com.che.hadoop.logclean.bo.discard

import java.net.URLDecoder
import java.text.SimpleDateFormat
import java.util.{Date, ResourceBundle}
import com.che.hadoop.logclean.dao.mysql_conn
import com.che.hadoop.underlay.dao
import com.che.hadoop.logclean.utils.{ConverPercent, DingRobot, function_}
import org.apache.spark.rdd
import org.apache.spark.sql.types.{IntegerType, StringType, StructField, StructType}
import org.apache.spark.sql.{DataFrame, Row, SparkSession}

/**
  * Created by zkpk on 1/4/19.
  * web日志清洗
  */
object LogCleaning_0716 {

  def main(args: Array[String]): Unit = {


    val str="Mozilla/5.0 (Linux; Android 10; GLK-AL00 Build/HUAWEIGLK-AL00; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/77.0.3865.120 MQQBrowser/6.2 TBS/045230 Mobile Safari/537.36 360CHESPEED/1.4.1 NETWORK/WIFI IMEI/6be23e06c92e89de PLATFORM/ANDROIDAPP USERID/0 DID/429bb0338b914c65b39321c4c6128207 DEVICE/GLK-AL00 SYSTEM/10 LONGITUDE/112.137265 LATITUDE/26.788861 CARCITYID/430400"


    val value=str.split("DID")

     var result= value(1).split(" ")(0).split("/")(1)
    println(result)

    val day ="20200714"//getDate(0,1)
    println(day)
    saveLogCleanToHive(day.substring(0,8))

  }


  val structTypeWeb = StructType(Array(
    StructField("res", StringType, true),
    StructField("k", StringType, true),
    StructField("vtc", StringType, true),
    StructField("i", StringType, true),
    StructField("vtf", StringType, true),
    StructField("vtl", StringType, true),
    StructField("uid", StringType, true),
    StructField("vid", StringType, true),
    StructField("sid", StringType, true),
    StructField("ip", StringType, true),
    StructField("ua", StringType, true),
    StructField("daytime", StringType, true),
    StructField("eventid", StringType, true),
    StructField("action", StringType, true),
    StructField("lable", StringType, true),
    StructField("code", StringType, true),
    StructField("ga_vid", StringType, true),
    StructField("dealers_uid", StringType, true),
    StructField("hours", IntegerType, true),
    StructField("day", StringType, true)
  ))

  /*清洗数据保存到hive */
  def saveLogCleanToHive(dayValue:String):Unit=
  {
    val tablename="t_logdata"
    var dirPath = "hdfs://nameservice1/pv-nginx/"+dayValue

    /*验证小时数据是否有漏跑*/
//    val df= dao.MySparkSession.conn.sql(s"select distinct hours from $tablename where day=$dayValue")
    var IsTrue=false


    if(!IsTrue)// 如果数据有漏跑，清洗全部日志
    {
      dirPath = "hdfs://nameservice1/pv-nginx/"+dayValue+"/*"
      dao.MySparkSession.conn().sql(s"ALTER TABLE $tablename DROP IF EXISTS PARTITION(day=$dayValue)")
//      println(s"ALTER TABLE $tablename DROP IF EXISTS PARTITION(day=$dayValue)")
      /*发布钉钉信息*/
      val myDingRobot=new DingRobot(ResourceBundle.getBundle("robot").getString("ding.monitor"))
      myDingRobot.sendAlert(s"ALTER TABLE $tablename DROP IF EXISTS PARTITION(day=$dayValue)")
    }
    //    dao.MySparkSession.conn.sqlContext.setConf("hive.exec.orc.default.block.size","134217728")
    val mySqlJson=getTypeByMsql(dao.MySparkSession.conn()).collect()

    /*得到已过滤的数据*/
    function_.getCarPropertyData(dao.MySparkSession.conn(),"select f_ip as ip from t_filter_Ip where date_format(f_updatetime,'%Y%m%d')="+dayValue).createOrReplaceTempView("t_filterIP")
    dao.MySparkSession.conn().conf.set("spark.sql.broadcastTimeout","1200")

    /*得到数据*/
    val logRdd = dao.MySparkSession.conn().read.json(dirPath).filter(t=>t.getAs[String]("status")=="200"
      && t.getAs[String]("args").split("&").length>=8  && t.getAs[String]("args").split("&").length<=15)

    val rowRDD=getWebRdd(logRdd,mySqlJson,dayValue)
      //转成DataFrame
    dao.MySparkSession.conn().sqlContext.createDataFrame(rowRDD,structTypeWeb).filter(t=>t.getAs[String]("day")==dayValue).createOrReplaceTempView("logclean")



    /*保存数据*/
    val saveDF=dao.MySparkSession.conn().sql(
      s"""select * from logclean
         |where
         |ip not in (select ip from (select ip,count(k) as t,k,i from logclean where i='' group by ip,k,i) s where t>=2000)
         |and ip not in (select ip from t_filterIP)""".stripMargin)//过滤来源为空，且访问数量大于2000的数据

//    if(!IsTrue)// 如果数据有漏跑，清洗全部日志
//    {
//      val hadoopConf = dao.MySparkSession.conn.sparkContext.hadoopConfiguration
//      val hdfs = org.apache.hadoop.fs.FileSystem.get(hadoopConf)
//      val path = new org.apache.hadoop.fs.Path(s"hdfs://nameservice1/apps/hive/warehouse/$tablename/day=$dayValue")
//      if (hdfs.exists(path)) {
//        println(path)
//        //为防止误删，禁止递归删除
//        hdfs.delete(path, true)
//      }
//    }
    saveDF.repartition(1).write.mode("append").format("Hive").partitionBy("day").saveAsTable(tablename)

    /*是否合并小文件*/
//    if(IsTrue)
//    {
//      /*合并小文件*/
//      dao.MySparkSession.conn.sql("set mapred.reduce.tasks=1")
//      val sql =
//        s"""insert overwrite table $tablename partition(day=$dayValue)
//           |select res,k,vtc,i,vtf,vtl,uid,vid,sid,ip,ua,daytime,eventid,action,lable,code,ga_vid,dealers_uid, case when hours is NULL then -1 else hours end hours
//           |from  $tablename where day=$dayValue  DISTRIBUTE BY rand()""".stripMargin
//      dao.MySparkSession.conn.sql(sql)
//    }

    /*过滤ip访问大于1000的 */
    dao.MySparkSession.conn().sql(s"select * from $tablename where day="+dayValue).createOrReplaceTempView("t_filter_data")
    val savemysqlDF=dao.MySparkSession.conn().sql("select k as f_url,ip as F_IP,t as f_count,f_updatetime from (select ip,count(k) as t,k,i ,from_unixtime(daytime/1000,'yyyy-MM-dd') as f_updatetime from t_filter_data " +
      "where i='' group by ip,k,i,from_unixtime(daytime/1000,'yyyy-MM-dd')) s where t>=2000") //来源为空，且访问数量大于2000的数据，保存到mysql
    com.che.hadoop.logclean.utils.function_.saveMysql(savemysqlDF,"t_filter_Ip")

    dao.MySparkSession.conn().close()
  }


  //返回参数值
  def getPValueByName(args: String,value: String): String = {
    var str:String=""
    val a1 =args
    val p1 = ("""(\?|\&)"""+value+"""=([^\&]+)""").r
    val arg=(p1 findAllIn a1 toList)
    if(arg.length>0&&arg(0).split("=").length>1)
    {
      str= arg(0).split("=")(1)
    }
    return str
  }

  //返回res参数值
  def getPValueByName_res(args: String,value: String): String = {
    var str:String=""
    val a1 =args
    val p1 = ("""(\\?|\\&)"""+value+"""=([^\\&]+)""").r
    val arg=(p1 findAllIn a1 toList)
    if(arg.length>0&&arg(0).split("=").length>1)
    {
      str= arg(0).split("=")(1)
    }
    return str
  }

  /*正则判断是否utf8转码*/
  def urlDecode(code:String ) :String={
    if (code.matches("^(?:[\\x00-\\x7f]|[\\xe0-\\xef][\\x80-\\xbf]{2})+$"))
      return URLDecoder.decode(code, "utf-8");
    else
      return code;
  }

  /*app获取vid*/
  def getAPPVid(vid:String,ua:String):String=
  {
    var result=vid
    if(ua.contains("360CHE")&&(ua.contains("iPhone")||ua.contains("Android")))
    {

      if(ua.contains("360CHESPEED"))
      {
        if (ua.contains("iPhone"))
        {
          result=getAppVidValue(vid, ua, "iPhone", "DID", 33)
        }
        else if (ua.contains("iPad")) {
          result = getAppVidValue(vid, ua, "iPad", "DID",33)
        }
        else if (ua.contains("Android"))
        {
          result=getAppVidValue(vid, ua, "Android", "DID", 37)
        }
      }
      else
      {
        if (ua.contains("iPhone"))
        {
          result=getAppVidValue(vid, ua, "iPhone", "DID", 33)
        }
        else if (ua.contains("iPad")) {
          result = getAppVidValue(vid, ua, "iPad", "DID",33)
        }
        else if (ua.contains("Android"))
        {
          result=getAppVidValue(vid, ua, "Android", "APPCID", 33)
        }
      }
      //          if(ua.contains("iPhone"))
      //          {
      //            val value=ua.split("DID")
      //            if(value.size>1)
      //            {
      //              result= value(1).substring(1,33).trim
      //            }
      //          }
      //          else if(ua.contains("Android"))
      //          {
      //            val value=ua.split("APPCID")
      //            if(value.size>1)
      //            {
      //              result= value(1).substring(1,37).trim
      //            }
      //          }
    }
    return result
  }

  /*app获取vid*/
  def getAppVidValue(vid:String,ua:String,appType:String,billtype:String,length:Int):String=
  {
    var result=vid
    if(ua.contains(appType))
    {
      val value=ua.split(billtype)
      if(value.size>1)
      {
        result= value(1).split(" ")(0).split("/")(1).trim
      }
    }
    return result
  }

  /*app获取Uid*/
  def getAPPUid(uid:String,ua:String):String=
  {
    var result=uid
    if(ua.contains("360CHE")&&ua.contains("USERID")&&(ua.contains("iPhone")||ua.contains("Android") || ua.contains("iPad")))
    {
      val value=ua.split("USERID")
      if(value.size>1)
      {
        val resultuid=value(1).split("\\s")
        val appuid=resultuid(0).substring(1).trim
        if(appuid!="0")
        {
          result=appuid
        }
      }
    }
    return result
  }

  /*从mysql查询得到t_type数据*/
  def getTypeByMsql(ss:SparkSession): DataFrame =
  {
    //          val mysqlurl="****************************************************************************"
    val rdd= ss.read.format("jdbc").option("url",mysql_conn.mysqlurl)
      .option("dbtable","(select " +
        "  cast(substr(f_code,1,2) as SIGNED) as parentid,b.f_isdefault as defaultvalue" +
        // ",cast(substr(f_code,6,4) as SIGNED) as channlPagecode" +
        // ",cast(substr(f_code,3,3) as SIGNED) as channlcode" +
        " ,b.f_urlregular as urlregular" +
        " ,cast(a.F_code as SIGNED) as code from " +
        " t_type as a join t_typeregular as b on a.f_id=b.f_typeid " +
        " where a.f_isdelete=0 and b.f_isdelete=0 ) as t")
      .option("user",mysql_conn.user)
      .option("password",mysql_conn.password)
      .option("driver","com.mysql.jdbc.Driver")
      .load()
    return rdd
  }

  /* 得到t_type code码*/
  def getCode(ua:String,value:String,arrays: Array[Row]):String=
  {
    var code="0"
    // var array=arrays.filter(it=> it.getAs[Long]("channlPagecode")>1000 && it.getAs[Long]("channlPagecode")<10000 && GetRegular(value,it.getAs("urlregular")))  //正则匹配channlpage
    //  if(array.length<=0) {
    var array = arrays.filter(it =>com.che.hadoop.logclean.utils.function_.GetRegular(value, it.getAs("urlregular"))) //正则匹配channl
    //  array=arrayvt
    // }

    if(array!=null &&array.length>0)//是否正则匹配成功
    {
      if(array.length>1) //如果正则匹配数量>1
      {
        val vt=getUa(ua)
        val array_1=array.filter(it=>it.getAs("parentid")==vt)//匹配ua
        if(array_1.length>0)
        {
          val array_sort=  array_1.sortBy(r => (r.getAs[Long]("code"))).reverse //降序排序， 然后取第一条赋值
          code=array_sort(0).getAs[Long]("code").toString
        }
        else
        {
          var array_2=array.filter(it=>it.getAs("defaultvalue")==1) //匹配默认值
          if(array_2.length>0)
          {
            val array_sort= array_2. sortBy(r => (r.getAs[Long]("code"))).reverse //降序排序， 然后取第一条赋值
            code=array_sort(0).getAs[Long]("code").toString
          }
        }
      }
      else
      {
        code=array(0).getAs[Long]("code").toString
      }
    }
    return  code
  }

  /*匹配user_agent判断vt 1 pc,2,ios,3.anrod,4 m站 5 微信端*/
  def  getUa(ua:String):Int={
    if(ua.contains("Windows")|| ua.contains("Macintosh"))
      return 11
    else if(ua.contains("MicroMessenger"))
      return 15
    else if(!ua.contains("MicroMessenger")&& !ua.contains("360CHE")&& !ua.contains("Windows")&& !ua.contains("Macintosh"))
      return 12
    else if((ua.contains("iPhone") || ua.contains("iPad"))&&ua.contains("360CHE"))
      return 13
    else if(ua.contains("Android")&&ua.contains("360CHE"))
      return 14
    else
      return 0
  }


  /*etl清洗*/
  def getWebRdd(logRdd:DataFrame,mySqlJson:Array[Row],dayValue:String):rdd.RDD[Row]=
  {
    //转换日志格式
    val rowRDD = logRdd.rdd.map(line => {

      var res="" ;var k="" ;var vtc=""; var i=""; var vtf="" ;var vtl="" ;var uid=""; var vid=""; var sid=""; var ip=""
      var ua="" ;var daytime="" ;var eventid="";var action="" ;var lable=""; var ga_vid=""; var dealers_uid="";  var day=""
      var hours=0; var code=""
      try{
        val args = line.getAs[String]("args").split("&")
        val days = getHourOrDayByTimeStamp(line.getAs[String]("@timestamp"),"yyyyMMdd")
        val timestamp=getTimeStampToDate(getPValueByName(line.getAs[String]("args"), "vtc")+"000","yyyyMMdd")
        if(days==dayValue&&timestamp==dayValue)
        {
          res = getPValueByName_res(line.getAs[String]("args"), "res")
          k = URLDecoder.decode(ConverPercent.convertPercent(URLDecoder.decode(getPValueByName(line.getAs[String]("args"), "k"), "UTF-8")),"UTF-8")
          vtc = getPValueByName(line.getAs[String]("args"), "vtc")
          i = URLDecoder.decode(ConverPercent.convertPercent(URLDecoder.decode(getPValueByName(line.getAs[String]("args"), "i"), "UTF-8")),"UTF-8")
          vtf = getPValueByName(line.getAs[String]("args"), "vtf")
          vtl = getPValueByName(line.getAs[String]("args"), "vtl")
          uid = if (getPValueByName(line.getAs[String]("args"), "uid") == "delete") "" else getAPPUid(getPValueByName(line.getAs[String]("args"), "uid"), line.getAs[String]("user_agent"))
          vid = getAPPVid(getPValueByName(line.getAs[String]("args"), "vid"), line.getAs[String]("user_agent"))
          sid = getPValueByName(line.getAs[String]("args"), "sid")
          ip = line.getAs[String]("clientip")
          ua = line.getAs[String]("user_agent")
          hours = getHourOrDayByTimeStamp(line.getAs[String]("@timestamp"),"HH").toInt
          daytime = getTimeTotimeStamp(line.getAs[String]("@timestamp")).toString()
          eventid = if (args.length >= 13) getPValueByName(line.getAs[String]("args"), "eventid") else ""
          action = if (args.length >= 13) URLDecoder.decode(getPValueByName(line.getAs[String]("args"), "action"), "UTF-8") else ""
          lable = if (args.length >= 13) URLDecoder.decode(getPValueByName(line.getAs[String]("args"), "lable"), "UTF-8") else ""
          ga_vid = if (args.length >= 14) getPValueByName(line.getAs[String]("args"), "ga_vid") else ""
          dealers_uid = if (args.length >= 15) getPValueByName(line.getAs[String]("args"), "dealers_uid") else ""
          code = getCode(line.getAs[String]("user_agent"), k, mySqlJson)
          day = getHourOrDayByTimeStamp(line.getAs[String]("@timestamp"),"yyyyMMdd")
          //      Row(res, k, vtc, i, vtf, vtl, uid, vid,sid,ip,ua,daytime, eventid,action,lable,ga_vid,dealers_uid, day)
        }
      } catch {
        case ex: Exception => {
          println(ex)
        }

      }
      Row(res, k, vtc, i, vtf, vtl, uid, vid, sid, ip, ua, daytime, eventid, action, lable, code, ga_vid, dealers_uid, hours, day)
    })
    return rowRDD
  }

  //      /*时间戳转日期*/
  //      def getTimeStampToDate(tm:String) :String={
  //        val fm = new SimpleDateFormat("yyyyMMdd")
  //        val tim = fm.format(new Date(tm.toLong))
  //        tim
  //      }
  //
  //      /*时间戳转小时*/
  //      def getTimeStampToHour(tm:String) :String={
  //        val fm = new SimpleDateFormat("HH")
  //        val tim = fm.format(new Date(tm.toLong))
  //        tim
  //      }

  /*时间戳转换*/
  def getTimeStampToDate(tm:String,formatType:String) :String={
    val fm = new SimpleDateFormat(formatType)
    val tim = fm.format(new Date(tm.toLong))
    tim
  }

  /*日期类型转换时间戳*/
  def getTimeTotimeStamp(tm: String): Long = {
    val dateFormat: SimpleDateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssX")
    val dt = dateFormat.parse(tm)
    //val aa = dateFormat.format(dt)
    val tim: Long = dt.getTime()
    // println(tim)
    tim
  }

  //      /*根据日期得到小时的字符串数据*/
  //      def getHourByTimeStamp(date:String):String={
  //        val dateFormat: SimpleDateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssX")
  //        val dateHour: SimpleDateFormat = new SimpleDateFormat("HH")
  //        val dt = dateFormat.parse(date)
  //        val hours = dateHour.format(dt.getTime())
  //        return hours
  //      }
  //
  //      /*根据日期得到类型为20191010的字符串数据*/
  //      def getDayByTimeStamp(date:String):String={
  //        val dateFormat: SimpleDateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssX")
  //        val dateHour: SimpleDateFormat = new SimpleDateFormat("yyyyMMdd")
  //        val dt = dateFormat.parse(date)
  //        val days = dateHour.format(dt.getTime())
  //        return days
  //      }

  /*日期类型转换天，小时*/
  def getHourOrDayByTimeStamp(date: String,formatType:String): String = {
    val dateFormat: SimpleDateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssX")
    val dateHour: SimpleDateFormat = new SimpleDateFormat(formatType)
    val dt = dateFormat.parse(date)
    val hours = dateHour.format(dt.getTime())
    return hours
  }
}
