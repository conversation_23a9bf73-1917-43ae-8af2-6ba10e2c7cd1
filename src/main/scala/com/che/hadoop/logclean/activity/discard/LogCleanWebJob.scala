package com.che.hadoop.logclean.activity.discard

import java.text.SimpleDateFormat
import java.util.Calendar

import com.che.hadoop.logclean.bo.LogCleanWeb
import com.che.hadoop.underlay.dao.MySparkSession
import com.che.hadoop.underlay.tool.date.DateTranslate

/**
  * Created by zkpk on 1/11/19.
  * web日志清洗，小时任务改成定时任务
  */

object LogCleanWebJob {
  def main(args: Array[String]): Unit = {
    var day=DateTranslate.getDate(1)
    if(args.length>0){
       day=args(0)
    }
    val spark=MySparkSession.conn()
    LogCleanWeb.saveLogCleanToHive(spark,day) /*清洗数据到hive*/
    spark.stop()


  }

  /*
   得到当前日期前一天
   */
  def getDate(day: Int,hour: Int): String = {
    val dateFormat: SimpleDateFormat = new SimpleDateFormat("yyyyMMddHH")
    var cal: Calendar = Calendar.getInstance()
    cal.add(Calendar.DATE, -day)
    cal.add(Calendar.HOUR, -hour)
    val date = dateFormat.format(cal.getTime())
    return date

  }


}
