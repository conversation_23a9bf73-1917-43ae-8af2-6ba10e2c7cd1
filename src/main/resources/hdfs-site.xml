<?xml version="1.0" ?>
<!-- Created at 2022-10-13 01:19:35.990 -->
<configuration>
  <property>
    <name>dfs.datanode.cache.revocation.timeout.ms</name>
    <value>900000</value>
  </property>
  <property>
    <name>dfs.namenode.resource.check.interval</name>
    <value>5000</value>
  </property>
  <property>
    <name>dfs.replication</name>
    <value>2</value>
  </property>
  <property>
    <name>dfs.namenode.write-lock-reporting-threshold-ms</name>
    <value>5000</value>
  </property>
  <property>
    <name>dfs.namenode.avoid.read.stale.datanode</name>
    <value>false</value>
  </property>
  <property>
    <name>dfs.namenode.lease-recheck-interval-ms</name>
    <value>2000</value>
  </property>
  <property>
    <name>dfs.client.block.write.locateFollowingBlock.initial.delay.ms</name>
    <value>400</value>
  </property>
  <property>
    <name>dfs.mover.max-no-move-interval</name>
    <value>60000</value>
  </property>
  <property>
    <name>dfs.hosts.exclude</name>
    <value>/etc/taihao-apps/hadoop-conf/dfs.exclude</value>
  </property>
  <property>
    <name>dfs.namenode.replication.min</name>
    <value>1</value>
  </property>
  <property>
    <name>dfs.datanode.directoryscan.threads</name>
    <value>1</value>
  </property>
  <property>
    <name>dfs.datanode.directoryscan.interval</name>
    <value>21600</value>
  </property>
  <property>
    <name>dfs.namenode.acls.enabled</name>
    <value>false</value>
  </property>
  <property>
    <name>dfs.client.short.circuit.replica.stale.threshold.ms</name>
    <value>1800000</value>
  </property>
  <property>
    <name>dfs.namenode.resource.du.reserved</name>
    <value>21474836480</value>
  </property>
  <property>
    <name>dfs.namenode.datanode.registration.ip-hostname-check</name>
    <value>false</value>
  </property>
  <property>
    <name>dfs.namenode.path.based.cache.block.map.allocation.percent</name>
    <value>0.25</value>
  </property>
  <property>
    <name>dfs.client.server-defaults.validity.period.ms</name>
    <value>3600000</value>
  </property>
  <property>
    <name>dfs.webhdfs.use.ipc.callq</name>
    <value>true</value>
  </property>
  <property>
    <name>dfs.namenode.edits.noeditlogchannelflush</name>
    <value>false</value>
  </property>
  <property>
    <name>dfs.datanode.cache.revocation.polling.ms</name>
    <value>500</value>
  </property>
  <property>
    <name>dfs.datanode.available-space-volume-choosing-policy.balanced-space-preference-fraction
    </name>
    <value>0.75f</value>
  </property>
  <property>
    <name>dfs.namenode.audit.loggers</name>
    <value>default</value>
  </property>
  <property>
    <name>dfs.http.policy</name>
    <value>HTTP_ONLY</value>
  </property>
  <property>
    <name>dfs.namenode.replication.interval</name>
    <value>3</value>
  </property>
  <property>
    <name>dfs.namenode.safemode.min.datanodes</name>
    <value>0</value>
  </property>
  <property>
    <name>dfs.client.file-block-storage-locations.num-threads</name>
    <value>10</value>
  </property>
  <property>
    <name>nfs.dump.dir</name>
    <value>/tmp/.hdfs-nfs</value>
  </property>
  <property>
    <name>dfs.namenode.metrics.logger.period.seconds</name>
    <value>600</value>
  </property>
  <property>
    <name>dfs.balancer.max-iteration-time</name>
    <value>1200000</value>
  </property>
  <property>
    <name>dfs.webhdfs.enabled</name>
    <value>true</value>
  </property>
  <property>
    <name>dfs.webhdfs.socket.read-timeout</name>
    <value>60s</value>
  </property>
  <property>
    <name>dfs.client.use.datanode.hostname</name>
    <value>false</value>
  </property>
  <property>
    <name>dfs.namenode.delegation.token.max-lifetime</name>
    <value>604800000</value>
  </property>
  <property>
    <name>dfs.datanode.drop.cache.behind.writes</name>
    <value>false</value>
  </property>
  <property>
    <name>dfs.namenode.avoid.write.stale.datanode</name>
    <value>false</value>
  </property>
  <property>
    <name>dfs.namenode.num.extra.edits.retained</name>
    <value>1000000</value>
  </property>
  <property>
    <name>dfs.datanode.data.dir</name>
    <value>/mnt/disk1/hdfs,/mnt/disk2/hdfs,/mnt/disk3/hdfs,/mnt/disk4/hdfs</value>
  </property>
  <property>
    <name>dfs.namenode.edekcacheloader.initial.delay.ms</name>
    <value>3000</value>
  </property>
  <property>
    <name>nfs.rtmax</name>
    <value>1048576</value>
  </property>
  <property>
    <name>dfs.client.mmap.cache.size</name>
    <value>256</value>
  </property>
  <property>
    <name>dfs.datanode.data.dir.perm</name>
    <value>750</value>
  </property>
  <property>
    <name>dfs.ha.zkfc.nn.http.timeout.ms</name>
    <value>20000</value>
  </property>
  <property>
    <name>dfs.namenode.max-lock-hold-to-release-lease-ms</name>
    <value>25</value>
  </property>
  <property>
    <name>dfs.client.datanode-restart.timeout</name>
    <value>30</value>
  </property>
  <property>
    <name>dfs.datanode.readahead.bytes</name>
    <value>4194304</value>
  </property>
  <property>
    <name>dfs.namenode.xattrs.enabled</name>
    <value>true</value>
  </property>
  <property>
    <name>dfs.client-write-packet-size</name>
    <value>65536</value>
  </property>
  <property>
    <name>dfs.datanode.bp-ready.timeout</name>
    <value>20</value>
  </property>
  <property>
    <name>dfs.datanode.transfer.socket.send.buffer.size</name>
    <value>0</value>
  </property>
  <property>
    <name>dfs.namenode.checkpoint.txns</name>
    <value>1000000</value>
  </property>
  <property>
    <name>dfs.journalnode.edits.dir</name>
    <value>/mnt/disk1/hdfs/journal</value>
  </property>
  <property>
    <name>dfs.client.block.write.retries</name>
    <value>3</value>
  </property>
  <property>
    <name>dfs.namenode.list.openfiles.num.responses</name>
    <value>1000</value>
  </property>
  <property>
    <name>httpfs.buffer.size</name>
    <value>4096</value>
  </property>
  <property>
    <name>dfs.namenode.safemode.threshold-pct</name>
    <value>0.999f</value>
  </property>
  <property>
    <name>dfs.cachereport.intervalMsec</name>
    <value>10000</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-bind-host</name>
    <value>0.0.0.0</value>
  </property>
  <property>
    <name>dfs.namenode.list.cache.directives.num.responses</name>
    <value>100</value>
  </property>
  <property>
    <name>dfs.namenode.replication.max-streams</name>
    <value>100</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.hdfs-cluster.nn1</name>
    <value>master-1-1.c-f7ba4ab31b10bfa1.cn-beijing.emr.aliyuncs.com:8020</value>
  </property>
  <property>
    <name>dfs.webhdfs.socket.connect-timeout</name>
    <value>60s</value>
  </property>
  <property>
    <name>nfs.allow.insecure.ports</name>
    <value>true</value>
  </property>
  <property>
    <name>dfs.client.write.exclude.nodes.cache.expiry.interval.millis</name>
    <value>600000</value>
  </property>
  <property>
    <name>dfs.client.mmap.cache.timeout.ms</name>
    <value>3600000</value>
  </property>
  <property>
    <name>dfs.nameservices</name>
    <value>hdfs-cluster</value>
  </property>
  <property>
    <name>dfs.http.address</name>
    <value>0.0.0.0:50070</value>
  </property>
  <property>
    <name>dfs.http.client.retry.policy.enabled</name>
    <value>false</value>
  </property>
  <property>
    <name>dfs.client.failover.connection.retries.on.timeouts</name>
    <value>0</value>
  </property>
  <property>
    <name>dfs.namenode.replication.work.multiplier.per.iteration</name>
    <value>100</value>
  </property>
  <property>
    <name>dfs.datanode.slow.io.warning.threshold.ms</name>
    <value>300</value>
  </property>
  <property>
    <name>dfs.namenode.reject-unresolved-dn-topology-mapping</name>
    <value>false</value>
  </property>
  <property>
    <name>dfs.datanode.drop.cache.behind.reads</name>
    <value>false</value>
  </property>
  <property>
    <name>dfs.image.compression.codec</name>
    <value>org.apache.hadoop.io.compress.DefaultCodec</value>
  </property>
  <property>
    <name>dfs.client.read.shortcircuit.streams.cache.size</name>
    <value>256</value>
  </property>
  <property>
    <name>dfs.namenode.upgrade.domain.factor</name>
    <value>${dfs.replication}</value>
  </property>
  <property>
    <name>dfs.xframe.value</name>
    <value>SAMEORIGIN</value>
  </property>
  <property>
    <name>dfs.namenode.replication.max-streams-hard-limit</name>
    <value>100</value>
  </property>
  <property>
    <name>dfs.namenode.top.num.users</name>
    <value>10</value>
  </property>
  <property>
    <name>dfs.namenode.accesstime.precision</name>
    <value>3600000</value>
  </property>
  <property>
    <name>mapreduce.job.acl-view-job</name>
    <value>*</value>
  </property>
  <property>
    <name>dfs.namenode.fs-limits.max-xattrs-per-inode</name>
    <value>32</value>
  </property>
  <property>
    <name>dfs.image.transfer.timeout</name>
    <value>60000</value>
  </property>
  <property>
    <name>dfs.namenode.lifeline.rpc-bind-host</name>
    <value>0.0.0.0</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.hdfs-cluster.nn2</name>
    <value>master-1-2.c-f7ba4ab31b10bfa1.cn-beijing.emr.aliyuncs.com:8020</value>
  </property>
  <property>
    <name>nfs.wtmax</name>
    <value>1048576</value>
  </property>
  <property>
    <name>dfs.namenode.edit.log.autoroll.check.interval.ms</name>
    <value>300000</value>
  </property>
  <property>
    <name>dfs.namenode.support.allow.format</name>
    <value>true</value>
  </property>
  <property>
    <name>hadoop.hdfs.configuration.version</name>
    <value>1</value>
  </property>
  <property>
    <name>dfs.stream-buffer-size</name>
    <value>4096</value>
  </property>
  <property>
    <name>dfs.namenode.invalidate.work.pct.per.iteration</name>
    <value>0.32f</value>
  </property>
  <property>
    <name>dfs.namenode.top.enabled</name>
    <value>true</value>
  </property>
  <property>
    <name>dfs.bytes-per-checksum</name>
    <value>512</value>
  </property>
  <property>
    <name>dfs.namenode.max.objects</name>
    <value>0</value>
  </property>
  <property>
    <name>dfs.datanode.max.transfer.threads</name>
    <value>8192</value>
  </property>
  <property>
    <name>dfs.client.read.shortcircuit</name>
    <value>true</value>
  </property>
  <property>
    <name>dfs.datanode.directoryscan.throttle.limit.ms.per.sec</name>
    <value>1000</value>
  </property>
  <property>
    <name>dfs.datanode.hdfs-blocks-metadata.enabled</name>
    <value>true</value>
  </property>
  <property>
    <name>dfs.image.transfer.chunksize</name>
    <value>65536</value>
  </property>
  <property>
    <name>dfs.namenode.list.encryption.zones.num.responses</name>
    <value>100</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.hdfs-cluster.nn2</name>
    <value>master-1-2.c-f7ba4ab31b10bfa1.cn-beijing.emr.aliyuncs.com:8021</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.hdfs-cluster.nn1</name>
    <value>master-1-1.c-f7ba4ab31b10bfa1.cn-beijing.emr.aliyuncs.com:8021</value>
  </property>
  <property>
    <name>dfs.client.failover.sleep.base.millis</name>
    <value>500</value>
  </property>
  <property>
    <name>dfs.namenode.decommission.interval</name>
    <value>30</value>
  </property>
  <property>
    <name>dfs.namenode.path.based.cache.refresh.interval.ms</name>
    <value>30000</value>
  </property>
  <property>
    <name>dfs.permissions.superusergroup</name>
    <value>hadoop</value>
  </property>
  <property>
    <name>dfs.ha.log-roll.period</name>
    <value>120</value>
  </property>
  <property>
    <name>dfs.namenode.checkpoint.edits.dir</name>
    <value>${dfs.namenode.checkpoint.dir}</value>
  </property>
  <property>
    <name>dfs.user.home.dir.prefix</name>
    <value>/user</value>
  </property>
  <property>
    <name>dfs.client.socket.send.buffer.size</name>
    <value>0</value>
  </property>
  <property>
    <name>dfs.blockreport.initialDelay</name>
    <value>0</value>
  </property>
  <property>
    <name>dfs.namenode.inotify.max.events.per.rpc</name>
    <value>1000</value>
  </property>
  <property>
    <name>dfs.namenode.safemode.extension</name>
    <value>30000</value>
  </property>
  <property>
    <name>dfs.client.failover.sleep.max.millis</name>
    <value>15000</value>
  </property>
  <property>
    <name>dfs.http.client.failover.sleep.base.millis</name>
    <value>500</value>
  </property>
  <property>
    <name>dfs.xframe.enabled</name>
    <value>true</value>
  </property>
  <property>
    <name>dfs.namenode.delegation.key.update-interval</name>
    <value>86400000</value>
  </property>
  <property>
    <name>dfs.datanode.transfer.socket.recv.buffer.size</name>
    <value>0</value>
  </property>
  <property>
    <name>fs.permissions.umask-mode</name>
    <value>026</value>
  </property>
  <property>
    <name>dfs.namenode.fs-limits.max-xattr-size</name>
    <value>16384</value>
  </property>
  <property>
    <name>dfs.http.client.failover.sleep.max.millis</name>
    <value>15000</value>
  </property>
  <property>
    <name>dfs.namenode.blocks.per.postponedblocks.rescan</name>
    <value>10000</value>
  </property>
  <property>
    <name>dfs.lock.suppress.warning.interval</name>
    <value>10s</value>
  </property>
  <property>
    <name>dfs.webhdfs.ugi.expire.after.access</name>
    <value>600000</value>
  </property>
  <property>
    <name>dfs.namenode.hosts.provider.classname</name>
    <value>org.apache.hadoop.hdfs.server.blockmanagement.HostFileManager</value>
  </property>
  <property>
    <name>dfs.balancer.keytab.enabled</name>
    <value>false</value>
  </property>
  <property>
    <name>dfs.client.block.write.replace-datanode-on-failure.enable</name>
    <value>true</value>
  </property>
  <property>
    <name>dfs.namenode.lifeline.handler.ratio</name>
    <value>0.10</value>
  </property>
  <property>
    <name>dfs.client.use.legacy.blockreader.local</name>
    <value>false</value>
  </property>
  <property>
    <name>dfs.namenode.checkpoint.dir</name>
    <value>file:///mnt/disk1/hdfs/namesecondary</value>
  </property>
  <property>
    <name>dfs.namenode.top.windows.minutes</name>
    <value>1,5,25</value>
  </property>
  <property>
    <name>dfs.webhdfs.rest-csrf.browser-useragents-regex</name>
    <value>^Mozilla.*,^Opera.*</value>
  </property>
  <property>
    <name>nfs.mountd.port</name>
    <value>4242</value>
  </property>
  <property>
    <name>dfs.storage.policy.enabled</name>
    <value>true</value>
  </property>
  <property>
    <name>dfs.namenode.list.cache.pools.num.responses</name>
    <value>100</value>
  </property>
  <property>
    <name>nfs.server.port</name>
    <value>2049</value>
  </property>
  <property>
    <name>dfs.client.failover.proxy.provider.hdfs-cluster</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>
  <property>
    <name>dfs.datanode.balance.max.concurrent.moves</name>
    <value>20</value>
  </property>
  <property>
    <name>dfs.datanode.block-pinning.enabled</name>
    <value>false</value>
  </property>
  <property>
    <name>dfs.datanode.imbalance.threshold</name>
    <value>10</value>
  </property>
  <property>
    <name>dfs.namenode.num.checkpoints.retained</name>
    <value>2</value>
  </property>
  <property>
    <name>dfs.encrypt.data.transfer.cipher.key.bitlength</name>
    <value>128</value>
  </property>
  <property>
    <name>dfs.ha.namenodes.hdfs-cluster</name>
    <value>nn1,nn2</value>
  </property>
  <property>
    <name>dfs.client.mmap.retry.timeout.ms</name>
    <value>300000</value>
  </property>
  <property>
    <name>dfs.datanode.sync.behind.writes</name>
    <value>false</value>
  </property>
  <property>
    <name>dfs.namenode.fslock.fair</name>
    <value>true</value>
  </property>
  <property>
    <name>dfs.permissions.enabled</name>
    <value>false</value>
  </property>
  <property>
    <name>dfs.blockreport.split.threshold</name>
    <value>1000000</value>
  </property>
  <property>
    <name>dfs.block.scanner.volume.bytes.per.second</name>
    <value>1048576</value>
  </property>
  <property>
    <name>dfs.datanode.balance.bandwidthPerSec</name>
    <value>104857600</value>
  </property>
  <property>
    <name>dfs.namenode.balancer.request.standby</name>
    <value>true</value>
  </property>
  <property>
    <name>dfs.namenode.stale.datanode.interval</name>
    <value>30000</value>
  </property>
  <property>
    <name>dfs.domain.socket.path</name>
    <value>/var/lib/hadoop-hdfs/dn_socket</value>
  </property>
  <property>
    <name>dfs.namenode.decommission.blocks.per.interval</name>
    <value>500000</value>
  </property>
  <property>
    <name>dfs.namenode.handler.count</name>
    <value>50</value>
  </property>
  <property>
    <name>dfs.image.transfer.bandwidthPerSec</name>
    <value>0</value>
  </property>
  <property>
    <name>dfs.replication.max</name>
    <value>512</value>
  </property>
  <property>
    <name>dfs.namenode.name.dir</name>
    <value>file:///mnt/disk1/hdfs/name</value>
  </property>
  <property>
    <name>dfs.namenode.read-lock-reporting-threshold-ms</name>
    <value>5000</value>
  </property>
  <property>
    <name>dfs.datanode.failed.volumes.tolerated</name>
    <value>0</value>
  </property>
  <property>
    <name>dfs.client.block.write.replace-datanode-on-failure.min-replication</name>
    <value>0</value>
  </property>
  <property>
    <name>dfs.client.domain.socket.data.traffic</name>
    <value>false</value>
  </property>
  <property>
    <name>dfs.block.access.token.enable</name>
    <value>true</value>
  </property>
  <property>
    <name>dfs.webhdfs.rest-csrf.methods-to-ignore</name>
    <value>GET,OPTIONS,HEAD,TRACE</value>
  </property>
  <property>
    <name>dfs.blocksize</name>
    <value>134217728</value>
  </property>
  <property>
    <name>dfs.encrypt.data.transfer</name>
    <value>false</value>
  </property>
  <property>
    <name>dfs.namenode.write.stale.datanode.ratio</name>
    <value>0.5f</value>
  </property>
  <property>
    <name>dfs.datanode.available-space-volume-choosing-policy.balanced-space-threshold
    </name>
    <value>10737418240</value>
  </property>
  <property>
    <name>dfs.datanode.shared.file.descriptor.paths</name>
    <value>/dev/shm,/tmp</value>
  </property>
  <property>
    <name>dfs.short.circuit.shared.memory.watcher.interrupt.check.ms</name>
    <value>60000</value>
  </property>
  <property>
    <name>dfs.data.transfer.protection</name>
    <value>integrity</value>
  </property>
  <property>
    <name>dfs.webhdfs.rest-csrf.custom-header</name>
    <value>X-XSRF-HEADER</value>
  </property>
  <property>
    <name>dfs.datanode.handler.count</name>
    <value>30</value>
  </property>
  <property>
    <name>dfs.client.failover.max.attempts</name>
    <value>15</value>
  </property>
  <property>
    <name>dfs.balancer.max-no-move-interval</name>
    <value>60000</value>
  </property>
  <property>
    <name>dfs.client.read.shortcircuit.streams.cache.expiry.ms</name>
    <value>300000</value>
  </property>
  <property>
    <name>dfs.namenode.block-placement-policy.default.prefer-local-node</name>
    <value>true</value>
  </property>
  <property>
    <name>dfs.client.read.shortcircuit.skip.checksum</name>
    <value>false</value>
  </property>
  <property>
    <name>dfs.namenode.resource.checked.volumes.minimum</name>
    <value>1</value>
  </property>
  <property>
    <name>dfs.http.client.retry.max.attempts</name>
    <value>10</value>
  </property>
  <property>
    <name>dfs.namenode.max.full.block.report.leases</name>
    <value>6</value>
  </property>
  <property>
    <name>dfs.namenode.quota.init-threads</name>
    <value>4</value>
  </property>
  <property>
    <name>dfs.namenode.max.extra.edits.segments.retained</name>
    <value>10000</value>
  </property>
  <property>
    <name>dfs.webhdfs.user.provider.user.pattern</name>
    <value>^[A-Za-z_][A-Za-z0-9._-]*[$]?$</value>
  </property>
  <property>
    <name>dfs.datanode.metrics.logger.period.seconds</name>
    <value>600</value>
  </property>
  <property>
    <name>dfs.client.mmap.enabled</name>
    <value>true</value>
  </property>
  <property>
    <name>dfs.client.file-block-storage-locations.timeout.millis</name>
    <value>60000</value>
  </property>
  <property>
    <name>dfs.datanode.block.id.layout.upgrade.threads</name>
    <value>12</value>
  </property>
  <property>
    <name>dfs.datanode.use.datanode.hostname</name>
    <value>false</value>
  </property>
  <property>
    <name>dfs.client.context</name>
    <value>default</value>
  </property>
  <property>
    <name>hadoop.fuse.timer.period</name>
    <value>5</value>
  </property>
  <property>
    <name>dfs.balancer.address</name>
    <value>0.0.0.0:0</value>
  </property>
  <property>
    <name>dfs.namenode.lock.detailed-metrics.enabled</name>
    <value>false</value>
  </property>
  <property>
    <name>dfs.namenode.delegation.token.renew-interval</name>
    <value>86400000</value>
  </property>
  <property>
    <name>dfs.namenode.retrycache.heap.percent</name>
    <value>0.03f</value>
  </property>
  <property>
    <name>dfs.reformat.disabled</name>
    <value>false</value>
  </property>
  <property>
    <name>dfs.blockreport.intervalMsec</name>
    <value>21600000</value>
  </property>
  <property>
    <name>dfs.image.transfer-bootstrap-standby.bandwidthPerSec</name>
    <value>0</value>
  </property>
  <property>
    <name>dfs.balancer.block-move.timeout</name>
    <value>600000</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-bind-host</name>
    <value>0.0.0.0</value>
  </property>
  <property>
    <name>dfs.image.compress</name>
    <value>false</value>
  </property>
  <property>
    <name>dfs.namenode.edekcacheloader.interval.ms</name>
    <value>1000</value>
  </property>
  <property>
    <name>dfs.client.failover.connection.retries</name>
    <value>0</value>
  </property>
  <property>
    <name>dfs.namenode.edit.log.autoroll.multiplier.threshold</name>
    <value>2.0</value>
  </property>
  <property>
    <name>dfs.namenode.top.window.num.buckets</name>
    <value>10</value>
  </property>
  <property>
    <name>dfs.http.client.failover.max.attempts</name>
    <value>15</value>
  </property>
  <property>
    <name>dfs.namenode.checkpoint.check.period</name>
    <value>60</value>
  </property>
  <property>
    <name>dfs.client.slow.io.warning.threshold.ms</name>
    <value>30000</value>
  </property>
  <property>
    <name>dfs.namenode.http-bind-host</name>
    <value>0.0.0.0</value>
  </property>
  <property>
    <name>dfs.datanode.max.locked.memory</name>
    <value>0</value>
  </property>
  <property>
    <name>dfs.namenode.retrycache.expirytime.millis</name>
    <value>600000</value>
  </property>
  <property>
    <name>dfs.client.block.write.locateFollowingBlock.retries</name>
    <value>8</value>
  </property>
  <property>
    <name>dfs.client.block.write.replace-datanode-on-failure.best-effort</name>
    <value>false</value>
  </property>
  <property>
    <name>dfs.datanode.scan.period.hours</name>
    <value>504</value>
  </property>
  <property>
    <name>dfs.namenode.service.handler.count</name>
    <value>30</value>
  </property>
  <property>
    <name>dfs.ha.fencing.methods</name>
    <value>shell(/bin/true)</value>
  </property>
  <property>
    <name>dfs.webhdfs.rest-csrf.enabled</name>
    <value>false</value>
  </property>
  <property>
    <name>dfs.namenode.enable.retrycache</name>
    <value>true</value>
  </property>
  <property>
    <name>dfs.namenode.edits.asynclogging</name>
    <value>true</value>
  </property>
  <property>
    <name>dfs.datanode.du.reserved</name>
    <value>8589934592</value>
  </property>
  <property>
    <name>dfs.client.block.write.replace-datanode-on-failure.policy</name>
    <value>DEFAULT</value>
  </property>
  <property>
    <name>dfs.namenode.path.based.cache.retry.interval.ms</name>
    <value>30000</value>
  </property>
  <property>
    <name>dfs.namenode.http-address</name>
    <value>0.0.0.0:50070</value>
  </property>
  <property>
    <name>dfs.ha.tail-edits.period</name>
    <value>60</value>
  </property>
  <property>
    <name>dfs.namenode.https-bind-host</name>
    <value>0.0.0.0</value>
  </property>
  <property>
    <name>dfs.namenode.startup.delay.block.deletion.sec</name>
    <value>0</value>
  </property>
  <property>
    <name>dfs.namenode.checkpoint.max-retries</name>
    <value>3</value>
  </property>
  <property>
    <name>dfs.namenode.edits.dir</name>
    <value>file:///mnt/disk1/hdfs/edits</value>
  </property>
  <property>
    <name>dfs.namenode.shared.edits.dir</name>
    <value>qjournal://master-1-1.c-f7ba4ab31b10bfa1.cn-beijing.emr.aliyuncs.com:8485;master-1-2.c-f7ba4ab31b10bfa1.cn-beijing.emr.aliyuncs.com:8485;master-1-3.c-f7ba4ab31b10bfa1.cn-beijing.emr.aliyuncs.com:8485/emr-cluster</value>
  </property>
  <property>
    <name>dfs.datanode.fsdatasetcache.max.threads.per.volume</name>
    <value>4</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.hdfs-cluster.nn2</name>
    <value>master-1-2.c-f7ba4ab31b10bfa1.cn-beijing.emr.aliyuncs.com:50070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.hdfs-cluster.nn1</name>
    <value>master-1-1.c-f7ba4ab31b10bfa1.cn-beijing.emr.aliyuncs.com:50070</value>
  </property>
  <property>
    <name>dfs.namenode.decommission.max.concurrent.tracked.nodes</name>
    <value>100</value>
  </property>
  <property>
    <name>dfs.namenode.full.block.report.lease.length.ms</name>
    <value>300000</value>
  </property>
  <property>
    <name>dfs.heartbeat.interval</name>
    <value>3</value>
  </property>
  <property>
    <name>dfs.namenode.secondary.http-address</name>
    <value>0.0.0.0:50090</value>
  </property>
  <property>
    <name>dfs.support.append</name>
    <value>true</value>
  </property>
  <property>
    <name>ipc.8020.callqueue.impl</name>
    <value>org.apache.hadoop.ipc.FairCallQueue</value>
  </property>
  <property>
    <name>dfs.http.client.retry.policy.spec</name>
    <value>10000,6,60000,10</value>
  </property>
  <property>
    <name>dfs.namenode.checkpoint.period</name>
    <value>3600</value>
  </property>
  <property>
    <name>hadoop.fuse.connection.timeout</name>
    <value>300</value>
  </property>
  <property>
    <name>dfs.ha.automatic-failover.enabled</name>
    <value>true</value>
  </property>
</configuration>