package com.che.hadoop.logclean.bo.discard

/**
 * Desc:清洗bbs用户数据进hive,按照更新日期更新数据
 * Auther:l<PERSON><PERSON><PERSON><PERSON><PERSON>
 * Date:2019-03-24
 * Update:增加数据异常预警(2019-08-01)
 * 已废弃
 **/

import com.che.hadoop.underlay.dao
import com.che.hadoop.logclean.utils.DingRobot
import scala.collection.mutable.ListBuffer
import scala.collection.JavaConversions._
import scala.collection.JavaConversions.asScalaBuffer
import scalaj.http.Http
import com.che.hadoop.logclean.utils._
import org.apache.spark.sql.{Row, SparkSession}
import org.apache.spark.sql.types.{StructField, StructType, StringType, IntegerType, ArrayType, MapType, TimestampType}
import java.util.concurrent.TimeUnit
import scala.io.Source
import java.text.SimpleDateFormat

object FetchUserDataTemp {
  val dingRobot=new DingRobot("https://oapi.dingtalk.com/robot/send?access_token=ce480db9940fafdc4cb1ec5253fa1fd9ff5a3f30746faf13adba6c3337eb03ad")
  def dealUidByAppend(uidList: ListBuffer[String] = new ListBuffer[String], startUid: Int = 0, endUid: Int = 0): Unit = {
    try {
      var uid = 0
      var nickname = ""
      var sjyz = ""
      var unionid = ""
      var openid = ""
      var status = 0
      var userLevel = 0
      var userLevelTtitle = ""
      var bday = ""
      var gender = 0
      var regdate = ""
      var isVerifyIdcard = 0
      var idcardNum = ""
      var name = ""
      var verifyIdcardDateline = ""
      var isVerifyCar = 0
      var platenumber = ""
      var brand = ""
      var verifyCarDateline = ""
      var idcard = scala.collection.mutable.Map[String, String]()
      var driverLicense = scala.collection.mutable.Map[String, String]()
      var travelCard = scala.collection.mutable.Map[String, String]()
      var otherInfo = scala.collection.mutable.Map[String, String]()

      //定义structType
      val structType = StructType(Array(
        StructField("uid", IntegerType, true),
        StructField("nickname", StringType, true),
        StructField("sjyz", StringType, true),
        StructField("unionid", StringType, true),
        //        StructField("openid", StringType, true),
        StructField("status", IntegerType, true),
        StructField("user_level", IntegerType, true),
        StructField("user_level_title", StringType, true),
        StructField("bday", StringType, true),
        StructField("gender", IntegerType, true),
        StructField("regdate", StringType, true),
        StructField("is_verify_idcard", IntegerType, true),
        StructField("idcardnum", StringType, true),
        StructField("name", StringType, true),
        StructField("verify_idcard_dateline", StringType, true),
        StructField("is_verify_car", IntegerType, true),
        StructField("platenumber", StringType, true),
        StructField("brand", StringType, true),
        StructField("verify_car_dateline", StringType, true),
        StructField("medals", ArrayType(MapType(StringType, StringType)), true),
        StructField("subforum", ArrayType(MapType(StringType, StringType)), true),
        StructField("idcard", MapType(StringType, StringType), true),
        StructField("driver_license", MapType(StringType, StringType), true),
        StructField("travel_card", MapType(StringType, StringType), true),
        StructField("other_info", MapType(StringType, StringType), true)
      ))
      //循环数据，封装在numberListBuffer中
      if (uidList.length == 0) {
        for (userId <- startUid to endUid) {
          uidList.append(userId.toString)
        }
      }
      var uidRdd = dao.MySparkSession.conn().sparkContext.parallelize(uidList)
      val rowRdd = uidRdd.repartition(10).map(userId => {
        var response = function_.request_data("https://bbs-api.360che.com/interface/app/index.php?action=UserData&type=terminal&fieldAppend=ORC&uid=" + userId.toString)
        if (response != null) {
          //unicodeStr转换为String类型
          TimeUnit.MICROSECONDS.sleep(10)
          var jsonStr = StrEcode.unicodeStr2String(response.body)
          //解析json数据
          val j = new JsonQuery(jsonStr)
          var msg = j.ms("msg")
          if (j.ms("status") == "0") {
            var data = j.m("data")
            uid = data.mi("uid")
            nickname = data.ms("nickname")
            sjyz = data.ms("sjyz")
            unionid = data.ms("unionid")
            //            openid = data.ms("openid")
            status = data.mi("status")
            var level = data.m("level")
            userLevel = level.mi("level")
            userLevelTtitle = level.ms("title")
            bday = data.ms("bday")
            gender = data.mi("gender")
            regdate = data.ms("regdate")
            var verify = data.m("verify")
            var verifyIdcard = data.m("verify").m("idcard")
            if (!verifyIdcard.getM.isEmpty) {
              isVerifyIdcard = 1
              idcardNum = verifyIdcard.ms("idcardnum")
              name = verifyIdcard.ms("name")
              verifyIdcardDateline = verifyIdcard.ms("dateline")
            } else {
              isVerifyIdcard = 0
              idcardNum = ""
              name = ""
              verifyIdcardDateline = ""
            }
            var verifyCar = data.m("verify").m("car")
            if (!verifyCar.getM.isEmpty) {
              isVerifyCar = 1
              platenumber = verifyCar.ms("platenumber")
              brand = verifyCar.ms("brand")
              verifyCarDateline = verifyCar.ms("dateline")
            } else {
              isVerifyCar = 0
              platenumber = ""
              brand = ""
              verifyCarDateline = ""
            }
            var medals = new ListBuffer[scala.collection.mutable.Map[String, String]]

            for (i <- data.m("medals")) {
              val tMap = new JsonQuery(i).getM()
              medals.append(tMap)
            }
            var subforum = new ListBuffer[scala.collection.mutable.Map[String, String]]
            for (i <- data.m("subforum")) {
              val tMap = new JsonQuery(i).getM()
              subforum.append(tMap)
            }
            var ORC = data.m("ORC")
            idcard = ORC.m("idcard").getM()
            driverLicense = ORC.m("driverLicense").getM()
            travelCard = ORC.m("travelCard").getM()
            otherInfo = ORC.m("others").getM()
            Row(uid, nickname, sjyz, unionid, status, userLevel, userLevelTtitle, bday, gender, regdate, isVerifyIdcard, idcardNum, name, verifyIdcardDateline, isVerifyCar, platenumber, brand, verifyCarDateline, medals, subforum, idcard, driverLicense, travelCard, otherInfo)
          } else {
            println(userId)
            Row()
          }
        } else {
          println(userId)
          Row()
        }
      }).filter(x => x.length != 0)
      //      执行hive操作
      val resultDF=dao.MySparkSession.conn().sqlContext.createDataFrame(rowRdd, structType).cache()
//      println(resultDF.count())
      resultDF.repartition(1).write.mode("append").format("Hive").saveAsTable("default.t_user")
    } catch {
      case e: Exception => {
        //异常向上层抛出
        throw e
      }
    }
  }

  //处理更新用户用户数据
  def dealDailyUid(): Unit = {
    try {
      dao.MySparkSession.conn("LogClean_UserDataFetchTempJob")
      //请求更新用户数据
      val startDay="20200206"
      val startTime=new SimpleDateFormat("yyyyMMdd").parse(startDay).getTime/1000
      val bbsUserUpdateWebInterface=s"https://bbs-api.360che.com/interface/app/index.php?action=UserData&type=terminal&method=getUids&startTime=$startTime"
      var response = function_.request_data(bbsUserUpdateWebInterface)
      //unicodeStr转换为String类型
      var jsonStr = StrEcode.unicodeStr2String(response.body)
      val j = new JsonQuery(jsonStr)
      var uidJavaList = j.m("data").getL()
      println(uidJavaList.length)
      if (j.mi("status") == 1 || uidJavaList.length==0){
        throw new Exception(s"bbs接口返回数据异常: $bbsUserUpdateWebInterface")
      }
      var uidStr = uidJavaList.toString.stripMargin('[').stripSuffix("]").replace("\"", "")
      //删除原表中需要更新的用户数据
      dao.MySparkSession.conn().sql(" set mapred.reduce.tasks=3")
      dao.MySparkSession.conn().sql("insert overwrite table default.t_user select * from default.t_user where uid not in (" + uidStr + ") DISTRIBUTE BY rand()")
      var uidListBuffer = new ListBuffer[String]
      for(item<-uidJavaList){
        uidListBuffer.append(item)
      }
      dealUidByAppend(uidList=uidListBuffer)
    }catch {
      case e: Exception =>
        dingRobot.sendAlert("项目运行异常：LogClean_UserDataFetchJob")
        throw  e

    }
  }

  def dealSmallUids():Unit= {
    var myList= ListBuffer[String]("1946187")
    dealUidByAppend(myList)
  }
  def main(args: Array[String]): Unit = {
        dealSmallUids()
//    dealDailyUid()
  }
}
