package com.che.hadoop.logclean.dao

import org.apache.spark.sql.SparkSession

object MySparkSession {
    /**
      * 先尝试获取线上连接，有异常则获取本地连接
      *
      * @param jobNname 作业名称
      * @return SparkSession
      */


    def conn(implicit jobNname: String = "DefaultName"): SparkSession = {
        try {
            SparkSession.builder()
                .appName(jobNname)
                .config("hive.exec.dynamic.partition.mode", "nonstrict")
                .config("hive.metastore.warehouse.dir", "/user/hive/warehouse")
                .config("hive.exec.max.dynamic.partitions", "20000")
//              支持insert overwrite 同一个表需要设置
                .config("spark.sql.hive.convertMetastoreOrc","false")
//              在没有group by的情况下使用having
                .config("spark.sql.legacy.parser.havingWithoutGroupByAsWhere","true")
                //配置钩子类型
                .config("hive.metastore.filter.hook","org.apache.hadoop.hive.ql.security.authorization.plugin.AuthorizationMetaStoreFilterHook")
                .enableHiveSupport()
                .getOrCreate()
        } catch {
            case _: Exception =>
                SparkSession.builder()
                    .appName(jobNname)
                    .master("local[*]")
                    .config("hive.metastore.uris", "thrift://192.168.2.101:9083,thrift://192.168.2.102:9083")
                    .config("hive.exec.max.dynamic.partitions", "20000")
                    .config("hive.exec.dynamic.partition.mode", "nonstrict")
                    .config("hive.metastore.warehouse.dir", "/apps/hive/warehouse")
                    .enableHiveSupport()
                    .getOrCreate()
        }
    }
}
