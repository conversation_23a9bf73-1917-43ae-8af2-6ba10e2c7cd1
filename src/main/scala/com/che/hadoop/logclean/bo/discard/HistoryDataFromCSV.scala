package com.che.hadoop.logclean.bo.discard

import java.text.SimpleDateFormat
import java.util.Calendar

import com.che.hadoop.underlay.dao.{MySparkSession, MySqlBaseDao}
import com.che.hadoop.logclean.utils.StrEcode
import org.apache.spark.sql.Row
import org.apache.spark.sql.functions.col
import org.apache.spark.sql.types.{StringType, StructField, StructType}
import org.joda.time.DateTime
import org.joda.time.format.DateTimeFormat

/**
  * 历史数据导入（历史数据存于CSV文件）
  */
object HistoryDataFromCSV {

  /**
    * 求助历史数据
    */
  def forHelp(): Unit = {

    val ss = MySparkSession.conn()

    val forhelpCSV = ss.read.csv("F:\\NewWork\\help_data\\ForHelpContent.csv").toDF("id", "title", "content", "isSolve", "F_Type", "createDateTime", "F_IsDelete", "userId", "address", "F_NoRead", "F_IsShowPhone", "rewordValue", "referrerPage", "referrerQuery", "viewTerminal", "F_isTop", "F_forwarding", "coordinate", "cityId", "detailed", "F_helpcount", "F_isClose", "resultsNote", "feedBack", "feedBackUid", "F_feedbackdel", "solveDate", "F_focuscount")
    val forHelpTypeCSV = ss.read.csv("F:\\NewWork\\help_data\\ForHelpType.csv").toDF("classifyId", "classifyName", "isDelete")
    forhelpCSV.join(forHelpTypeCSV).where(col("F_Type") === col("classifyId")).createOrReplaceTempView("forhelp_tmp")

    val sqlStr = "select id, title, classifyId , classifyName, createDateTime,  userId, address, content, rewordValue, referrerPage, referrerQuery, viewTerminal, coordinate, cityId, detailed, resultsNote, feedBack, feedBackUid, solveDate, isSolve, " +
      "if(F_IsDelete == '0', 'insert', 'delete') as operationState, " +
      "date_format(regexp_replace(createDateTime, '/', '-'), 'yyyyMMdd') as day, " +
      "concat(unix_timestamp(regexp_replace(createDateTime, '/', '-')), substr(createDateTime, 21)) as timestamp " +
      "from forhelp_tmp"

    val df = ss.sql(sqlStr)

    df.write.mode("append").format("orc").partitionBy("day").saveAsTable("t_forhelp")

  }

  /**
    * 评论历史数据
    */
  def comment(): Unit = {
    val ss = MySparkSession.conn()
    val csvDF = ss.read.csv("F:\\NewWork\\comment_data\\T_Comments_0.csv",
      "F:\\NewWork\\comment_data\\T_Comments_1.csv",
      "F:\\NewWork\\comment_data\\T_Comments_2.csv",
      "F:\\NewWork\\comment_data\\T_Comments_0_20190327.csv",
      "F:\\NewWork\\comment_data\\T_Comments_1_20190327.csv",
      "F:\\NewWork\\comment_data\\T_Comments_2_20190327.csv")
      .toDF("id", "topicId", "content", "F_img", "parentId", "replyId",
        "toUserId", "toNikeName", "F_toheadpic", "userId", "nikeName", "F_headpic",
        "viewTime", "F_praisecount", "F_reportcount", "F_isreview", "isDelete",
        "F_istop", "F_toptime", "F_ishides", "F_isgood", "F_isadmin", "viewTerminal")

    val df = ss.read.csv("F:\\NewWork\\comment_data\\T_Topic.csv").toDF("F_id", "F_articleid", "F_title", "F_url", "siteId", "F_viewtime", "F_categoryid", "F_sum", "F_outersum")

    csvDF.join(df).where(col("topicId") === col("F_id")).createOrReplaceTempView("comment_tmp")

    val sqlStr = "select id, topicId, content, parentId, replyId, toUserId, toNikeName, userId, nikeName, viewTime, isDelete, viewTerminal, siteId, " +
      "if(isDelete == '0', 'insert', 'delete') as operationState, " +
      "date_format(viewTime, 'yyyyMMdd') as day, " +
      "concat(unix_timestamp(viewTime), substr(viewTime, 21)) as timestamp " +
      "from comment_tmp"

    val resultDF = ss.sql(sqlStr)

    resultDF.write.mode("append").format("orc").partitionBy("day").saveAsTable("t_comment")

  }

  /**
    * 帮助回答历史数据
    */
  def help(): Unit ={

    val ss = MySparkSession.conn()

    ss.read.csv("F:\\NewWork\\help_data\\HelpContent_0.csv", "F:\\NewWork\\help_data\\HelpContent_1.csv")
      .toDF("id", "content", "H_IsValid", "thankTitle", "createDateTime", "userId", "H_GivingCalories", "H_GivingThink",
        "forHelpId", "H_IsDelete", "H_IsProblem", "H_Praise", "H_IsShowPhone", "H_NoRead", "H_HighCount", "H_AdoptTime", "H_ReferrerPage",
        "H_ReferrerQuery", "H_ViewTerminal", "H_NoPraise", "H_ContentCount", "H_DiscussCount", "isAdmin").createOrReplaceTempView("help_tmp")

    val sqlStr = "select id, content, thankTitle, createDateTime, userId, forHelpId, isAdmin, " +
      "if(H_IsDelete == '0', 'insert', 'delete') as operationState, " +
      "date_format(createDateTime, 'yyyyMMdd') as day, " +
      "concat(unix_timestamp(createDateTime), substr(createDateTime, 21)) as timestamp " +
      "from help_tmp"
    val df = ss.sql(sqlStr)

    df.write.mode("append").format("orc").partitionBy("day").saveAsTable("t_help")

  }

  /**
    * 求助评论回复数据
    */
  def forhelpdiscuss(): Unit ={

    val ss = MySparkSession.conn()

    ss.read.csv("F:\\NewWork\\help_data\\ForHelpDiscuss.csv")
      .toDF("id", "content", "createDateTime", "helpId", "forHelpId", "discussParentID", "userID", "userName", "D_IsDelete", "D_img")
      .createOrReplaceTempView("forhelpdiscuss_tmp")

    val sqlStr = "select id, content, createDateTime, helpId, forHelpId, discussParentID, userID, userName, " +
      "if(D_IsDelete == '0', 'insert', 'delete') as operationState, " +
      "date_format(createDateTime, 'yyyyMMdd') as day, " +
      "concat(unix_timestamp(createDateTime), substr(createDateTime, 21)) as timestamp " +
      "from forhelpdiscuss_tmp"
    val df = ss.sql(sqlStr)

    df.write.mode("append").format("orc").partitionBy("day").saveAsTable("t_forhelpdiscuss")

  }

  /**
    * 求助关注数据
    */
  def forhelpfocuson(): Unit ={

    val ss = MySparkSession.conn()

    ss.read.csv("F:\\NewWork\\help_data\\ForHelpFocusOn.csv")
      .toDF("id", "forHelpId", "createDateTime", "userId", "FO_NoRead")
      .createOrReplaceTempView("forhelpfocuson_tmp")

    val sqlStr = "select id, forHelpId, createDateTime, userId, " +
      "'insert' as operationState, " +
      "date_format(createDateTime, 'yyyyMMdd') as day, " +
      "concat(unix_timestamp(createDateTime), substr(createDateTime, 21)) as timestamp " +
      "from forhelpfocuson_tmp"
    val df = ss.sql(sqlStr)

    df.write.mode("append").format("orc").partitionBy("day").saveAsTable("t_forhelpfocuson")

  }

  /**
    * 文章历史数据
    */
  def articleData(): Unit ={

    // 标签id与标签名称映射
    val lableMap = scala.collection.mutable.Map[Int, String]()
    // 标签类型id与标签类型名称映射
    val lableTypeMap = scala.collection.mutable.Map[Int, String]()
    // 一节分类id与名称映射
    val firstClassifyMap = scala.collection.mutable.Map[Int, String]()
    //二级分类id与名称映射
    val secondClassifyMap = scala.collection.mutable.Map[Int, String]()

    val conn = new MySqlBaseDao()
    var sql = "select * from t_article_lable"
    var rs = conn.executeQuery(sql, null)
    while (rs.next()) {
      lableMap += (rs.getInt("f_lable_id") -> rs.getString("f_lable_name"))
      lableTypeMap += (rs.getInt("f_lable_type_id") -> rs.getString("f_lable_type_name"))
    }
    sql = "select * from t_article_classify"
    rs = conn.executeQuery(sql, null)
    while (rs.next()) {
      firstClassifyMap += (rs.getInt("f_first_classify_id") -> rs.getString("f_first_classify_name"))
      secondClassifyMap += (rs.getInt("f_second_classify_id") -> rs.getString("f_second_classify_name"))
    }

//    // 车型id与价格映射
//    val priceMap = scala.collection.mutable.Map[Int, Float]()
//    // 车型id与驱动形式映射
//    val driveFromMap = scala.collection.mutable.Map[Int, String]()
//    // 车型id与马力映射
//    val horsepowerMap = scala.collection.mutable.Map[Int, Int]()
//    // 车型id与货箱长度映射
//    val containerLengthMap = scala.collection.mutable.Map[Int, String]()
//    // 车型id与货箱形式映射
//    val containerFormMap = scala.collection.mutable.Map[Int, String]()

    // 将所有车辆属性数据读入内存
//    sql = "SELECT f_product_id, f_price, f_driving_form, f_horsepower, f_container_length, f_container_form " +
//      "FROM t_truck_attribute WHERE f_enable = 1"
//    rs = conn.executeQuery(sql, null)
//
//    while (rs.next()) {
//
//      priceMap += (rs.getInt("f_product_id") -> rs.getFloat("f_price"))
//      driveFromMap += (rs.getInt("f_product_id") -> rs.getString("f_driving_form"))
//      horsepowerMap += (rs.getInt("f_product_id") -> rs.getInt("f_horsepower"))
//      containerLengthMap += (rs.getInt("f_product_id") -> rs.getString("f_container_length"))
//      containerFormMap += (rs.getInt("f_product_id") -> rs.getString("f_container_form"))
//
//    }

    // 大类id与名称映射
    val cateMap = scala.collection.mutable.Map[Int, String]()
    // 子类id与名称映射
    val subCateMap = scala.collection.mutable.Map[String, String]()
    // 品牌id与名称映射
    val brandMap = scala.collection.mutable.Map[String, String]()
    // 车系id与名称映射
    val seriesMap = scala.collection.mutable.Map[String, Int]()
    // 车型id与名称映射
    val productMap = scala.collection.mutable.Map[String, String]()

    sql = "SELECT F_CateId, F_CateName FROM t_category"
    rs = conn.executeQuery(sql, null)
    while (rs.next()) {
      cateMap += (rs.getInt("F_CateId") -> rs.getString("F_CateName"))
    }
    sql = "select id, name from t_subcategory where enable=1"
    rs = conn.executeQuery(sql, null)
    while (rs.next()){
      subCateMap += (rs.getString("id") -> rs.getString("name"))
    }
    sql = "select id, name from t_brand where enable=1"
    rs = conn.executeQuery(sql, null)
    while (rs.next()){
      brandMap += (rs.getString("id") -> rs.getString("name"))
    }
    sql = "select id,if(t_series_tonnage.tonnageType,t_series_tonnage.tonnageType,0) as tonn from t_series LEFT JOIN t_series_tonnage " +
      " ON t_series.id=t_series_tonnage.seriesId where t_series.enable=1 "
    rs = conn.executeQuery(sql, null)
    while (rs.next()){
      seriesMap += (rs.getString("id") -> rs.getInt("tonn"))
    }
    sql = "select id, name from t_product where enable=1"
    rs = conn.executeQuery(sql, null)
    while (rs.next()){
      productMap += (rs.getString("id") -> rs.getString("name"))
    }

    val ss = MySparkSession.conn()
    //从csv文件中读取数据并创建临时表
    val rawDF = ss.read.csv("C:\\Users\\<USER>\\Desktop\\ArticleTable(1).csv")
      .toDF("ArticleId", "title","mtitle","CategoryId1", "CategoryId2", "RelationId", "CommentCount", "ShareCount", "PraiseCount", "SourceType", "PublishDateTime","CityName","ProvinceName","Lable","LableType","contents")
      //.createOrReplaceTempView("t_tmp_raw")

//    ss.sql("select ArticleId as id, PublishDateTime as publishTime, CategoryId1 as first_classifyId, " +
//      "CategoryId2 as second_classifyId")

    // 将一对多的关系拆分
    //"id", "title", "mTitle", "publishTime", "createDateTime", "first_classifyId", "first_classifyName",
    // "second_classifyId", "second_classifyName","content","relationId","relationType","lable","sourceType",
    // "proName","citName","timestamp", "operationState", "day"

    val rdd = rawDF.rdd.map(row => {
      val articleId = row.getAs[String]("ArticleId")
      var rddRow:Row = Row()
      try {
      //文章id

      //一级分类id
      val firstClassifyId = row.getAs[String]("CategoryId1")
      //一级分类名
      val firstClassifyName = firstClassifyMap(firstClassifyId.toInt)
      //二级分类id
      val secondClassifyId = row.getAs[String]("CategoryId2")
      //二级分类名
      var secondClassifyName = ""
      if(secondClassifyId.toInt!=0){
        secondClassifyName = secondClassifyMap(secondClassifyId.toInt)
      }
      //标签id
      var lableId = row.getAs[String]("Lable")
      if(lableId != null && lableId != "") {
        lableId = lableId.split("\\|").toSet.mkString(",")
      }
      //标签名
//      var lableName = ""
//      var lId = ""
//      if(lableId != null && lableId != ""){
//          for(a <- lableId.split("\\|")){
//            try{
//              lableName = "|" + lableMap(a.toInt)
//              lId = "|" + a
//            }catch{
//              case e:Exception =>
//            }
//          }
//          lableName = lableName.substring(1)
//          lableId = lId.substring(1)
//      }else{
//        lableId = ""
//      }
      //标签类别id
      var lableTypeId = row.getAs[String]("LableType")
      if(lableTypeId != null && lableTypeId != "") {
        lableTypeId = lableTypeId.split("\\|").toSet.mkString(",")
      }
      //标签类别名
//      var lableTypeName = ""
//      var ltId = ""
//      if(lableTypeId != null && lableTypeId != ""){
//          for(b <- lableTypeId.split("\\|")){
//            try{
//              lableTypeName = "|" + lableTypeMap(b.toInt)
//              ltId = "|" + b
//            }
//            catch{
//              case e:Exception =>
//            }
//          }
//          lableTypeName = lableTypeName.substring(1)
//          lableTypeId = ltId.substring(1)
//
//      }else{
//        lableTypeId = ""
//      }

      //大类
      import scala.collection.mutable.Set
      var cateId: Set[String]  = Set()
      var cateName = ""
      //子类
      var subCateId: Set[String]  = Set()
      var subCateName = ""
      //品牌
      var brandId: Set[String]  = Set()
      var brandName = ""
      //车系
      var seriesId: Set[String]  = Set()
      var seriesName = ""
      //车型
      var productId: Set[String]  = Set()
      var tonn: Set[String]  = Set()
      var productName = ""

      //价格
//      var price: Set[String]  = Set()
//      //马力
//      var horsepower: Set[String]  = Set()
//      //驱动形式
//      var driveFrom: Set[String]  = Set()
//      //货箱长度
//      var containerLength: Set[String]  = Set()
//      //货箱形式
//      var containerForm: Set[String]  = Set()

      // 解析关联的产品id
      var rid = row.getAs[String]("RelationId")
      if(rid != null && rid != "") {
        val rids = rid.split("\\|")
        for (r <- rids) {
          val idArray = r.split("#")
          for (i <- 0 until idArray.length) {
            if (i == 0) {
              cateId.add(idArray(i))
            } else if (i == 1 && subCateMap.contains(idArray(i))) {
              subCateId.add(idArray(i))
            } else if (i == 2 && brandMap.contains(idArray(i))) {
              brandId.add(idArray(i))
            } else if (i == 3 && seriesMap.contains(idArray(i))) {
              seriesId.add(idArray(i))
//              if (seriesMap(idArray(i)) != 0) {
//                tonn.add(seriesMap(idArray(i)).toString)
//              }
            } else if (i == 4 && productMap.contains(idArray(i))) {
              productId.add(idArray(i))
//              val pid = idArray(i).toInt
//              if(priceMap.contains(pid)){
//                price.add(priceMap(pid).toString)
//                horsepower.add(horsepowerMap(pid).toString)
//                driveFrom.add(driveFromMap(pid))
//                containerLength.add(containerLengthMap(pid))
//                containerForm.add(containerFormMap(pid))
//              }

            }

          }
        }
      }
      val title = StrEcode.unicode2String(row.getAs[String]("title"))
      val mtitle = StrEcode.unicode2String(row.getAs[String]("mtitle"))
      val content = StrEcode.unicode2String(row.getAs[String]("contents"))
      //评论数
      val commentCount = row.getAs[String]("CommentCount")
      //分享数
      val praiseCount = row.getAs[String]("PraiseCount")
      //点赞数
      val shareCount = row.getAs[String]("ShareCount")
      //来源
      val sourceType = row.getAs[String]("SourceType")

      //省份
      val province = row.getAs[String]("ProvinceName")
      //城市
      val city = row.getAs[String]("CityName")
      //发布时间
      val publishTime =transFormat(row.getAs[String]("PublishDateTime"),"yyyy/MM/dd HH:mm:ss","yyyy-MM-dd")
      rddRow =  Row(articleId,title,mtitle,content, firstClassifyId, firstClassifyName, secondClassifyId, secondClassifyName,
        lableId, lableTypeId, cateId.mkString(","), subCateId.mkString(","),
        brandId.mkString(","), seriesId.mkString(","), productId.mkString(","),
        commentCount, praiseCount, shareCount, sourceType, province, city, publishTime,"0","20190716")
      }catch {
        case ex:NumberFormatException  => {
          println(ex)
          println("articleId:"+articleId)
        }
        case ex2:NoSuchElementException =>{
          println(ex2)
          println("articleId:"+articleId)
        }

      }
      rddRow
    }).filter(x=>x.length!=0)
    rdd.collect()
    val fields = "article_id,title,mtitle,content,first_classify_id,first_classify_name,second_classify_id,second_classify_name,lable_id," +
      "lable_type_id,cate_id,sub_cate_id,brand_id," +
      "series_id,product_id," +
      "comment_count,praise_count,share_count,source_type,province,city,publish_time,is_del,day"
    val schema = StructType(fields.split(",").map(x => StructField(x, StringType, nullable = true)))

    val df = ss.createDataFrame(rdd, schema)
    val exe = new MySqlBaseDao()
//    exe.saveMysql(df,"t_article_t")

//    df.write.mode("append").format("orc").saveAsTable("db_test.t_article_tmh")

    ss.stop()

  }
  def transFormat(strdate: String, inputPattern: String, outputPattern: String): String = {
    val dateFormat: SimpleDateFormat = new SimpleDateFormat(outputPattern)
    val cal: Calendar = Calendar.getInstance()
    cal.setTime(DateTime.parse(strdate, DateTimeFormat.forPattern(inputPattern)).toDate)
    cal.add(Calendar.DATE, 0)
    val date = dateFormat.format(cal.getTime())
    return date

  }

  /**
   * 获取帖子历史数据，写入到Hive
   */
  def getPostData(): Unit = {

    //val spark = SparkSession.builder().master("local").getOrCreate()
    val spark = MySparkSession.conn()

    val data = spark.read.format("csv")
      .option("header", "true")
      .option("delimiter", "\t")
      .load("D:\\post_more_new\\*.csv").toDF("uid", "is_superman", "fans_num", "bbs_tid",
      "bbs_publish_time", "ip", "bbs_domain", "bbs_subcate", "bbs_tonnage", "bbs_series", "bbs_brand",
      "bbs_comment_count", "bbs_praise_count", "bbs_share_count", "is_del", "bbs_type")
      //.where("uid is not null and bbs_tid is not null and is_del is not null and bbs_type is not null")

//    val dataResult = data.select(
//      "bbs_tid",
//      "bbs_domain",
//      "bbs_subcate",
//      "bbs_tonnage",
//      "bbs_series",
//      "bbs_brand",
//      "is_del",
//      "bbs_type")

//    data.filter("bbs_domain like '%|%' ").show()
//
//    data.filter("bbs_tid is null").show()
//
//    data.filter("uid is null").show()

    data.write.mode("append").format("hive")
      .saveAsTable("db_test.t_bbs_post_more_new")

    //    val data = spark.sql("select * from db_test.t_bbs_post")

    //    val reader = ResourceBundle.getBundle("connection")
    //    val url = reader.getString("portrait.url")
    //    val username = reader.getString("portrait.username")
    //    val password = reader.getString("portrait.password")
    //    //将结果写入到Mysql
    //    data.write.mode("append")
    //      .format("jdbc")
    //      .option("url", url)
    //      .option("dbtable","t_bbs_post_bak") //表名
    //      .option("user", username)
    //      .option("password", password)
    //      .save()

    spark.stop()
  }

  def main(args: Array[String]): Unit = {

    getPostData()

  }

}
