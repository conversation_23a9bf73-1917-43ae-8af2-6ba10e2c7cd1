package com.che.hadoop.logclean.bo

/**
 * Desc:清洗bbs用户数据进hive
 * Auther:l<PERSON><PERSON><PERSON><PERSON>an
 * Date:2019-03-24
 * json解析采用alibabaJson方式
 * 线上使用
 **/

import java.util.ResourceBundle

import com.che.hadoop.logclean.utils.DingRobot
import org.apache.spark.sql.{Row, SparkSession}
import org.apache.spark.sql.types._
import com.alibaba.fastjson._
import com.che.hadoop.underlay.dao.{MySparkSession, MySqlBaseDao}
import com.che.hadoop.underlay.tool.http.HttpUtil
import com.che.hadoop.underlay.tool.common.StrEcode
import com.che.hadoop.underlay.tool.date.DateTranslate
import scala.collection.JavaConversions.{asScalaBuffer, _}
import scala.collection.mutable.ListBuffer
import scala.io.Source

object FetchUserDataAlibaba {

  /*定义全局变量*/
  //实例化钉钉机器人对象
  val dingRobot = new DingRobot(ResourceBundle.getBundle("robot").getString("ding.monitor"))
  //用户信息hive表(结果表)
  val userTable = "t_user"
//  val userTable = "db_test.t_user_lxq"

  /**
   *通过ListBuffer 添加用户
   * @param uidList 用户列表
   * @param spark SparkSession实例
   * @param startUid 起始uid
   * @param endUid 结束uid
   */
  def dealUidByAppend(uidList: ListBuffer[String] = new ListBuffer[String],spark:SparkSession, startUid: Int = 0, endUid: Int = 0): Unit = {
    try {
      var uid = 0
      var nickname = ""
      var sjyz = ""
      var unionid = ""
      //      var openid = ""
      var status = 0
      var userLevel = 0
      var userLevelTtitle = ""
      var bday = ""
      var gender = 0
      var regdate = ""
      var isVerifyIdcard = 0
      var idcardNum = ""
      var name = ""
      var verifyIdcardDateline = ""
      var isVerifyCar = 0
      var platenumber = ""
      var brand = ""
      var verifyCarDateline = ""
      var idcard = scala.collection.mutable.Map[String, String]()
      var driverLicense = scala.collection.mutable.Map[String, String]()
      var travelCard = scala.collection.mutable.Map[String, String]()
      var otherInfo = scala.collection.mutable.Map[String, String]()
      var isNiuren = 0
      var niurenLevel = 0
      var lastvisit=""
      var badge=""
      //定义structType
      val structType = StructType(Array(
        StructField("uid", IntegerType, true),
        StructField("nickname", StringType, true),
        StructField("sjyz", StringType, true),
        StructField("unionid", StringType, true),
        //        StructField("openid", StringType, true),
        StructField("status", IntegerType, true),
        StructField("user_level", IntegerType, true),
        StructField("user_level_title", StringType, true),
        StructField("bday", StringType, true),
        StructField("gender", IntegerType, true),
        StructField("regdate", StringType, true),
        StructField("is_verify_idcard", IntegerType, true),
        StructField("idcardnum", StringType, true),
        StructField("name", StringType, true),
        StructField("verify_idcard_dateline", StringType, true),
        StructField("is_verify_car", IntegerType, true),
        StructField("platenumber", StringType, true),
        StructField("brand", StringType, true),
        StructField("verify_car_dateline", StringType, true),
        StructField("medals", ArrayType(MapType(StringType, StringType)), true),
        StructField("subforum", ArrayType(MapType(StringType, StringType)), true),
        StructField("idcard", MapType(StringType, StringType), true),
        StructField("driver_license", MapType(StringType, StringType), true),
        StructField("travel_card", MapType(StringType, StringType), true),
        StructField("other_info", MapType(StringType, StringType), true),
        StructField("is_niuren", IntegerType, true),
        StructField("niuren_level", IntegerType, true),
        StructField("lastvisit", StringType, true),
        StructField("badge", StringType, true)
      ))
      //循环数据，封装在numberListBuffer中
      if (uidList.length == 0) {
        for (userId <- startUid to endUid) {
          uidList.append(userId.toString)
        }
      }
      val uidRdd = spark.sparkContext.parallelize(uidList)
      val rowRdd = uidRdd.map(userId => {
        val reader=ResourceBundle.getBundle("connection")
        val api=reader.getString("api.bbs.uid_info")
        //正式
        val response = HttpUtil.get(api + userId.toString)
        if (response != None) {
          //                    TimeUnit.MILLISECONDS.sleep(500)
          val jsonStr = response.get.body
          println(jsonStr)
          //解析json数据
          val j = JSON.parseObject(jsonStr)
          if (j.getIntValue("status") == 0) {
            try {
              val data = j.getJSONObject("data")
              uid = data.getIntValue("uid")
              nickname = data.getString("nickname")
              sjyz = data.getString("sjyz")
              unionid = data.getString("unionid")
              //            openid = data.getString("openid")
              status = data.getIntValue("status")
              val level = data.getJSONObject("level")
              userLevel = level.getIntValue("level")
              userLevelTtitle = level.getString("title")
              bday = data.getString("bday")
              gender = data.getIntValue("gender")
              regdate = data.getString("regdate")
              val verify = data.getJSONObject("verify")
              val verifyIdcard = verify.getJSONObject("idcard")
              if (verifyIdcard != null) {
                isVerifyIdcard = 1
                idcardNum = verifyIdcard.getString("idcardnum")
                name = verifyIdcard.getString("name")
                verifyIdcardDateline = verifyIdcard.getString("dateline")
              } else {
                isVerifyIdcard = 0
                idcardNum = ""
                name = ""
                verifyIdcardDateline = ""
              }
              val verifyCar = verify.getJSONObject("car")
              if (verifyCar != null) {
                isVerifyCar = 1
                platenumber = verifyCar.getString("platenumber")
                brand = verifyCar.getString("brand")
                verifyCarDateline = verifyCar.getString("dateline")
              } else {
                isVerifyCar = 0
                platenumber = ""
                brand = ""
                verifyCarDateline = ""
              }
              val medals = new ListBuffer[scala.collection.mutable.Map[String, String]]

              for (i <- data.getJSONArray("medals")) {
                val tMap = getMap(JSON.parseObject(i.toString))
                medals.append(tMap)
              }
              val subforum = new ListBuffer[scala.collection.mutable.Map[String, String]]
              for (i <- data.getJSONArray("subforum")) {
                val tMap = getMap(JSON.parseObject(i.toString))
                medals.append(tMap)
              }
              val ORC = data.getJSONObject("ORC")
              idcard = getMap(ORC.getJSONObject("idcard"))
              driverLicense = getMap(ORC.getJSONObject("driverLicense"))
              travelCard = getMap(ORC.getJSONObject("travelCard"))
              otherInfo = getMap(ORC.getJSONObject("others"))
              isNiuren = data.getIntValue("isNiuren")
              niurenLevel = if (data.get("niurenLevel") == null) 0 else data.getIntValue("niurenLevel")
              lastvisit=data.getString("lastdate")
              badge=if (data.get("badge") == null) null else data.getJSONArray("badge").map(x=>{
                new JSONObject(JSON.parseObject(x.toString).filterKeys(List("id","name","level").contains(_))).toString
              }).mkString("[",",","]")
              Row(uid, nickname, sjyz, unionid, status, userLevel, userLevelTtitle, bday, gender, regdate, isVerifyIdcard, idcardNum, name, verifyIdcardDateline, isVerifyCar, platenumber, brand, verifyCarDateline, medals, subforum, idcard, driverLicense, travelCard, otherInfo, isNiuren, niurenLevel,lastvisit,badge)
            } catch {
              case e: Exception => {
                println(s"***$userId--解析异常***")
                //异常向上层抛出
                throw e
              }
            }
          } else {
            println(s"***$userId--status=1**")
            Row()
          }
        } else {
          println(s"***$userId--respose错误***")
          Row()
        }
      }).filter(x => x.length != 0)
      //      执行hive操作
      spark.sqlContext.createDataFrame(rowRdd, structType).createOrReplaceTempView("t_user_tmp")
      val resultDF = spark.sql(
        """
          |select uid, nickname, base64(sjyz) as sjyz, unionid, status, user_level,
          | user_level_title, cast(bday as date), gender, regdate, is_verify_idcard, idcardNum, name, verify_idcard_dateline, is_verify_car,
          | platenumber, brand, verify_car_dateline, medals, subforum, idcard, driver_license, travel_card, other_info, is_niuren, niuren_level,lastvisit,badge
          |from t_user_tmp
          |""".stripMargin)
      resultDF.cache()
//      resultDF.show(false)
      println(resultDF.count())
      //正式环境打开
      resultDF.repartition(1).write.mode("append").format("Hive").saveAsTable(userTable)

    } catch {
      case e: Exception => {
        //异常向上层抛出
        throw e
      }
    }
  }

  /**
   * 阿里巴巴JSONObject转换为Map(String,String)
   * @param JsonObject
   */
  def getMap(JsonObject: JSONObject): scala.collection.mutable.Map[String, String] = {
    var javaMap = scala.collection.mutable.Map[String, String]()
    if (JsonObject != null) {
      for ((k, v) <- JsonObject) {
        javaMap.put(k, v.toString)
      }
    }
    javaMap
  }

  /**
   * 处理更新用户用户数据入口方法
   * @param startTime 获取更新用户截至时间（时间戳格式）
   */
  def dealDailyUid(spark:SparkSession,startTime: String = ""): Unit = {
    val spark=MySparkSession.conn()
    val exec = new MySqlBaseDao()
    val day = com.che.hadoop.logclean.utils.Common.getDate(1)
    try {
      //请求更新用户数据
      val reader=ResourceBundle.getBundle("connection")
      var bbsUserUpdateWebInterface =reader.getString("api.bbs.uids")
      if (startTime != "") {
        val tranStartTime = DateTranslate.tranTimeStringToLong(startTime,"yyyyMMdd")/1000
        bbsUserUpdateWebInterface += s"&startTime=$tranStartTime"
        println(bbsUserUpdateWebInterface)
      }
      val response = HttpUtil.get(bbsUserUpdateWebInterface)
      if (response==None){
        throw new Exception(s"拉取用户接口重试3次失败--$bbsUserUpdateWebInterface")
      }
      //unicodeStr转换为String类型
      val jsonStr = StrEcode.unicodeStr2String(response.get.body)
      val j=JSON.parseObject(jsonStr)
      val uidJavaList=j.getJSONArray("data").toArray
      println(j)
      if (j.getIntValue("status") == 1 || uidJavaList.length == 0) {
        throw new Exception(s"bbs接口返回数据异常: $bbsUserUpdateWebInterface")
      }
      val uidStr = uidJavaList.mkString(",").replace("\"", "")
      //删除原表中需要更新的用户数据
      spark.sql(" set mapred.reduce.tasks=3")
      spark.sql(s"insert overwrite table $userTable select * from $userTable where uid not in (" + uidStr + ") DISTRIBUTE BY rand()")
      val uidListBuffer = new ListBuffer[String]
      for (item <- uidJavaList) {
        uidListBuffer.append(item.toString)
      }
      dealUidByAppend(uidList = uidListBuffer,spark)
      exec.executeInsertOrUpdate(s"update t_check set flag=1, message='$day:运行成功' where name='aliyun_t_user'",null)
    } catch {
      case e: Exception =>

        e.printStackTrace()
        try{
          exec.executeInsertOrUpdate(s"update t_check set flag=2, message='$day:aliyun_t_user 运行失败' where name='aliyun_t_user'",null)
        }catch {
          case e:Throwable=>{
            e.printStackTrace()
          }
        }
    }
  }

  /**
   * 通过文件更新数据
   */
  def updateUidsFromFile(): Unit = {
    val uidFile = Source.fromFile("/Users/<USER>/Downloads/niuren_uid.txt")
    val lines = uidFile.getLines()
    val uidList = new ListBuffer[String]
    for (line <- lines) {
      uidList.append(line)
    }
    println(uidList)
    var uidStr = uidList.toString.stripSuffix(")").replace("ListBuffer(", "")
    println(uidStr)
    //删除原表中需要更新的用户数据
    MySparkSession.conn().sql(" set mapred.reduce.tasks=3")
    MySparkSession.conn().sql("insert overwrite table default.t_user select * from default.t_user where uid not in (" + uidStr + ") DISTRIBUTE BY rand()")
    //        dao.MySparkSession.conn.sql("insert overwrite table db_test.t_user_back select * from db_test.t_user_back where uid not in (" + uidStr + ") DISTRIBUTE BY rand()")
  }

  def test(spark:SparkSession): Unit ={
    val uidsDF2=spark.read
      .format("csv")
      .option("header", "true")  // 声明csv文件的第一行为列名
      .option("inferSchema", "true")  // 自动推断列的类型
//      .load("file:////E:\\a.csv") // 指定csv文件的路径
      .load("hdfs://hdfs-cluster/test/a.csv") // 指定csv文件的路径
    val sqlBase=new MySqlBaseDao()
//    uidsDF2.show(false)
//    sqlBase.saveMysql(uidsDF2.filter("uid is not null"),"t_user_tmp","overwrite")

//    sqlBase.saveMysql(uidsDF,"t_user_tmp")
    val uidsDF=sqlBase.getDfTable("t_user_tmp",spark)
//    val uidsDF=uidsDF2.filter("uid is not null")
    val t_userDF=spark.sql("select uid from t_user")
    val uidListBuffer = new ListBuffer[String]
    uidsDF.rdd.subtract(t_userDF.rdd).collect.foreach(row=>{
      println(s"uid=${row.getInt(0).toString}")
      uidListBuffer.append(row.getInt(0).toString)
    })
    println(s"uidListBuffer的长度--${uidListBuffer.length}")
    dealUidByAppend(uidList = uidListBuffer,spark)
  }

  /**
   * 通过List添加用户
   */
  def updateUidsFromList(): Unit = {
    val ss=MySparkSession.conn()
    val myList = ListBuffer[String]("2087374","2155075","242036","361758","3136545","2159338","393349","1704431","2109261","1849626","3136576","3004644","2096096","2165402","1944437","2178755","3004456","2169820","2517731","1859159","2331291","1949336","3057978","254430","2178076","1846357","1869124","1749074","347429","1446691","2016564","1649624","1994803","1997709","2169527","2638208","2106823","1555893","2331355","3004675","2087511","2719282","3137101","2200419","1756237","3136303","317507","247259","2161054","2084283","1608392","2032260","1466596","2179838","2085938","2087171","1989139","2149187","2162727","3004888","196649","1350401","3004983","1849449","2085974","34074239","2133796","2163176","2153206","2164401","2183708","273115","2158143","2170194","2164188","2597872","329763","310193","2158038","567281","3004575","324215","1820478","2175878","2149141","437345","2090354","3134505","2089867","664960","391617","1357894","2085064","2184224","1854700","268429","2143408","2158534","2109349","3004750","2037330","3004481","127420","2169333","755191","110611","2016224","2719431","2088339","1440073","2331750","2178320","2088504","1536616","2138124","1867578","2149609","1786127","1393836","2089616","387060","2087854","2641890","2719313","432924","2523092","2000645","2262697","2719454","3004891","1723239","2185322","644583","1741456","437573","2177827","105412","1342664","2148709","2262136","1428245","2170399","2091317","1579617","2154506","616062","2633666","2719361","3004979","1438686","2153922","2175758","1977894","2159135","2162779","2554315","2510557","2089431","581232","2719289","588256","2152176","2090778","1524509","2162391","2169894","2163201","3004665","3004577","1559068","2719325","1833513","2099514","2719345","2155279","2086055","731056","737661","2262710","2153509","142530","3022193","2165295","2090172","396874","2068061","732342","2178347","589352","1709156","2687115","2161391","1752509","3004720","2121580","1749277","1706159","1800054","2098426","2170048","362471","2635734","2719260","314247","2157357","2184502","2088331","2166009","58610","2678819","2178672","2089932","1537576","2640503","491847","2154245","3004698","2162759","1630512","3005114","3004445","2089991","3005024","2169337","2154681","2178525","2085803","2161961","2155918","2641905","2262545","377823","2669107","2170907","1691715","2707669","405447","2090450","37230790","2148537","2160645","1450446","2106609","2284785","238174","1370551","62565783","705988","2059013","2719249","1727920","3137985","1475088","3001539","2091010","2148996","1416090","2156438","1510554","2719276","101583","2091274","1844154","2087459","2170814","2089455","2102625","2160659","2106992","2262371","1776309","3004669","2148237","3004674","2151639","2150546","1385078","525229","2089871","2089494","3137935","1515973","2184262","739443","2719383","3116909","2163580","1966066","2164608","2156664","2153221","561443","1708302","2390869","2151377","1385908","2162839","3117777","1915799","2091124","1801905","2089132","2151865","1947032","3004961","285514","1957157","2719280","1991235","1849064","2160028","2262345","2719342","1932830","3004574","2176313","2331734","1551181","221403","2149124","2089053","3004540","2047435","1849919","2151411","2262580","416014","2088056","2170380","2091313","2719436","1758772","2091318","3137780","90937","56228","1888758","2087842","1939170","2107086","355451","1847997","3003822","3004716","2156357","2261886","2092444","1699681","2164573","3004570","1417532","2087825","2165262","2142015","161393","2086949","194297","2162339","2719362","2089826","458271","2262088","3138645","439562","2157412","2641915","438687","418919","2162567","1523605","1510440","2262094","1804359","2150150","1614867","3004492","2085906","217051","2179052","1983928","3004578","2153581","347648","2064626","1940959","1915062","1984556","2090223","2106659","3005085","1366905","510317","2180435","2715122","2088429","2719334","1481744","2155888","3004450","2135900","1862880","2086071","2719196","3015680","279972","2157805","2154606","2190640","2090535","2086510","127323","2179845","2088608","1355272","1899722","2084744","2151800","2150784","2180499","129378","342558","441635","583237","2086249","2719214","1955110","278580","4698","617690","89197","1762672","2162420","2719229","2148966","1404628","1793492","2169812","2162507","2149209","1961854","1715881","518818","392331","358777","489741","2169368","428129","2150519","1914806","1777218","2193445","2719271","2177295","115346","101931","2107031","2065155","1866963","2120273","2158958","1739715","2169619","2157829","477171","2084731","2086039","1928503","2153590","2163279","3004976","110562","2141054","2657961","2155143","1455991","1414126","2175791","496981","2222442","2154048","110521","2088058","2106801","732997","2153216","2185282","2098113","1732158","2331147","726760","21986208","1817768","3119462","447885","1954288","2155131","548962","2087134","2157919","1601540","2331601","64458")
    dealUidByAppend(myList,ss)
  }

  /**
   * 通过数据库添加用户
   */
  def updateUids(): Unit = {
    val ss=MySparkSession.conn()
    val sqlBase=new MySqlBaseDao()
    val myList = ListBuffer[String]()
    sqlBase.getCacheTable("t_cdb_member",
      """
        |(select uid from db_userportrait_test.cdb_members_test)t
        |""".stripMargin,ss)
    ss.sql(
      """
        |select a.uid as uid_a from t_cdb_member a
        | left join (select uid from t_user) b
        | on a.uid=b.uid
        | having b.uid is null
        | order by a.uid
        |""".stripMargin).collect().map(x=>{
      myList.append(x.getAs[String]("uid_a"))
    })
    println(myList.length)
    dealUidByAppend(myList,ss)
  }

  def test2(): Unit ={
    val data=
      """
        |{
        |    "status": 0,
        |    "data": {
        |        "uid": "4188235",
        |        "username": "贵州卡友uitgep",
        |        "nickname": "贵州卡友uitgep",
        |        "sjyz": "18285808852",
        |        "unionid": "",
        |        "regdate": "**********",
        |        "lastdate": **********,
        |        "extcredits8": "0",
        |        "status": "0",
        |        "medals": [],
        |        "verify": {
        |            "idcard": "",
        |            "car": ""
        |        },
        |        "ORC": {
        |            "idcard": "",
        |            "driverLicense": "",
        |            "travelCard": "",
        |            "others": ""
        |        },
        |        "level": {
        |            "level": 1,
        |            "score": 0,
        |            "title": "卡车小伙",
        |            "downlimit": 0,
        |            "uplimit": 10,
        |            "progressbar": "0.00"
        |        },
        |        "follow": 0,
        |        "follower": 0,
        |        "subforum": [],
        |        "gender": 0,
        |        "bday": "1970-01-01",
        |        "isNiuren": "0",
        |        "niurenLevel": null,
        |        "user_cars": 2,
        |        "user_driver": 3,
        |        "badge": null
        |    },
        |    "msg": ""
        |}
        |""".stripMargin
    val badge=if (JSON.parseObject(data).getJSONObject("data").getJSONArray("badge")==null) null else JSON.parseObject(data).getJSONObject("data").getJSONArray("badge").map(x=>{
      val obj=JSON.parseObject(x.toString)
      new JSONObject(obj.filterKeys(List("id","name","level").contains(_))).toString
    }).mkString("[",",","]")
    println(badge)
  }

  def test3(): Unit = {
    val spark=MySparkSession.conn()
    val a = (1 to 4406161).toSet
    val b= spark.sql(
      """
        |select distinct uid from t_user
        |""".stripMargin).collect().map(_.getAs[Int]("uid")).toSet
    val myList = ListBuffer[String]()
    (a -- b).foreach(x=>myList.append(x.toString))
//    println(myList.length)
//    println(myList.slice(1, 20).mkString(","))
    dealUidByAppend(myList,spark)
  }

  def main(args: Array[String]): Unit = {
//    val day="20230716"
//    val spark=MySparkSession.conn()
    test3()
//    FetchUserDataAlibaba.dealDailyUid(spark,day)
//    dealUidByAppend(ListBuffer[String]("4176610"),spark)
  }

  /**
   * 新增 lastvisit字典，同步历史数据
   */
  def alterFiledFromMysql(): Unit ={
    val spark=MySparkSession.conn()
    val sqlBase=new MySqlBaseDao("bigdata_test2")
    //从mysql数据库查出lastvisit字段数据
    sqlBase.getCacheTable("t_lastvisit",
      """
        |(
        |select uid,lastvisit from test
        |)t
        |""".stripMargin,spark)
    //    spark.sql(
    //      """
    //        |select * from t_lastvisit
    //        |""".stripMargin).show(false)
    //    return
    //计算数据
    spark.sql(
      """
        |insert overwrite table db_test.t_user_lxq
        |select a.*,nvl(b.lastvisit,"") as lastvisit from t_user as a
        |left join t_lastvisit as b on a.uid=b.uid
        |""".stripMargin)
    spark.stop()
  }

}

