package com.che.hadoop.logclean.bo.discard

import java.util.ResourceBundle

import com.alibaba.fastjson.JSON
import com.che.hadoop.underlay.dao.{MySparkSession, MySqlBaseDao}
import org.apache.spark.sql.SparkSession

object ReadExcel {
  case class AskRecord(id: Int, user_tel: String, ask_type:String, truck_list:String,
                       is_buycar: String, buycar_province: String, buycar_city: String,
                       buycar_county: String,buycar_price_min: String, buycar_price_max: String,
                       buycar_date: String, part_type: String, part_cateid: String,
                       part_subcateid: String, part_brandid: String,
                       part_seriesid: String, part_cate: String, part_subcate: String,
                       part_brand: String, part_series: String, ask_park_aim: String,
                       policy: String,after_sale_service:String,complain:String,
                       complain_type: String,complain_name:String,complain_province:String,
                       complain_city: String,complain_county:String,remarks:String,
                       ask_date: String,create_time:String,status:String,
                       day: String,buycar_city_code:String,buycar_province_code:String,buycar_county_code:String)


  def main(args: Array[String]): Unit = {
    driver

  }

  def driver(): Unit ={
    val spark = MySparkSession.conn()
    val sqlBase = new MySqlBaseDao()
    sqlBase.getCacheTable("t_car_tmp","(select id,user_id userId,hpzl_key carType,series_name truckName,car_model_id truckId," +
      "concat(province,license_plate_number) as truckNumber,`status`,brand_id brandId,brand_name brandName,series_id seriesId," +
      "filter_name seriesName,sub_category_id subCategoryId, " +
      "from_unixtime(add_time) createTime,save_time timestamp,'insert' as operationState,from_unixtime(save_time,'%Y%m%d') as day from pic_user_car_list) as t",spark)
    val t = spark.sql("select  * from t_car_tmp")
    spark.sql(s"select id, userId, carType, truckName, truckId, truckNumber, status, " +
      s"brandId,brandName,seriesId,seriesName,subCategoryId,createTime,timestamp, operationState, day " +
      s"from t_car_certification where id >2388").union(t).createOrReplaceTempView("t_car_certification_tmp")
    spark.sql("insert overwrite table t_car_certification select * from t_car_certification_tmp ")
  }

  def kfData(): Unit ={
    val spark = MySparkSession.conn()
    import spark.implicits._
    val sexMap = Map("1"->"男","2"->"女")
    val identityMap = Map("0"->"","1"->"","2"->"个体司机","3"->"车主","4"->"物流公司","5"->"货运公司","6"->"车队老板",
      "7"->"经销商工作人员","8"->"厂家工作人员")
    val askTypeMap = Map("1"->"买车","2"->"配件","3"->"厂商电话","4"->"政策咨询","5"->"服务站","6"->"求职",
      "7"->"卡车之家活动","8"->"论坛","9"->"入驻卡车之家")
    val buyCarDate = Map("0"->"","1"->"近一周","2"->"近十五天","3"->"近一个月","4"->"近两个月","5"->"近半年","9"->"暂无")
    val partMap = Map("0"->"","1"->"发动机","2"->"变速箱","3"->"车桥","4"->"轮胎","5"->"机油","6"->"尿素",
      "7"->"保险杠","8"->"驻车加热器","9"->"车载空调","10"->"液力缓速器","11"->"车灯","12"->"驾驶室总成","13"->"底盘",
      "14"->"柴油滤清器","15"->"机油滤清器","16"->"空气滤清器","17"->"车轮","18"->"变速箱油",
      "19"->"润滑脂","20"->"干燥罐","21"->"车载冰箱","22"->"差速器","23"->"车用逆变器",
      "24"->"车身反光贴","25"->"行车记录仪","26"->"卡车座椅","27"->"电机","28"->"电瓶","29"->"齿轮油","30"->"空气悬挂",
      "31"->"油封","32"->"刹车片","33"->"液压尾板","34"->"混合动力总成","35"->"卡车坐垫","36"->"后视镜","37"->"车门",
      "38"->"加装货厢","39"->"加装栏板","40"->"车窗")
    val aimMap = Map("0"->"","1"->"购买","2"->"维修")
    val zczxMap = Map("0"->"","1"->"违章罚款","2"->"合格证","3"->"超载","4"->"ETC")
    val shfwMap = Map("0"->"","1"->"保养","2"->"维修")
    val tsMap = Map("0"->"","1"->"服务站","2"->"经销商")
    val userMap = scala.collection.mutable.Map[String,String]()
    val userMapBc = spark.sparkContext.broadcast(userMap)
      spark.read
      .format("com.crealytics.spark.excel")
      .option("useHeader", "true") // 是否将第一行作为表头
      //      .option("inferSchema", "true") // 是否推断schema
      .option("sheetName", "Sheet2")
      .load("D:\\Documents\\客服系统数据导出(1).xlsx").map(f=>{
//        println(f)
        //id	user_name	tel	微信	微信昵称	性别	年龄	身份	省份编号	省份	城市编号	城市	区县编号	区县13	驾驶证级别
        // 车型信息15	创建时间 16 day17	是否删除 18
        val sex = if(sexMap.contains(f.getString(5))) sexMap(f.getString(5)) else ""
        val identity = identityMap(f.getString(7))
        var day = f.getString(17)
        if(day == "1753-01-01 00:00:00.000"){
          day="20200622"
        }
        var truckList = f.getString(15)
          if(truckList!=null){
            truckList = truckList.replace("LargeClass","cateId")
          .replace("Subclass","subcateId")
          .replace("Brand","brandId")
          .replace("Series","seriesId")
          .replace("LargeClassName","cate")
          .replace("SubclassName","subcate")
          .replace("BrandName","brand")
          .replace("SeriesName","series") }
      ( f.getString(0).toInt,
        f.getString(1),
        f.getString(2),
        f.getString(3),f.getString(4),sex,f.getString(6),identity,f.getString(8),f.getString(9),
        f.getString(10),f.getString(11),f.getString(12),f.getString(13),truckList,
        f.getString(16),f.getString(18),day)
    }).toDF("id","user_name","user_tel","wechat_num","wechat_name","sex","age","identity","province_code",
      "province","city_code","city","county_code","county","trucklist","create_time","status","day")
        .repartition(1).write.mode("append")
        .format("hive")
        .saveAsTable("t_dwd_kf_user_info")
    spark.sql("select id,user_tel from t_dwd_kf_user_info").collect().foreach(f=>{
      userMapBc.value += (f.getInt(0).toString->f.getString(1))
    })
    spark.read
      .format("com.crealytics.spark.excel")
      .option("useHeader", "true") // 是否将第一行作为表头
      //      .option("inferSchema", "true") // 是否推断schema
      .option("sheetName", "Sheet1")
      .load("D:\\Documents\\ask_record.xlsx").map(f=>{
      println(f)
      //0编号	1客户Id	2咨询类型	3咨询车辆	4致电时间	5是否有购车意向	6目标购车区域-省	 7目标购车区域-省名称	8目标购车区域-市
      // 9目标购车区域-市名称	10目标购车区域-区	11目标购车区域-区名称
      // 12价格区间-最小	  13价格区间-最大	14预计购买时间	 15咨询配件	16关联车系	17咨询目的	18政策咨询	19售后服务	20是否投诉
      // 21被投诉商家名称	22投诉类型	23投诉对象所在区域-省	24投诉对象所在区域-省名称	25投诉对象所在区域-市	26投诉对象所在区域-市名称
      // 27投诉对象所在区域-区	28投诉对象所在区域-区名称	29备注	30创建时间	31是否删除
      val userTel = userMapBc.value(f.getString(1))
      var day = f.getString(32)
      if(day == "1753-01-01 00:00:00.000"){
        day="20200622"
      }
      var part_cateid = "0"
      var part_subcateid = "0"
      var part_brandid = "0"
      var part_seriesid = "0"
      var part_cate = ""
      var part_subcate = ""
      var part_brand = ""
      var part_series = ""
      val partBrandList = JSON.parseArray(f.getString(16))
      if(partBrandList.size()!=0){
        val pl = partBrandList.getJSONObject(0)
        part_cateid = pl.getString("LargeClass")
        part_subcateid = pl.getString("Subclass")
        part_brandid = pl.getString("Brand")
        part_seriesid = pl.getString("Series")
        part_cate = pl.getString("LargeClassName")
        part_subcate = pl.getString("SubclassName")
        part_brand = pl.getString("BrandName")
        part_series = pl.getString("SeriesName")
      }
      var truckList = f.getString(3)
      if(truckList!=null){
        truckList = truckList.replace("LargeClass","cateId")
          .replace("Subclass","subcateId")
          .replace("Brand","brandId")
          .replace("Series","seriesId")
          .replace("LargeClassName","cate")
          .replace("SubclassName","subcate")
          .replace("BrandName","brand")
          .replace("SeriesName","series") }
      val askTypeList = f.getString(2).split(",").map(f=>askTypeMap(f)).mkString(",")
      AskRecord( f.getString(0).toInt,
        userTel,
        askTypeList,
        truckList,f.getString(5),f.getString(7),f.getString(9),f.getString(11),f.getString(12),f.getString(13),
        buyCarDate(f.getString(14)),
        partMap(f.getString(15)),part_cateid,part_subcateid,part_brandid,part_seriesid,part_cate,part_subcate,part_brand,part_series,
        aimMap(f.getString(17)),zczxMap(f.getString(18)),
        shfwMap(f.getString(19)),f.getString(20),tsMap(f.getString(22)),
        f.getString(21),f.getString(24),f.getString(26),f.getString(28),f.getString(29),f.getString(4),
        f.getString(30),f.getString(31),day,f.getString(8),f.getString(6),f.getString(10))
    })
      .repartition(1).write.mode("append")
      .format("hive")
      .saveAsTable("t_dwd_kf_ask_record")
  }

  /**
   * 获取帖子历史数据，写入到Hive
   */
  def getPostData(): Unit = {


    val spark = SparkSession.builder().master("local").getOrCreate()
    //val spark = MySparkSession.conn
    val data = spark.read
      .format("com.crealytics.spark.excel")
      .option("useHeader", "true") // 是否将第一行作为表头
      .option("inferSchema", "true") // 是否推断schema
      .load("D:\\a.xls") //excel文件路径 + 文件名

    // 结果写入Hive
    data.drop("subject", "message")
      .write.mode("append")
      .format("hive")
      .saveAsTable("db_test.t_bbs_post")

    //val data = spark.sql("select * from db_test.t_bbs_post")

    val reader = ResourceBundle.getBundle("connection")
    val url = reader.getString("portrait.url")
    val username = reader.getString("portrait.username")
    val password = reader.getString("portrait.password")
    //将结果写入到Mysql
    data.write.mode("append")
      .format("jdbc")
      .option("url", url)
      .option("dbtable", "t_bbs_post_bak") //表名
      .option("user", username)
      .option("password", password)
      .save()

    spark.stop()
  }

  /**
   * 写出到 Excel
   */
  def saveAsExcel(): Unit = {

    val spark = SparkSession.builder().master("local").getOrCreate()
    //val spark = MySparkSession.conn

    val data = spark.read
      .format("com.crealytics.spark.excel")
      .option("useHeader", "true")
      .option("inferSchema", "false")
      .load("D:\\bbs_data\\12.xlsx").filter("subject not like '%山东尚联运输有限公司%'")
//      .union(
//      spark.read
//        .format("com.crealytics.spark.excel")
//        .option("useHeader", "true")
//        .option("inferSchema", "false")
//        .load("D:\\bbs_data\\8.xlsx")
//    ).union(
//      spark.read
//        .format("com.crealytics.spark.excel")
//        .option("useHeader", "true")
//        .option("inferSchema", "false")
//        .load("D:\\bbs_data\\9.xlsx")
//    ).union(
//      spark.read
//        .format("com.crealytics.spark.excel")
//        .option("useHeader", "true")
//        .option("inferSchema", "false")
//        .load("D:\\bbs_data\\10.xlsx")
//    ).union(
//      spark.read
//        .format("com.crealytics.spark.excel")
//        .option("useHeader", "true")
//        .option("inferSchema", "false")
//        .load("D:\\bbs_data\\11.xlsx")
//    ).union(
//      spark.read
//        .format("com.crealytics.spark.excel")
//        .option("useHeader", "true")
//        .option("inferSchema", "false")
//        .load("D:\\bbs_data\\12.xlsx")
//    )

//    def readExcel(f: String): DataFrame = {
//      spark.read
//        .format("com.crealytics.spark.excel")
//        .option("location", f)
//        .option("useHeader", "true")
//        .option("inferSchema", "false")
//        .load()
//    }

//    val dir = new File("D:\\bbs_data")
//    val excelFiles = dir.listFiles.sorted.map(f => f.toString)  // Array[String]
//    val dfs = excelFiles.map(f => readExcel(f))  // Array[DataFrame]
//    val data = dfs.reduce(_.union(_))  // DataFrame

    data.createOrReplaceTempView("t_tmp")

    spark.sql(
      """
        |select
        |  tid,
        |  subject as title,
        |  dateline,
        |  concat('https://bbs.360che.com/thread-', tid, '-1-1.html') as url,
        |  recommend_add as like,
        |  replies
        |from t_tmp
        |""".stripMargin)
      .write
      .format("com.crealytics.spark.excel")
      .option("useHeader", "true")
      .option("inferSchema", "false")
//      .option("workbookPassword", "None")
      .save("D:\\bbs_12.xlsx")

    spark.stop()
  }

  /**
    * 计算半年内的帖子pv
    */
  def getPostPv(): Unit ={
    val spark = MySparkSession.conn()
    // thread-
    spark.sql("select regexp_extract(k, 'thread-(\\\\d+)', 1) AS tid, k from t_logdata where day between '20190901' and '20200212'")
      .filter("k like '%bbs.360che.com%'")
      .union(
    // tid=
    spark.sql("select regexp_extract(k, 'tid=(\\\\d+)', 1) AS tid, k from t_logdata where day between '20190901' and '20200212'")
      .filter("k like '%bbs.360che.com%'"))
      .union(
    // APP
    spark.sql("select lable AS tid, 'app' from t_logdata_app where day between '20190901' and '20200212' and eventid in ('CHES_00000026', 'CHE_00000066') and t='event'")
    ).union(
    // 老版APP已删除标签
    spark.sql("select lable AS tid, 'app' from t_logdata_app where day between '20190901' and '20200212' and eventid = 'CHE_00000055'")
    ).filter("tid != ''").createOrReplaceTempView("t_tid")

    // 计算pv写入到Hive表中
    spark.sql("select tid, count(tid) as pv from t_tid group by tid")
      .write.mode("append").format("hive")
      .saveAsTable("db_test.t_bbs_pv_1")

    spark.stop()
  }

  /**
    * Excel写入Hive
    */
  def saveExcelToTable(): Unit ={
    val spark = MySparkSession.conn()
    spark.read
         .format("com.crealytics.spark.excel")
         .option("useHeader", "true")
         .option("inferSchema", "false")
         .load("F:\\bbs_data\\bbs_for_label.xlsx")
        .write.mode("append").format("hive")
        .saveAsTable("db_test.t_bbs_for_label")
    spark.stop()
  }



}
