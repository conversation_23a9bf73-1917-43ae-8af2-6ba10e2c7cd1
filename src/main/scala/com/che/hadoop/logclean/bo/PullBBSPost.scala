package com.che.hadoop.logclean.bo

import java.util.ResourceBundle

import com.alibaba.fastjson.JSON
import com.che.hadoop.underlay.dao
import com.che.hadoop.logclean.utils.Common
import com.che.hadoop.underlay.dao.{ MySqlBaseDao, MySparkSession}
import org.apache.spark.sql.SparkSession
import scalaj.http.Http
import org.apache.spark.sql.functions._
import scala.collection.mutable.ListBuffer

/**
 * 从业务数据清洗帖子数据
 */
object PullBBSPost {
  //社区
  case class Bbs(uid :String, nickname :String, bbs_tid :String, bbs_pid :String, subject :String, message :String, bbs_publish_time :String, client :String, bbs_type :String, coverurl :String, videourl :String,  imgurl :String, position :String, bbs_domain :String, bbs_subcate :String, bbs_series :String,  bbs_brand :String, bbs_first_label :String, bbs_second_label :String, bbs_third_label :String, bbs_scenario :String,  is_dealer :String,  relationid :String,  isexcellent :String,  excellent_date :String,  timestamp :String,  operationState :String, update_time :String)
  def main(args: Array[String]): Unit = {
    val spark=MySparkSession.conn()
    val sqlBase=new MySqlBaseDao()
    getBBSCircle(spark,sqlBase,"20230418")
  }

  /**
   * 从业务数据清洗帖子数据,该方法report工程在数据切换后取消
   * 语雀文档地址：https://360che.yuque.com/technology_team/rof05c/197623986
   * @param day 计算日期
   */
  def pullPostToODSOld(spark:SparkSession,day: String): Unit = {
    val t_check_pre="aliyun_"
    try {
      //01帖子帖子打标签
      val bbsPostLabelTable="t_ods_post_tag"
      val bbsPostLabel = spark.sql(
        """
          |select json_tuple(data, 'tid', 'domain', 'subcate','series','brand','firstLabel','secondLabel','thirdLabel','bbsScenario',"relationId")
          |,`timestamp`,operation_state,from_unixtime(substr(`timestamp`,0,10), 'yyyyMMdd')
          |from t_biz_data_temp where billtype='bbs_post' and env !='test'
          |""".stripMargin)
        .toDF("tid", "domain", "subcate", "series", "brand", "firstLabel", "secondLabel", "thirdLabel", "bbsScenario","relationId","timestamp", "operationState", "day")
        .filter(f => f.get(1) != null)
      //写数据之前删除当天日期数据
      spark.sql(s"alter table $bbsPostLabelTable drop if exists partition (day='$day')")
      bbsPostLabel.write.mode("append").partitionBy("day").format("Hive")
        .saveAsTable(bbsPostLabelTable)
      //02发帖数据
      val bbsPostTable="t_ods_bbs_post"
      val bbsPost = spark.sql(
        """
          |select json_tuple(data,'uid','nickName','tid','pid','title','content','postTime','client','type','coverUrl','videoUrl','imgUrl','position','bbsScenario','isDealer','subcate','series','brand'),
          |`timestamp`,operation_state,from_unixtime(substr(`timestamp`,0,10), 'yyyyMMdd')
          |from t_biz_data_temp
          |""".stripMargin)
        .toDF("uid", "nickname", "tid", "pid", "title", "content", "post_time",
          "client", "type", "cover_url", "video_url", "img_url", "position", "bbsScenario","isDealer","subcate","series","brand","timestamp", "operationState", "day")
        .filter(f => f.get(3) != null)
//      spark.sql(s"alter table $bbsPostTable drop if exists partition (day='$day')")
//      bbsPost.withColumnRenamed("subcate","subcate_id").
//        withColumnRenamed("series","series_id").
//        withColumnRenamed("brand","brand_id")
//        .write.mode("append").partitionBy("day").format("Hive")
//        .saveAsTable(bbsPostTable)
      //03帖子加精数据
      val bbsPostExcellentTable="t_ods_post_excellent"
      val bbsPostExcellent = spark.sql(
        """
          |select json_tuple(data, 'tid', 'isExcellent', 'excellentDate'),`timestamp`,from_unixtime(substr(`timestamp`,0,10), 'yyyyMMdd')
          |from t_biz_data_temp
          |""".stripMargin)
        .toDF("tid", "is_excellent", "excellent_date", "insert_timestamp", "day")
        .filter(f => f.get(1) != null)
        .withColumn("tid",col("tid").cast("Int"))
        .withColumn("is_excellent",col("is_excellent").cast("Int"))
      spark.sql(s"alter table $bbsPostExcellentTable drop if exists partition (day='$day')")
      bbsPostExcellent.write.mode("append").partitionBy("day").format("Hive")
        .saveAsTable(bbsPostExcellentTable)
      val exec = new MySqlBaseDao()
      exec.executeInsertOrUpdate(s"update t_check set flag=1 , message='$day:运行成功' where name='${t_check_pre}t_ods_post_tag'",null)
    } catch {
      case e: Exception => {
        e.printStackTrace()
        try{
          val exec = new dao.MySqlBaseDao()

          exec.executeInsertOrUpdate(s"update t_check set flag=2, message='$day:${t_check_pre}t_ods_post_tag 运行失败' where name='${t_check_pre}t_ods_post_tag'",null)
        }catch {
          case e:Throwable=>{
            e.printStackTrace()
          }
        }
      }
    }
  }

  /***
   * 拉取十大热帖排名接口数据
   * @param day 拉取数据日期
   */
  def tenPosts(spark:SparkSession,day: String): Unit ={
    val t_check_pre="aliyun_"
    val exec = new MySqlBaseDao()
    try{
      //测试接口   app
      val sourceUrlApp = ResourceBundle.getBundle("connection").getString("api.ten_hot_thread.app")
      //  pc测试接口
      val sourceUrlPc = ResourceBundle.getBundle("connection").getString("api.ten_hot_thread.pc")
      val action = "TenHotThread"
      val method = "theDay"
      val pullDate = Common.transFormat(day,"yyyyMMdd","yyyy-MM-dd")
      //请求接口获取Json
      val responseAPP = Http(sourceUrlApp).param("date",pullDate).asString
      println(responseAPP)
      val resultJson = JSON.parseObject(responseAPP.body)
      println(resultJson)
      // 判断接口返回的状态，若失败直接return失败信息，程序不再往下进行
      if (resultJson.getInteger("status") == 1) {
        exec.executeInsertOrUpdate(s"update t_check set flag=2, message='$day:${t_check_pre}t_ods_bbs_post_rank 运行失败' where name='${t_check_pre}t_ods_bbs_post_rank'",null)
      }

      val responsePc = Http(sourceUrlPc).param("action",action).param("method",method).param("date",pullDate).asString
      println(responsePc)
      val resultJsonPc = JSON.parseObject(responsePc.body)
      println(resultJsonPc)
      // 判断pc接口返回的状态，若失败直接return失败信息，程序不再往下进行
      if (resultJsonPc.getInteger("status") == 1) {
        exec.executeInsertOrUpdate(s"update t_check set flag=2, message='$day:${t_check_pre}t_ods_bbs_post_rank 运行失败' where name='${t_check_pre}t_ods_bbs_post_rank'",null)
      }
      // app 存储
      val dataListApp = new ListBuffer[(String,Int,String,String,String)]
      // pc 存储
      val dataListPc = new ListBuffer[(String,Int,String, String,String)]

      val jsonArrayApp = resultJson.getJSONArray("data")
      val jsonArrayPc = resultJsonPc.getJSONArray("data")

      import spark.implicits._
      //app获取数据
      for(item <- 0 until jsonArrayApp.size()){
        val argObj = jsonArrayApp.getJSONObject(item)
        val turnDate = argObj.getString("dateline")
        val os ="APP"
        dataListApp.append((
          argObj.getString("tid"),
          argObj.getInteger("rank"),
          turnDate,day,os
        ))
      }
      val dateDFApp =  dataListApp.toDF("tid","rank","time","day","os")
      dateDFApp.createOrReplaceTempView("t_app")

      //PC获取数据
      for(item <- 0 until jsonArrayPc.size()){
        val argObj = jsonArrayPc.getJSONObject(item)
        val turnDate = argObj.getString("dateline")
        val os ="PC"
        dataListPc.append((
          argObj.getString("tid"),
          argObj.getInteger("rank"),
          turnDate,day,os
        ))
      }
      val dateDFPc =  dataListPc.toDF("tid","rank","time","day","os")
      dateDFPc.createOrReplaceTempView("t_pc")


      val dataDF = spark.sql(
        """
          |select tid,rank,time,day,os
          |from (
          |select tid,rank,time,day,os from t_app
          |union  all
          |select tid,rank,time,day,os from t_pc
          |) as a
          |""".stripMargin)
      //删除数据
      spark.sql("alter table t_ods_bbs_post_rank drop if exists partition(day='" + day + "')")
      //保存数据到数据库
      dataDF.repartition(1).write.mode("append").format("Hive").partitionBy("day").saveAsTable("t_ods_bbs_post_rank")
      resultJson.getString("msg")
      resultJsonPc.getString("msg")
      exec.executeInsertOrUpdate(s"update t_check set flag=1 , message='$day:运行成功' where name='${t_check_pre}t_ods_bbs_post_rank'",null)
    }catch {
      case e:Exception=>{
        e.printStackTrace()
        try{
          val exec = new dao.MySqlBaseDao()

          exec.executeInsertOrUpdate(s"update t_check set flag=2, message='$day:${t_check_pre}t_ods_bbs_post_rank 运行失败' where name='${t_check_pre}t_ods_bbs_post_rank'",null)
        }catch {
          case e:Throwable=>{
            e.printStackTrace()
          }
        }
      }
    }

  }

  /**
   * 从业务数据清洗帖子数据
   * 语雀文档地址：https://360che.yuque.com/technology_team/rof05c/197623986
   * @param day 计算日期
   */
  def pullPostToODS(spark:SparkSession,day: String): Unit = {
    val t_check_pre="aliyun_"
    try {
      val bbsPostTable="t_ods_bbs_post"
      val bbsPostDF = spark.sql(
        s"""
          |select json_tuple(data,
          |-- 发帖数据
          |'uid', 'nickName', 'tid', 'pid','title', 'content', 'postTime',
          |'client', 'type','coverUrl', 'videoUrl','imgUrl','position',
          |--打标签数据
          |'domain','subcate', 'series', 'brand','firstLabel',
          | 'secondLabel', 'thirdLabel','bbsScenario','isDealer','relationId',
          |-- 精华数据
          |'isExcellent','excellentDate')
          |,`timestamp`,operation_state,from_unixtime(substr(`timestamp`,0,10), 'yyyyMMdd'),
          |$day as day
          |from t_biz_data_temp where billtype='bbs_post' and env='prod'
          |""".stripMargin)
        .toDF("uid" ,"nickname" ,"bbs_tid","bbs_pid","subject","message" ,"bbs_publish_time",
                         "client","bbs_type","coverurl","videourl","imgurl","position",
                         "bbs_domain","bbs_subcate","bbs_series","bbs_brand","bbs_first_label",
          "bbs_second_label","bbs_third_label","bbs_scenario","is_dealer","relationid","isexcellent","excellent_date",
          "timestamp", "operationState", "update_time","day")
        .withColumn("bbs_tid",col("bbs_tid").cast("Long"))
        .withColumn("bbs_pid",col("bbs_pid").cast("Long"))
        .withColumn("bbs_type",col("bbs_type").cast("Int"))
        .withColumn("isexcellent",col("isexcellent").cast("Int"))
      //写数据之前删除当天日期数据
      spark.sql(s"alter table $bbsPostTable drop if exists partition (day='$day')")
      bbsPostDF.write.mode("append").partitionBy("day").format("Hive")
        .saveAsTable(bbsPostTable)
      val exec = new MySqlBaseDao()
      exec.executeInsertOrUpdate(s"update t_check set flag=1 , message='$day:运行成功' where name='${t_check_pre}t_ods_bbs_post'",null)
    } catch {
      case e: Exception => {
        e.printStackTrace()
        try{
          val exec = new dao.MySqlBaseDao()

          exec.executeInsertOrUpdate(s"update t_check set flag=2, message='$day:${t_check_pre}t_ods_bbs_post 运行失败' where name='${t_check_pre}t_ods_bbs_post'",null)
        }catch {
          case e:Throwable=>{
            e.printStackTrace()
          }
        }
      }
    }
  }

  /**
   * 从业务数据清洗帖子数据到dwd层
   * 语雀文档地址：https://360che.yuque.com/technology_team/rof05c/197623986
   * @param day 计算日期
   */
  def pullPostToDWD(spark:SparkSession,day: String): Unit = {

    val t_check_pre="aliyun_"
    val odsTable="t_ods_bbs_post"
    val dwdTable="t_dwd_bbs_post"
    import spark.implicits._
    try {
      spark.sql(
        s"""
          |SELECT uid, nickname, bbs_tid, bbs_pid, subject, message, bbs_publish_time, client, bbs_type, coverurl, videourl, imgurl, `position`, bbs_domain, bbs_subcate, bbs_series, bbs_brand, bbs_first_label, bbs_second_label, bbs_third_label, bbs_scenario, is_dealer, relationid, isexcellent, excellent_date, `timestamp`, operationstate, update_time
          |FROM $odsTable where day="$day"
          |""".stripMargin)
        .as[Bbs]
        .groupByKey(f => f.bbs_tid)
        .mapGroups((bbs_tid, bbs_attrs) => {
          val result = new Array[String](28)
          var timestampMax = 0l
          for (bbs_attr <- bbs_attrs.toSeq.sortBy(_.timestamp.toLong)) {
            if (bbs_attr.timestamp.toLong > timestampMax) {
              timestampMax = bbs_attr.timestamp.toLong
              if (bbs_attr.uid != null && !bbs_attr.uid.isEmpty) {
                result(0) = bbs_attr.uid
              }
              if (bbs_attr.nickname != null && !bbs_attr.nickname.isEmpty) {
                result(1) = bbs_attr.nickname
              }
              if (bbs_attr.bbs_tid != null && !bbs_attr.bbs_tid.isEmpty) {
                result(2) = bbs_attr.bbs_tid
              }
              if (bbs_attr.bbs_pid != null && !bbs_attr.bbs_pid.isEmpty) {
                result(3) = bbs_attr.bbs_pid
              }
              if (bbs_attr.subject != null && !bbs_attr.subject.isEmpty) {
                result(4) = bbs_attr.subject
              }
              if (bbs_attr.message != null && !bbs_attr.message.isEmpty) {
                result(5) = bbs_attr.message
              }
              if (bbs_attr.bbs_publish_time != null && bbs_attr.bbs_publish_time.startsWith("2")) {
                result(6) = bbs_attr.bbs_publish_time
              }
              if (bbs_attr.client != null && !bbs_attr.client.isEmpty) {
                result(7) = bbs_attr.client
              }
              if (bbs_attr.bbs_type != null && !bbs_attr.bbs_type.isEmpty) {
                result(8) = bbs_attr.bbs_type
              }
              if (bbs_attr.coverurl != null && !bbs_attr.coverurl.isEmpty) {
                result(9) = bbs_attr.coverurl
              }
              if (bbs_attr.videourl != null && !bbs_attr.videourl.isEmpty) {
                result(10) = bbs_attr.videourl
              }
              if (bbs_attr.imgurl != null && !bbs_attr.imgurl.isEmpty) {
                result(11) = bbs_attr.imgurl
              }
              if (bbs_attr.position != null && !bbs_attr.position.isEmpty) {
                result(12) = bbs_attr.position
              }
              if (bbs_attr.bbs_domain != null && !bbs_attr.bbs_domain.isEmpty) {
                result(13) = bbs_attr.bbs_domain
              }
              if (bbs_attr.bbs_subcate != null && !bbs_attr.bbs_subcate.isEmpty) {
                result(14) = bbs_attr.bbs_subcate
              }
              if (bbs_attr.bbs_series != null && !bbs_attr.bbs_series.isEmpty) {
                result(15) = bbs_attr.bbs_series
              }
              if (bbs_attr.bbs_brand != null && !bbs_attr.bbs_brand.isEmpty) {
                result(16) = bbs_attr.bbs_brand
              }
              if (bbs_attr.bbs_first_label != null && !bbs_attr.bbs_first_label.isEmpty) {
                result(17) = bbs_attr.bbs_first_label
              }
              if (bbs_attr.bbs_second_label != null && !bbs_attr.bbs_second_label.isEmpty) {
                result(18) = bbs_attr.bbs_second_label
              }
              if (bbs_attr.bbs_third_label != null && !bbs_attr.bbs_third_label.isEmpty) {
                result(19) = bbs_attr.bbs_third_label
              }
              if (bbs_attr.bbs_scenario != null && !bbs_attr.bbs_scenario.isEmpty) {
                result(20) = bbs_attr.bbs_scenario
              }
              if (bbs_attr.is_dealer != null && !bbs_attr.is_dealer.isEmpty) {
                result(21) = bbs_attr.is_dealer
              }
              if (bbs_attr.relationid != null && !bbs_attr.relationid.isEmpty) {
                result(22) = bbs_attr.relationid
              }
              if (bbs_attr.isexcellent != null && !bbs_attr.isexcellent.isEmpty) {
                result(23) = bbs_attr.isexcellent
              }
              if (bbs_attr.excellent_date != null && !bbs_attr.excellent_date.isEmpty) {
                result(24) = bbs_attr.excellent_date
              }
              if (bbs_attr.timestamp != null && !bbs_attr.timestamp.isEmpty) {
                result(25) = bbs_attr.timestamp
              }
              if (bbs_attr.operationState != null && !bbs_attr.operationState.isEmpty) {
                result(26) = bbs_attr.operationState
              }
              if (bbs_attr.update_time != null && !bbs_attr.update_time.isEmpty) {
                result(27) = bbs_attr.update_time
              }
            }
            else {
            if (bbs_attr.uid != null && !bbs_attr.uid.isEmpty && (result(0) == null || result(0).isEmpty)) {
              result(0) = bbs_attr.uid
            }
            if (bbs_attr.nickname != null && !bbs_attr.nickname.isEmpty && (result(1) == null || result(1).isEmpty)) {
              result(1) = bbs_attr.nickname
            }
            if (bbs_attr.bbs_tid != null && !bbs_attr.bbs_tid.isEmpty && (result(2) == null || result(2).isEmpty)) {
              result(2) = bbs_attr.bbs_tid
            }
            if (bbs_attr.bbs_pid != null && !bbs_attr.bbs_pid.isEmpty && (result(3) == null || result(3).isEmpty)) {
              result(3) = bbs_attr.bbs_pid
            }
            if (bbs_attr.subject != null && !bbs_attr.subject.isEmpty && (result(4) == null || result(4).isEmpty)) {
              result(4) = bbs_attr.subject
            }
            if (bbs_attr.message != null && !bbs_attr.message.isEmpty && (result(5) == null || result(5).isEmpty)) {
              result(5) = bbs_attr.message
            }
            if (bbs_attr.bbs_publish_time != null && bbs_attr.bbs_publish_time.startsWith("2") && (result(6) == null || result(6).isEmpty)) {
              result(6) = bbs_attr.bbs_publish_time
            }
            if (bbs_attr.client != null && !bbs_attr.client.isEmpty && (result(7) == null || result(7).isEmpty)) {
              result(7) = bbs_attr.client
            }
            if (bbs_attr.bbs_type != null && !bbs_attr.bbs_type.isEmpty && (result(8) == null || result(8).isEmpty)) {
              result(8) = bbs_attr.bbs_type
            }
            if (bbs_attr.coverurl != null && !bbs_attr.coverurl.isEmpty && (result(9) == null || result(9).isEmpty)) {
              result(9) = bbs_attr.coverurl
            }
            if (bbs_attr.videourl != null && !bbs_attr.videourl.isEmpty && (result(10) == null || result(10).isEmpty)) {
              result(10) = bbs_attr.videourl
            }
            if (bbs_attr.imgurl != null && !bbs_attr.position.isEmpty && (result(11) == null || result(11).isEmpty)) {
              result(11) = bbs_attr.position
            }
            if (bbs_attr.position != null && !bbs_attr.position.isEmpty && (result(12) == null || result(12).isEmpty)) {
              result(12) = bbs_attr.position
            }
            if (bbs_attr.bbs_domain != null && !bbs_attr.bbs_domain.isEmpty && (result(13) == null || result(13).isEmpty)) {
              result(13) = bbs_attr.bbs_domain
            }
            if (bbs_attr.bbs_subcate != null && !bbs_attr.bbs_subcate.isEmpty && (result(14) == null || result(14).isEmpty)) {
              result(14) = bbs_attr.bbs_subcate
            }
            if (bbs_attr.bbs_series != null && !bbs_attr.bbs_series.isEmpty && (result(15) == null || result(15).isEmpty)) {
              result(15) = bbs_attr.bbs_series
            }
            if (bbs_attr.bbs_brand != null && !bbs_attr.bbs_brand.isEmpty && (result(16) == null || result(16).isEmpty)) {
              result(16) = bbs_attr.bbs_brand
            }
            if (bbs_attr.bbs_first_label != null && !bbs_attr.bbs_first_label.isEmpty && (result(17) == null || result(17).isEmpty)) {
              result(17) = bbs_attr.bbs_first_label
            }
            if (bbs_attr.bbs_second_label != null && !bbs_attr.bbs_second_label.isEmpty && (result(18) == null || result(18).isEmpty)) {
              result(18) = bbs_attr.bbs_second_label
            }
            if (bbs_attr.bbs_third_label != null && !bbs_attr.bbs_third_label.isEmpty && (result(19) == null || result(19).isEmpty)) {
              result(19) = bbs_attr.bbs_third_label
            }
            if (bbs_attr.bbs_scenario != null && !bbs_attr.bbs_scenario.isEmpty && (result(20) == null || result(20).isEmpty)) {
              result(20) = bbs_attr.bbs_scenario
            }
            if (bbs_attr.is_dealer != null && !bbs_attr.is_dealer.isEmpty && (result(21) == null || result(21).isEmpty)) {
              result(21) = bbs_attr.is_dealer
            }
            if (bbs_attr.relationid != null && !bbs_attr.relationid.isEmpty && (result(22) == null || result(22).isEmpty)) {
              result(22) = bbs_attr.relationid
            }
            if (bbs_attr.isexcellent != null && !bbs_attr.isexcellent.isEmpty && (result(23) == null || result(23).isEmpty)) {
              result(23) = bbs_attr.isexcellent
            }
            if (bbs_attr.excellent_date != null && !bbs_attr.excellent_date.isEmpty && (result(24) == null || result(24).isEmpty)) {
              result(24) = bbs_attr.excellent_date
            }
            if (bbs_attr.timestamp != null && !bbs_attr.timestamp.isEmpty && (result(25) == null || result(25).isEmpty)) {
              result(25) = bbs_attr.timestamp
            }
            if (bbs_attr.operationState != null && !bbs_attr.operationState.isEmpty && (result(26) == null || result(26).isEmpty)) {
              result(26) = bbs_attr.operationState
            }
            if (bbs_attr.update_time != null && !bbs_attr.update_time.isEmpty && (result(27) == null || result(27).isEmpty)) {
              result(27) = bbs_attr.update_time
            }
          }
          }
          Bbs(result(0), result(1), bbs_tid, result(3),
             result(4),result(5), result(6), result(7),
            result(8), result(9), result(10),result(11),
            result(12),result(13), result(14), result(15),
            result(16), result(17), result(18),result(19),
            result(20), result(21),result(22), result(23),
            result(24),result(25),result(26), result(27))
        }).createOrReplaceTempView("bbs_attrs_level_today")
      spark.sql("set spark.sql.shuffle.partitions=1")
      spark.sql(
        s"""
           |insert overwrite table ${dwdTable}
           |select coalesce(a.uid,b.uid) as uid ,
           |       coalesce(a.nickname,b.nickname) as nickname,
           |       cast(coalesce(a.bbs_tid,b.bbs_tid) as long) as bbs_tid,
           |       cast(coalesce(a.bbs_pid,b.bbs_pid) as long) as bbs_pid,
           |       coalesce(a.subject,b.subject) as subject,
           |       coalesce(a.message,b.message) as message,
           |       coalesce(a.bbs_publish_time,b.bbs_publish_time) as bbs_publish_time,
           |       coalesce(a.client,b.client) as client ,
           |       cast(coalesce(a.bbs_type,b.bbs_type) as int) as bbs_type,
           |       coalesce(a.coverurl,b.coverurl) as coverurl,
           |       coalesce(a.videourl,b.videourl) as videourl,
           |       coalesce(a.imgurl,b.imgurl) as imgurl ,
           |       coalesce(a.position,b.position) as position ,
           |       coalesce(a.bbs_domain,b.bbs_domain) as bbs_domain,
           |       coalesce(a.bbs_subcate,b.bbs_subcate) as bbs_subcate,
           |       coalesce(a.bbs_series,b.bbs_series) as bbs_series,
           |       coalesce(a.bbs_brand,b.bbs_brand) as bbs_brand,
           |       coalesce(a.bbs_first_label,b.bbs_first_label) as bbs_first_label,
           |       coalesce(a.bbs_second_label,b.bbs_second_label) as bbs_second_label,
           |       coalesce(a.bbs_third_label,b.bbs_third_label) as bbs_third_label,
           |       coalesce(a.bbs_scenario,b.bbs_scenario) as bbs_scenario,
           |       coalesce(a.is_dealer,b.is_dealer) as is_dealer,
           |       coalesce(a.relationid,b.relationid) as relationid,
           |       cast(coalesce(a.isexcellent,b.isexcellent) as int) as isexcellent,
           |       coalesce(a.excellent_date,b.excellent_date) as excellent_date,
           |       coalesce(a.timestamp,b.timestamp) as timestamp,
           |       cast(case a.operationState when null then b.is_del when 'delete' then '1' else '0' end as int) is_del,
           |       date_format(coalesce(a.bbs_publish_time, b.bbs_publish_time),"yyyyMMdd") as publish_date,
           |       coalesce(a.update_time,b.update_time) as update_time,
           |       coalesce(b.niuren_level,a.niuren_level) as niuren_level,
           |       cast(nvl(date_format(coalesce(a.bbs_publish_time, b.bbs_publish_time),"yyyy"),'1999') as int) as publish_year
           | from
           | (select c.*,nvl(d.niuren_level,0) as niuren_level from bbs_attrs_level_today as c left join t_user as d on c.uid=d.uid) a
           | full join ${dwdTable} b on b.bbs_tid=a.bbs_tid
           |""".stripMargin)
//        .show(false)
      val exec = new MySqlBaseDao()
      exec.executeInsertOrUpdate(s"update t_check set flag=1 , message='$day:运行成功' where name='${t_check_pre}t_dwd_bbs_post'",null)
    } catch {
      case e: Exception => {
        e.printStackTrace()
        try{
          val exec = new dao.MySqlBaseDao()

          exec.executeInsertOrUpdate(s"update t_check set flag=2, message='$day:${t_check_pre}t_dwd_bbs_post 运行失败' where name='${t_check_pre}t_dwd_bbs_post'",null)
        }catch {
          case e:Throwable=>{
            e.printStackTrace()
          }
        }
      }
    }
  }

  /**
   * 从业务数据清洗帖子数据到dwS层,关联吨位数据和作者是否是牛人信息
   * 语雀文档地址：https://360che.yuque.com/technology_team/rof05c/197623986
   */
  def pullPostToDWS(spark:SparkSession,day:String): Unit = {
    val t_check_pre="aliyun_"
    val dwdTable="t_dwd_bbs_post"
    val dwsTable="t_dws_bbs_post"
    val sqlBase=new MySqlBaseDao()
    try{
      //获取车系关联品牌和吨位数据
      sqlBase.getCacheTable("t_series_info",
        """
          |(
          |select a.seriesId,a.brandId,b.tonnageType from
          |(select seriesId,brandId from db_userportrait.t_product where enable=1 GROUP BY seriesId,brandId) as a
          |inner join db_userportrait.t_series_tonnage as b on a.seriesId=b.seriesId
          |)t
          |""".stripMargin,spark)
      val resultDF=spark.sql(
        s"""
           |select a.uid, a.nickname, bbs_tid, bbs_pid, subject, message, bbs_publish_time,
           |       client, bbs_type, coverurl, videourl, imgurl, `position`, bbs_domain,
           |       bbs_subcate, bbs_series, coalesce(a.bbs_brand,b.brandId,"") as bbs_brand, bbs_first_label, bbs_second_label,
           |       bbs_third_label, bbs_scenario, is_dealer, relationid, isexcellent,
           |       excellent_date, `timestamp`, is_del, publish_date, update_time, publish_year,
           |       coalesce(b.tonnageType,"") as `bbs_tonnage`,
           |       a.niuren_level
           |from $dwdTable as a
           |left join t_series_info as b on a.bbs_series=b.seriesId
           |""".stripMargin)
      spark.sql(s"truncate table $dwsTable")
      resultDF.repartition(1).write.format("hive").partitionBy("publish_year").mode("append")
        .saveAsTable(dwsTable)
      val exec = new MySqlBaseDao()
      exec.executeInsertOrUpdate(s"update t_check set flag=1 , message='$day:运行成功' where name='${t_check_pre}t_dws_bbs_post'",null)
    }catch {
      case e: Exception => {
        e.printStackTrace()
        try{
          val exec = new dao.MySqlBaseDao()

          exec.executeInsertOrUpdate(s"update t_check set flag=2, message='$day:${t_check_pre}t_dws_bbs_post 运行失败' where name='${t_check_pre}t_dws_bbs_post'",null)
        }catch {
          case e:Throwable=>{
            e.printStackTrace()
          }
        }
      }
    }
  }

  /**
   * 业务数据同步，圈子数据
   * @param spark SparkSession
   * @param sqlBase mysql数据库操作实例
   * @param day 计算日期
   */
  def getBBSCircle(spark:SparkSession,sqlBase:MySqlBaseDao,day:String): Unit ={
    try{
      val resultMysqlTable="db_userportrait.t_bbs_circle"
      val resultDF=spark.sql(
        s"""
           |select c0 as id,c1 as name,c2 as display,c3 as del,c4 as series_id,c5 as subcate_id,c6 as `type`,c7 as create_time,`timestamp` as update_timestamp,ROW_NUMBER() OVER(partition by c0 order by `timestamp` desc) as rank from
           |(select json_tuple(data, 'id', 'name', 'display','del', 'seriesId','subcateId', 'type', 'createTime'),`timestamp`
           |from t_biz_data_temp where billType='bbs_circle') as t
           |""".stripMargin).filter("rank=1").drop("rank")
      //数据只有insert直接保存
      val args=resultDF.collect().map(row=>List(row.getAs[Int]("id"),row.getAs[String]("name"),row.getAs[Int]("display"),row.getAs[Int]("del"),row.getAs[String]("series_id"),row.getAs[String]("subcate_id"),row.getAs[String]("type"),row.getAs[String]("create_time"),row.getAs[String]("update_timestamp"))).toList
      sqlBase.executeMany(
        s"""
           |REPLACE INTO $resultMysqlTable (id, name, display, del, series_id, subcate_id, `type`, create_time,update_timestamp) VALUES(?,?,?,?,?,?,?,?,?)
           |""".stripMargin,args)
      sqlBase.execute(s"update t_check set flag=1, message='$day:t_bbs_circle 运行成功' where name='aliyun_t_bbs_circle'")
    }catch {
      case e: Exception => {
        e.printStackTrace()
        try{
          val exec = new MySqlBaseDao()
          exec.execute(s"update t_check set flag=2, message='$day:t_bbs_circle 运行失败' where name='aliyun_t_bbs_circle'")
        }catch {
          case e:Throwable=>{
            e.printStackTrace()
          }
        }
      }
    }
  }

  /**
   * 读取csv数据关联历史relationid字段
   */
  def relationCsv2Hive(): Unit = {
    val ss = MySparkSession.conn()
    val relationDF = ss.read.format("csv")
      .option("header", "true")
      .load("file:////D:\\download\\bbs_post_20210928.csv").createOrReplaceTempView("relation_view")
        ss.sql("set hive.exec.dynamic.partition=true")
        ss.sql("set hive.exec.dynamic.partition.mode=nonstrick;")
    //    ss.sql(s"truncate table t_dwd_bbs_post")
        ss.sql(
          """
            |insert overwrite table t_dwd_bbs_post partition(publish_year)
            |SELECT uid, nickname, bbs_tid, bbs_pid, subject, message, bbs_publish_time,
            |client, bbs_type, coverurl, videourl, imgurl, `position`, bbs_domain,
            |bbs_subcate, bbs_series, bbs_brand, bbs_first_label, bbs_second_label,
            |bbs_third_label, bbs_scenario, is_dealer, b.relationId as relationid, isexcellent,
            |excellent_date, `timestamp`, is_del, publish_date, update_time, publish_year
            |FROM default.t_dwd_bbs_post as a left join relation_view as b on a.bbs_tid=b.tid
            |""".stripMargin)

  }


  /**
   * 读取csv数据关联导入到dwd层(历史数据)
   */
  def historyCsv2Hive(): Unit = {
    val ss = MySparkSession.conn()
//    01关联用户信息t_user 的牛人信息写入hive中,运行完可以注释
    val historyDF = ss.read.format("csv")
      .option("header", "true")
      .load("file:////D:\\download\\bbs_history\\*.csv")
      .createOrReplaceTempView("history_view")
    ss.sql(
      """
        |truncate table db_test.t_dwd_bbs_post_history
        |""".stripMargin)
    ss.sql(
      """
        |select a.uid,bbs_tid,subject,(from_unixtime(bbs_publish_time,'yyyy-MM-dd HH:mm:ss')) as bbs_publish_time,bbs_type,is_dealer,isexcellent,is_del,coalesce(b.niuren_level,0) as `niuren_level`,(from_unixtime(bbs_publish_time,'yyyy')) as publish_year
        |from history_view as a left join t_user as b on a.uid=b.uid
        |""".stripMargin).repartition(1).write.format("hive").partitionBy("publish_year").mode("append").saveAsTable("db_test.t_dwd_bbs_post_history")
//    02数据整合到db_test.t_dwd_bbs_post
    ss.sql("set hive.exec.dynamic.partition=true")
    ss.sql("set hive.exec.dynamic.partition.mode=nonstrick")
    ss.sql(
      s"""
        |insert overwrite table db_test.t_dwd_bbs_post partition(publish_year)
        |(SELECT a.uid, "" as nickname, a.bbs_tid, 0 as bbs_pid, a.subject, "" as message,
        |a.bbs_publish_time, "" as client, a.bbs_type,"" as coverurl, "" as videourl, "" as imgurl,
        | "" as `position`,"" as bbs_domain, "" as bbs_subcate, "" as bbs_series,"" as bbs_brand,
        | "" as bbs_first_label, "" as bbs_second_label,"" as bbs_third_label,"" as bbs_scenario,
        | a.is_dealer, "" as relationid, a.isexcellent, "" as excellent_date, "" as `timestamp`,
        | a.is_del, DATE_FORMAT(a.bbs_publish_time,"yyyyMMdd")  as publish_date, "" as update_time,a.niuren_level, a.publish_year
        |FROM (select * from db_test.t_dwd_bbs_post_history where  publish_year >1970 and uid rlike "^[0-9]+$$" and bbs_tid rlike "^[0-9]+$$" and uid is not null and bbs_tid is not null ) as a left join db_test.t_dwd_bbs_post as b on a.bbs_tid=b.bbs_tid having b.bbs_tid is null
        |union
        |SELECT uid, nickname, bbs_tid, bbs_pid, subject, message, bbs_publish_time, client, bbs_type, coverurl, videourl, imgurl, `position`, bbs_domain, bbs_subcate, bbs_series, bbs_brand, bbs_first_label, bbs_second_label, bbs_third_label, bbs_scenario, is_dealer, relationid, isexcellent, excellent_date, `timestamp`, is_del, publish_date, update_time,niuren_level, publish_year
        |FROM db_test.t_dwd_bbs_post)
        |""".stripMargin)
    /*03更新牛人信息*/
//    //先用 t_user更新
    ss.sql(
      """
        |insert overwrite table db_test.t_dwd_bbs_post
        |SELECT  a.uid, a.nickname, bbs_tid, bbs_pid, subject, message, bbs_publish_time, client, bbs_type, coverurl, videourl, imgurl, `position`, bbs_domain, bbs_subcate, bbs_series, bbs_brand, bbs_first_label, bbs_second_label, bbs_third_label, bbs_scenario, is_dealer, relationid, isexcellent, excellent_date, `timestamp`, is_del, publish_date, update_time,nvl(b.niuren_level,0) as niuren_level, publish_year
        |FROM db_test.t_dwd_bbs_post as a left join t_user as b on a.uid=b.uid
        |""".stripMargin)
//    //用t_bbs_post更新
    ss.sql(
      """
        |insert overwrite table db_test.t_dwd_bbs_post
        |SELECT  a.uid, nickname, a.bbs_tid, bbs_pid, subject, message, bbs_publish_time, client, bbs_type, coverurl, videourl, imgurl, `position`, bbs_domain, bbs_subcate, bbs_series, bbs_brand, bbs_first_label, bbs_second_label, bbs_third_label, bbs_scenario, is_dealer, relationid, isexcellent, excellent_date, `timestamp`, is_del, publish_date, update_time,nvl(b.niuren_level,a.niuren_level) as niuren_level, publish_year
        |FROM db_test.t_dwd_bbs_post as a left join (select distinct bbs_tid,uid,niuren_level from t_bbs_post where bbs_tid is not null ) as b on a.bbs_tid=b.bbs_tid
        |""".stripMargin)
  }

  }
