# 360che.hadoop.logclean

## 项目简介

360che.hadoop.logclean是一个大数据日志清洗与处理系统，主要用于处理和分析360che平台的各种日志数据，包括Web日志、App日志、神策平台数据等。该项目基于Hadoop生态系统开发，使用Spark和Scala作为主要的处理框架和编程语言。

## 功能特点

- **多源日志处理**：支持处理各种来源的日志数据，包括Web日志、App日志、神策平台数据、小程序日志等
- **自动化数据清洗**：对原始日志数据进行清洗、转换和规范化处理
- **数据压缩存储**：支持将历史日志数据进行压缩存储，节省存储空间
- **业务数据整合**：从各种业务接口拉取数据，支持用户映射、线索数据处理等功能
- **批处理与流处理**：同时支持批处理和流处理两种模式，满足不同的数据处理需求

## 项目结构

```
360che.hadoop.logclean/
├── src/                                # 源代码目录
│   └── main/
│       ├── java/                       # Java源代码
│       │   ├── HadoopCompress.java     # Hadoop日志压缩工具
│       │   └── LogUtil.java            # 日志工具类
│       ├── scala/                      # Scala源代码
│       │   └── com/che/hadoop/logclean/
│       │       ├── activity/           # 主要业务逻辑
│       │       │   ├── LogCleanAppWebJob.scala        # Web和App日志清洗作业
│       │       │   ├── LogCleanInformationJob.scala   # 信息日志清洗作业
│       │       │   ├── PullBusinessDataJob.scala      # 业务数据拉取作业
│       │       │   └── ...
│       │       ├── bo/                 # 业务对象
│       │       ├── dao/                # 数据访问对象
│       │       └── utils/              # 工具类
│       └── resources/                  # 资源文件
│           ├── connection.properties   # 数据库和API连接配置
│           ├── compress.properties     # 压缩配置
│           ├── core-site.xml           # Hadoop核心配置
│           ├── hdfs-site.xml           # HDFS配置
│           ├── hive-site.xml           # Hive配置
│           └── ...
├── pom.xml                             # Maven项目配置
└── package.xml                         # 打包配置
```

## 主要功能模块

1. **日志清洗处理**
   - Web日志清洗 (LogCleanWeb)
   - App日志清洗 (LogCleanApp)
   - 神策平台日志清洗 (LogCleanSCWeb)
   - 小程序日志清洗 (LogCleanMiniProgram)

2. **数据压缩存储**
   - 使用GZip格式进行日志文件压缩 (HadoopCompress)
   - 支持批量压缩指定日期范围内的日志文件

3. **业务数据拉取**
   - 从各种业务API接口拉取数据 (PullBusinessDataJob, PullBusinessInterfaceDataJob)
   - 支持经销商数据、用户注册信息、文章信息等多种数据类型

4. **用户映射处理**
   - 用户ID映射 (UserMapJob, DmIdMapJob)
   - 支持批处理和流处理两种模式

## 技术栈

- **开发语言**：Scala, Java
- **大数据框架**：Hadoop, Spark, Hive
- **数据存储**：HDFS, MySQL, Kudu
- **构建工具**：Maven
- **日志处理**：Flume
- **分词处理**：结巴分词

## 关键文件和方法

### 日志清洗处理

**文件位置**: `src/main/scala/com/che/hadoop/logclean/activity/LogCleanAppWebJob.scala`

**主要方法**:
- `main(args: Array[String])`: 程序入口，支持传入数据库环境和日期参数
  - 参数1: 数据库环境（如"default."）
  - 参数2: 开始日期（格式：yyyyMMdd）
  - 参数3: 结束日期（格式：yyyyMMdd）

**调用的关键方法**:
- `LogCleanSCWeb.webscLogClean(spark, day, dbEnv)`: 神策平台web数据清洗
- `LogCleanApp.cleanAppScData(spark, day, dbEnv)`: 神策app日志离线清洗
- `LogCleanApp.dmIdSupplement(day, dbEnv)`: 维护未关联dm_id的数据
- `LogCleanMiniProgram.cleanMiniProgramLog(spark, day, dbEnv)`: 神策小程序数据清洗
- `LogCleanWeb.saveLogCleanToHive(spark, day, dbEnv)`: hadoop web日志离线清洗
- `LogCleanApp.cleanAppData(spark, day, dbEnv)`: hadoop app日志离线清洗

### 数据压缩工具

**文件位置**: `src/main/java/HadoopCompress.java`

**主要方法**:
- `main(args: Array[String])`: 程序入口，从配置文件读取参数并执行压缩
- `fileCompress(String uri, String outputPath)`: 执行压缩操作
  - 参数1: 原始文件路径
  - 参数2: 文件压缩后的保存路径
- `fileDecompress(String uri, String outputPath)`: 执行解压操作
  - 参数1: 压缩文件路径
  - 参数2: 文件解压后的保存路径
- `getTimesBetweenDates(String startTime, String endTime)`: 获取指定日期范围内的所有日期
  - 参数1: 起始日期（格式：yyyyMMdd）
  - 参数2: 结束日期（格式：yyyyMMdd）

### 业务数据拉取

**文件位置**: `src/main/scala/com/che/hadoop/logclean/activity/PullBusinessDataJob.scala`

**主要方法**:
- `main(args: Array[String])`: 程序入口，支持传入数据库环境和日期参数
  - 参数1: 数据库环境（如"default."）
  - 参数2: 开始日期（格式：yyyyMMdd）
  - 参数3: 结束日期（格式：yyyyMMdd）

**调用的关键方法**:
- `FetchThbalogData.cleanThbaLog(spark, day)`: 清洗thba日志数据
- `PullDiscountOil.saveData(spark, day)`: 优惠加油数据采集
- `PullBBSPost.pullPostToODS(spark, day)`: 帖子数据拉取到ODS层
- `PullBBSPost.pullPostToDWD(spark, day)`: 帖子数据处理到DWD层
- `PullKyqData.pullKyq(spark, day)`: 卡友圈数据拉取
- `PullUserInfo.start(spark, sqlBase, day)`: 用户数据采集
- `pullDealerProduct.pullDealerProduct(spark, day)`: 拉取经销商车型信息

### 用户映射处理

**文件位置**: `src/main/scala/com/che/hadoop/logclean/activity/UserMapJob.scala`

**主要方法**:
- `main(args: Array[String])`: 程序入口，处理用户ID映射
  - 参数1: 数据库环境（如"default."）
  - 参数2: 开始日期（格式：yyyyMMdd）
  - 参数3: 结束日期（格式：yyyyMMdd）

## 配置文件

### 日志压缩配置 (compress.properties)

**文件位置**: `src/main/resources/compress.properties`

**主要配置**:
- `date.start`: 压缩开始日期（格式：yyyyMMdd）
- `date.end`: 压缩结束日期（格式：yyyyMMdd）
- `path.source`: 源文件路径
- `path.destination`: 目标文件路径

### 数据库和API连接配置 (connection.properties)

**文件位置**: `src/main/resources/connection.properties`

**主要配置**:
- 数据库连接配置（URL、用户名、密码等）
- API接口地址配置

## 打包和部署

### 1. 打包配置

项目使用Maven进行打包，主要配置文件有：

- **pom.xml**: Maven 项目配置文件，定义项目依赖和构建流程
- **package.xml**: 使用 Maven Assembly 插件的自定义打包配置，定义包含哪些依赖和资源

### 2. Maven本地仓库管理

Maven本地仓库是所有项目依赖的缓存位置，了解如何管理它对于解决打包问题很重要。

#### 查看Maven本地仓库位置

Maven本地仓库默认位置为：`~/.m2/repository`（Unix/Mac）或 `C:\Users\<USER>\.m2\repository`（Windows）。

有以下几种方式可以查看本地仓库位置和内容：

1. **通过Maven命令查看配置**
   ```bash
   mvn help:effective-settings
   ```
   在输出中查找 `<localRepository>` 标签，它显示了本地仓库的实际位置。

2. **查看配置文件**
   Maven的本地仓库路径在 `~/.m2/settings.xml` 文件中配置。如果该文件不存在，则使用默认位置。

3. **查看磁盘使用情况**
   ```bash
   # Unix/Linux/Mac
   du -sh ~/.m2/repository/
   
   # Windows (PowerShell)
   Get-ChildItem -Path "$env:USERPROFILE\.m2\repository" -Recurse | Measure-Object -Property Length -Sum
   ```

#### 管理Maven本地仓库

1. **清理本地仓库**
   如果本地仓库占用空间过大，可以清理：
   ```bash
   # 清理过期的快照
   mvn dependency:purge-local-repository
   
   # 强制更新所有依赖
   mvn clean install -U
   ```

2. **修改本地仓库位置**
   在 `~/.m2/settings.xml` 文件中添加：
   ```xml
   <settings>
     <localRepository>/path/to/new/repository</localRepository>
   </settings>
   ```

3. **解决依赖冲突**
   如果打包过程中遇到依赖冲突，可以尝试：
   ```bash
   # 显示依赖树
   mvn dependency:tree
   
   # 分析依赖
   mvn dependency:analyze
   ```

### 3. 打包步骤

1. **确保环境准备**
   - 安装 JDK 8 或更高版本
   - 安装 Maven 3.6 或更高版本
   - 确保网络能够访问 Maven 中央仓库

2. **进入项目根目录**
   ```bash
   cd 360che.hadoop.logclean
   ```

3. **执行 Maven 打包命令**
   ```bash
   mvn clean package
   ```
   
   这将执行以下步骤：
   - 清理之前的构建产物（`clean`）
   - 编译 Java 和 Scala 源码
   - 运行单元测试（如果有）
   - 打包成 JAR 文件（`package`）
   - 根据 `package.xml` 配置，使用 Assembly 插件创建一个包含所有依赖的 JAR 包

4. **查看打包结果**
   
   打包完成后，在 `target` 目录下会生成以下文件：
   - `360che.hadoop.logclean-1.0.jar`: 不包含依赖的 JAR 包
   - `360che.hadoop.logclean-1.0-jar-with-dependencies.jar`: 包含所有依赖的完整 JAR 包（用于部署）

### 4. 自定义打包选项

如需自定义打包过程，可以使用以下 Maven 参数：

- **跳过测试**：
  ```bash
  mvn clean package -DskipTests
  ```

- **指定 Java 版本**：
  ```bash
  mvn clean package -Dmaven.compiler.source=1.8 -Dmaven.compiler.target=1.8
  ```

- **使用特定的 Maven profile**：
  ```bash
  mvn clean package -P prod
  ```

### 5. 部署运行

1. **将打包后的 JAR 文件上传到 Hadoop 集群**
   ```bash
   scp target/360che.hadoop.logclean-1.0-jar-with-dependencies.jar user@hadoop-master:/path/to/deployment/
   ```

2. **使用 spark-submit 提交作业**
   ```bash
   # 日志清洗作业
   spark-submit --class com.che.hadoop.logclean.activity.LogCleanAppWebJob \
     --master yarn \
     --deploy-mode cluster \
     --driver-memory 4g \
     --executor-memory 2g \
     --executor-cores 2 \
     --num-executors 4 \
     360che.hadoop.logclean-1.0-jar-with-dependencies.jar [dbEnv] [startDay] [endDay]
   
   # 业务数据拉取作业
   spark-submit --class com.che.hadoop.logclean.activity.PullBusinessDataJob \
     --master yarn \
     --deploy-mode cluster \
     --driver-memory 4g \
     --executor-memory 2g \
     --executor-cores 2 \
     --num-executors 4 \
     360che.hadoop.logclean-1.0-jar-with-dependencies.jar [dbEnv] [startDay] [endDay]
   
   # 用户映射作业
   spark-submit --class com.che.hadoop.logclean.activity.UserMapJob \
     --master yarn \
     --deploy-mode cluster \
     --driver-memory 4g \
     --executor-memory 2g \
     --executor-cores 2 \
     --num-executors 4 \
     360che.hadoop.logclean-1.0-jar-with-dependencies.jar [dbEnv] [startDay] [endDay]
   
   # 日志压缩（使用Java命令）
   java -cp 360che.hadoop.logclean-1.0-jar-with-dependencies.jar HadoopCompress
   ```

## 参数说明

大多数作业支持以下参数：
- 数据库环境（默认为"default."）
- 开始日期（格式：yyyyMMdd）
- 结束日期（格式：yyyyMMdd）

## 版本历史

- **v1.0.0** (2019-02-17): 初始版本，实现用户偏好相关功能
- **v1.0.1** (2019-08-29): 修复几处由于数据变化导致的bug
- **v1.0.2** (2020-03-26): 新增网店数据清洗和用户注册数据整理功能
- **v2.0.0** (2020-08-21): 实现DM ID映射功能
- **v2.1.0** (2021-04-09): 新增小程序数据清洗功能

## 注意事项

1. 运行前请确保已正确配置所有必要的属性文件
2. 数据库连接配置中包含敏感信息，请妥善保管
3. 大数据处理任务可能需要较长时间，建议设置合理的执行时间窗口
4. 日志压缩功能会消耗大量系统资源，请在系统负载较低时执行
5. 打包过程中如遇依赖下载问题，可以尝试配置国内Maven镜像或使用代理
6. Maven本地仓库可能会随着时间增长占用大量磁盘空间，定期清理可以释放空间 