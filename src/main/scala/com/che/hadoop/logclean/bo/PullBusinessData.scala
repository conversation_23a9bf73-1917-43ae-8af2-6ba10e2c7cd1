package com.che.hadoop.logclean.bo

import java.nio.file.Paths
import java.util.ResourceBundle
import java.net.URLEncoder
import com.alibaba.fastjson.{JSON, JSONException}
import com.che.hadoop.underlay.tool.http.MyHttpService
import com.che.hadoop.logclean.utils.{UrlDecodeUtil, function_}
import com.huaban.analysis.jieba.JiebaSegmenter.SegMode
import com.huaban.analysis.jieba.{JiebaSegmenter, SegToken, WordDictionary}
import org.apache.spark.sql.{Row, SaveMode, SparkSession}
import org.apache.spark.sql.types.{IntegerType, StringType, StructField, StructType}
import com.che.hadoop.underlay.dao.{MyKudu, MySparkSession, MySqlBaseDao}
import com.che.hadoop.underlay.tool.date.DateTranslate
import org.apache.spark.sql.functions.{col, size}

import scala.collection.mutable
import scala.collection.mutable.Set

/**
 * 拉取司机招聘数据，车型认证,帖子加精数据
 * gaojie
 */
object PullBusinessData {

  case class AtricleDWS(article_id:Int,title:String, mtitle:String,publish_time:String, create_time:String, first_classify_id:Int, first_classify_name:String, second_classify_id:Int, second_classify_name:String, content:String, cate_id:String, sub_cate_id:String, brand_id:String, series_id:String, product_id:String, label_id:String, label_type_id:String, source_type:String, province:String, city:String, first_label:String, second_label:String, third_label:String, is_del:Int, publish_date:String, relation_id:String, tonnage_type:String,is_dealer:Int,is_dealer_push:Int,scenario:String,census_typeone_id:Int,census_typeone_name:String,census_typetwo_id:Int,census_typetwo_name:String,user_type_id:Int,user_type_name:String, mtitle_seg:Array[String],authorid:Int,talkids:String,program_id:String,program_name:String)
  case class product400(id:Int,tel:String,tape_url:String,brand_id:Int,create_time:String,day:String)
  case class WechatRecord(room_id:String,msg_content:String)
  case class WechatRecordTest(room_id:String,msg_content:String,day:String)
  val reader = ResourceBundle.getBundle("connection_underlay")
  private val segmenter = new JiebaSegmenter()
  //获取编译后生成的${项目名}/target/classes/路径
  //  val resource: URL = this.getClass.getClassLoader.getResource("jieba_dict.txt")
  //  private val iter: Iterator[Char] = Source.fromURL(resource).iter
  //  println("resource => " + resource.toURI)
  private val path = Paths.get("jieba_dict.txt")
  WordDictionary.getInstance().loadUserDict(path)

  def main(args: Array[String]): Unit = {
    val spark = MySparkSession.conn()
    val day="20240101"
    //    val endDate = DateTranslate.getAddDate(1, day)
    val sqlBase=new MySqlBaseDao()
    //    println(day, endDate)
    //    pullContentIteract(spark,sqlBase, day)
//    test2(spark)
//    saveArticleToDWS(spark, "20240529")
    test2(spark,sqlBase,day)
    /*资讯数据-通过接口拉取*/
    //    PullBusinessData.pullArticleToDWD(spark, day, endDate)
    //    PullBusinessData.pullVideoToDWD(spark, day, endDate)
    //    PullBusinessData.saveArticleToDWS(spark, day)
    //    pullUserFocus(spark,sqlBase,"20231129")
    //    DateTranslate.getDatesBetween("20231017","20231017").foreach(day=>{
    //      test()
    //    })
  }


  /**
   * 从埋点中获取业务数据并保存到hive
   * @param day 日期
   */
  def saveDriverData(spark:SparkSession,day: String): Unit = {
    /*司机招聘孵化失败,相关数据停止清洗*/
    //    val driverInfoTable = "t_driver_info" //司机招聘求职数据表
    //    val recruitTable = "t_recruit_identify" //司机招聘 招聘人认证数据表
    //    val publishTable = "t_recruit_publish" //司机招聘 发布职位数据表
    //    val applicationTable = "t_job_application" //司机招聘 求职数据表
    val carTable = "t_car_certification" //违章查询 车型认证数据表
    val queryTable = "t_illegal_query" //违章查询数据
    val t_check_pre="aliyun_"
    try {
      //      //获取司机招聘求职数据
      //      val driverData = spark.sql("select json_tuple(data,'id', 'userId', 'userPhone', 'userName', 'userSex', 'userAge', " +
      //        "'driveType', 'driveAge', 'createTime'),`timestamp`, operation_state,from_unixtime(substr(`timestamp`,0,10), 'yyyyMMdd') from t_biz_data_temp where billtype='driver'")
      //        .toDF("id", "userId", "userPhone", "userName", "userSex", "userAge", "driveType", "driveAge", "createTime", "timestamp", "operationState", "day")
      //      driverData.write.mode("append").partitionBy("day").format("Hive").saveAsTable(driverInfoTable)
      //
      //      //司机招聘 招聘人认证数据
      //      val recruitHistoryData = spark.sql(s"select id, userId, userPhone, userName, userCardNumber, type, createTime, " +
      //        s"timestamp,operationState, day from $recruitTable where day!='$day'")
      //      val recruitData = spark.sql("select json_tuple(data,'id', 'user_id', 'userPhone', 'userName', 'userCardNumber', 'type', " +
      //        " 'createTime'),`timestamp`, operation_state,from_unixtime(substr(`timestamp`,0,10), 'yyyyMMdd') from t_biz_data_temp where billtype='recruit_identify'")
      //        .toDF("id", "userId", "userPhone", "userName", "userCardNumber", "type", "createTime", "timestamp",
      //          "operationState", "day").union(recruitHistoryData).coalesce(1)
      //        .createOrReplaceTempView("t_recruit_publish_tmp")
      //      spark.sql(
      //        s"""
      //           | insert overwrite table $recruitTable select * from t_recruit_publish_tmp
      //           |""".stripMargin)
      //
      //
      //      //司机招聘 发布职位数据
      //      val publishData = spark.sql("select json_tuple(data,'id', 'userId', 'userPhone','userName', " +
      //        "'province_id', 'province_name', 'city_id','city_name','driver_license','start_money','end_money','type','status'," +
      //        " 'createTime'),`timestamp`, operation_state,from_unixtime(substr(`timestamp`,0,10), 'yyyyMMdd') from t_biz_data_temp where billtype='recruit_publish'")
      //        .toDF("id", "user_id", "user_phone", "user_name", "province_id", "province_name", "city_id", "city_name",
      //          "driver_license", "start_money", "end_money", "type", "status", "create_time", "timestamp", "operation_state", "day")
      //      publishData.write.mode("append").partitionBy("day").format("Hive").saveAsTable(publishTable)
      //
      //      //司机招聘 职位申请数据
      //      val applicationData = spark.sql("select json_tuple(data,'id', 'userId', 'jobId','resumeId', 'status', " +
      //        " 'createTime'),`timestamp`, operation_state,from_unixtime(substr(`timestamp`,0,10), 'yyyyMMdd') from t_biz_data_temp where billtype='job_application'")
      //        .toDF("id", "user_id", "job_id", "resume_id", "status", "create_time", "timestamp", "operation_state", "day")
      //      applicationData.write.mode("append").partitionBy("day").format("Hive").saveAsTable(applicationTable)

      //违章查询
      val queryData = spark.sql("select json_tuple(data,'id', 'userId', 'truckId','truckNumber', 'statusCode', " +
        " 'createTime'),`timestamp`, operation_state,from_unixtime(substr(`timestamp`,0,10), 'yyyyMMdd') from t_biz_data_temp where billtype='illegal_query'")
        .toDF("id", "user_id", "truck_id", "truck_number", "status_code", "create_time", "timestamp", "operation_state", "day")
      spark.sql(s"alter table $queryTable drop if exists partition(day='$day')")
      queryData.write.mode("append").partitionBy("day").format("Hive").saveAsTable(queryTable)

      //违章查询 车型认证数据
      val carHistoryData = spark.sql(s"select id, userId, carType, truckName, truckId, truckNumber, status, " +
        s"brandId,brandName,seriesId,seriesName,subCategoryId,createTime,timestamp, operationState, day from $carTable where day!='$day'")
      spark.sql("select json_tuple(data, 'id', 'userId', 'carType', 'truckName', 'truckId', " +
        "'truckNumber','status', 'brandId','brandName','seriesId','seriesName','subCategoryId','createTime'),`timestamp`, operation_state,from_unixtime(substr(`timestamp`,0,10), 'yyyyMMdd') from t_biz_data_temp where billtype='car_certification'")
        .toDF("id", "userId", "carType", "truckName", "truckId", "truckNumber", "status", "brandId",
          "brandName", "seriesId", "seriesName", "subCategoryId", "createTime", "timestamp", "operationState", "day").union(carHistoryData)
        .coalesce(1).createOrReplaceTempView("t_car_certification_tmp")
      spark.sql(
        s"""
           | insert overwrite table $carTable select * from t_car_certification_tmp
           |""".stripMargin)
      val exec = new MySqlBaseDao()
      exec.executeInsertOrUpdate(s"update t_check set flag=1 , message='$day:运行成功' where name='${t_check_pre}t_car_certification'",null)
    } catch {
      case e: Exception => {
        e.printStackTrace()
        try{
          val exec = new MySqlBaseDao()

          exec.executeInsertOrUpdate(s"update t_check set flag=2, message='$day:t_car_certification 运行失败' where name='${t_check_pre}t_car_certification'",null)
        }catch {
          case e:Throwable=>{
            e.printStackTrace()
          }
        }

      }

    }


  }

  /**
   * 清洗资讯数据到ods层
   * 已更改为通过接口方式拉取数据到t_dwd_article，废弃
   * @param day
   */
  def saveInfoData(day: String): Unit = {
    val spark = MySparkSession.conn("Logclean_PullBusinessData")
    //文章数据字典
    val articleStruct = StructType(Array(
      StructField("article_id", StringType, true),
      StructField("title", StringType, true), StructField("mtitle", StringType, true),
      StructField("publish_time", StringType, true), StructField("create_time", StringType, true),
      StructField("first_classify_id", StringType, true), StructField("first_classify_name", StringType, true),
      StructField("second_classify_id", StringType, true), StructField("second_classify_name", StringType, true),
      StructField("content", StringType, true),
      StructField("cate_id", StringType, true), StructField("sub_cate_id", StringType, true),
      StructField("brand_id", StringType, true), StructField("series_id", StringType, true), StructField("product_id", StringType, true),
      StructField("label_id", StringType, true), StructField("label_type_id", StringType, true), StructField("source_type", StringType, true),
      StructField("province", StringType, true), StructField("city", StringType, true),
      StructField("first_label", StringType, true),
      StructField("second_label", StringType, true),
      StructField("third_label", StringType, true),
      StructField("operation_state", StringType, true),
      StructField("timestamp", StringType, true),
      StructField("relation_id", StringType, true),
      StructField("tonnage_type", StringType, true),
      StructField("is_dealer", StringType, true),
      StructField("day", StringType, true)
    ))
    //视频数据字典
    val videoStruct = StructType(Array(
      StructField("id", StringType, true),
      StructField("title", StringType, true),
      StructField("url", StringType, true),
      StructField("publish_time", StringType, true),
      StructField("classify_id", StringType, true), StructField("classify_name", StringType, true),
      StructField("video_type", StringType, true), StructField("program_id", StringType, true),
      StructField("program_name", StringType, true),
      StructField("cate_id", StringType, true), StructField("sub_cate_id", StringType, true),
      StructField("brand_id", StringType, true), StructField("series_id", StringType, true), StructField("product_id", StringType, true),
      StructField("label_id", StringType, true), StructField("label_type_id", StringType, true),
      StructField("first_label", StringType, true),
      StructField("second_label", StringType, true),
      StructField("third_label", StringType, true),
      StructField("operation_state", StringType, true),
      StructField("timestamp", StringType, true),
      StructField("relation_id", StringType, true),
      StructField("is_dealer", StringType, true),
      StructField("day", StringType, true)
    ))
    val data = spark.read.json("oss://360che-bigdata.cn-beijing.oss-dls.aliyuncs.com/pv-nginx-app/" + day)
      .filter("status=200 and http_host='thba.360che.com'")
    val dao = new MySqlBaseDao()
    val seriesTonnageM = mutable.Map[String, String]()
    val rs = dao.executeQuery("select seriesId, tonnageType from db_userportrait.t_series_tonnage", null)
    while (rs.next()) {
      seriesTonnageM += (rs.getInt("seriesId").toString -> rs.getInt("tonnageType").toString)
    }
    //清洗文章数据
    try {
      val resultRDD = data.rdd.map(line => {

        var resultRow = (0, Row())
        try {
          val requestBody = UrlDecodeUtil.urlDecode(line.getAs[String]("request_body"))
          val params = JSON.parseObject(requestBody)
          if (params.getString("billType").contains("article_t_")) {
            val row = params.getJSONObject("data")
            val id = row.getString("id")
            val title = row.getString("title")
            val publishTime = row.getString("publishTime")
            val relationId = row.getString("relationId")
            //      val relationType = row.getString](11)
            val firstLabel = row.getString("firstLabel")
            val secondLabel = row.getString("secondLabel")
            val thirdLabel = row.getString("thirdLabel")
            val operation = params.getString("operationState")
            //获取文章关联产品信息 埋点格式：1#66#17#6976#81218 大类#子类#品牌#车系#车型，多个产品用‘|’分隔
            val relations = Array(Set[String](), Set[String](), Set[String](), Set[String](), Set[String](),Set[String]())
            var i = 0
            for (item <- relationId.split("\\|")) {
              for (item1 <- item.split("#")) {
                relations(i).add(item1)
                if (i == 3 && seriesTonnageM.contains(item1)) {
                  relations(5).add(seriesTonnageM(item1))
                }
                i += 1
              }
              i = 0
            }
            var isDealer = ""
            if(row.containsKey("isDealer")){
              isDealer=row.getString("isDealer")
            }
            if (params.getString("billType") == "article_t_article") { //清洗文章数据
              //获取文章标签及标签类型
              val label = row.getString("lable")
              //文章标签埋点格式：[{"lableId":44,"TypeId":6},{"lableId":49,"TypeId":6},{"lableId":54,"TypeId":6}]
              var label_id = mutable.Set[String]()
              val label_type_id = Set[String]()
              if (label != null && label.length != 0) {
                val labelJsonArr = JSON.parseArray(label)
                for (item <- 0 until labelJsonArr.size()) {
                  val m = labelJsonArr.getJSONObject(item)
                  label_id += m.getString("lableId")
                  label_type_id += m.getString("typeId")
                }
              }
              val label_type_id_str = label_type_id.mkString(",")
              val mTitle = row.getString("mTitle")
              val createDateTime = row.getString("createDateTime")
              val firstClassifyId = row.getString("first_classifyId")
              val firstClassifyName = row.getString("first_classifyName")
              val secondClassifyId = row.getString("second_classifyId")
              val secondClassifyName = row.getString("second_classifyName")
              val sourceType = row.getString("sourceType")
              val proName = row.getString("proName")
              val cityName = row.getString("citName")
              val content = row.getString("content")
              val timestamp = params.getString("timestamp")

              resultRow = (1, Row(id, title, mTitle, publishTime, createDateTime, firstClassifyId, firstClassifyName, secondClassifyId, secondClassifyName,
                content, relations(0).mkString(","), relations(1).mkString(","), relations(2).mkString(","), relations(3).mkString(","),
                relations(4).mkString(","), label_id.mkString(","), label_type_id_str, sourceType, proName, cityName,
                firstLabel, secondLabel, thirdLabel, operation, timestamp,relationId,relations(5).mkString(","), isDealer,day))
            }
            else if (params.getString("billType") == "article_t_video") { //清洗视频数据
              val timestamp = function_.getTimestamp(line.getAs[String]("@timestamp"))
              val labelId = row.getString("labelId")
              val labelTypeId = row.getString("labelTypeId").split(",").toSet.mkString(",") //去重
              val url = row.getString("url")
              val classifyId = row.getString("classifyId")
              val classifyName = row.getString("classifyName")
              val videoType = row.getString("videoType")
              val programId = row.getString("programId")
              val programName = row.getString("programName")

              resultRow = (2, Row(id, title, url, publishTime, classifyId, classifyName, videoType, programId, programName, relations(0).mkString(","),
                relations(1).mkString(","), relations(2).mkString(","), relations(3).mkString(","),
                relations(4).mkString(","), labelId, labelTypeId, firstLabel, secondLabel, thirdLabel, operation, timestamp,relationId,isDealer, day))
            }
          }

        } catch {
          case ex: JSONException => {
            println(ex)
          }
          case ex: NumberFormatException => {
            println(ex)
          }
          case ex: NullPointerException => {
            println(ex)
          }
        }
        resultRow
      })
      //保存文章数据
      spark.sql(s"ALTER TABLE t_ods_article DROP IF EXISTS PARTITION(day=$day)")
      spark.createDataFrame(resultRDD.filter(f => f._1 == 1).map(line => line._2), articleStruct).repartition(1).write.mode("append")
        .partitionBy("day").format("Hive").saveAsTable("t_ods_article")
      //保存视频数据
      spark.sql(s"ALTER TABLE t_ods_video DROP IF EXISTS PARTITION(day=$day)")
      spark.createDataFrame(resultRDD.filter(f => f._1 == 2).map(line => line._2), videoStruct).repartition(1).write.mode("append")
        .partitionBy("day").format("Hive").saveAsTable("t_ods_video")
    } catch {
      case e: Exception => {
        e.printStackTrace()
        try{
          val exec = new MySqlBaseDao()

          exec.executeInsertOrUpdate(s"update t_check set flag=2, message='$day:t_ods_video 运行失败' where name='t_ods_video'",null)
        }catch {
          case e:Throwable=>{
            e.printStackTrace()
          }
        }
      }
    }
  }

  /**
   * 清洗视频原始数据到数据明细层
   * @param day
   */
  def saveVideoDetail(day: String): Unit = {
    val spark = MySparkSession.conn("Logclean_PullBusinessData")
    try {
      spark.sql("set spark.sql.shuffle.partitions=1")
      spark.sql(
        s"""
           |INSERT OVERWRITE table t_dwd_video
           | SELECT
           |   nvl(ov.id,dv.id) as id,nvl(ov.title,dv.title) as title,nvl(ov.url,dv.url) as url,
           |   nvl(ov.publish_time,dv.publish_time) as publish_time,
           |   nvl(ov.classify_id,dv.classify_id) as classify_id,
           |   nvl(ov.classify_name,dv.classify_name) as classify_name,
           |   nvl(ov.video_type,dv.video_type) as video_type,
           |   nvl(ov.program_id,dv.program_id) as program_id,
           |   nvl(ov.program_name,dv.program_name)as program_name,
           |   nvl(ov.cate_id,dv.cate_id) as cate_id,
           |   nvl(ov.sub_cate_id,dv.sub_cate_id) as sub_cate_id,
           |   nvl(ov.brand_id,dv.brand_id) as brand_id,
           |   nvl(ov.series_id,dv.series_id) as series_id,
           |   nvl(ov.product_id,dv.product_id) as product_id,
           |   nvl(ov.label_id,dv.label_id) as label_id,
           |   nvl(ov.label_type_id,dv.label_type_id) as label_type_id,
           |   nvl(ov.first_label,dv.first_label) as first_label,
           |   nvl(ov.second_label,dv.second_label) as second_label,
           |   nvl(ov.third_label,dv.third_label) as third_label,
           |   nvl(ov.is_del,dv.is_del) as is_del,
           |   from_unixtime(unix_timestamp(nvl(ov.publish_time,dv.publish_time),'yyyy-MM-dd HH:mm:ss'),'yyyyMMdd') as publish_date,
           |   nvl(ov.relation_id,dv.relation_id) as relation_id,
           |   nvl(ov.is_dealer,dv.is_dealer) as is_dealer
           | from t_dwd_video dv
           |full outer join
           | (SELECT  DISTINCT v.id,title,url,publish_time,classify_id,classify_name,video_type,program_id,program_name,
           | cate_id,sub_cate_id,brand_id,series_id,product_id,label_id,label_type_id,first_label,second_label,third_label,relation_id,is_dealer,
           | if(v.operation_state='delete',1,0) as is_del from t_ods_video v
           | INNER join
           |(SELECT id,max(`timestamp`) max_time from t_ods_video where day='$day' GROUP by id) t
           | ON v.id=t.id and v.`timestamp`=t.max_time where v.`day`='$day') ov
           |ON dv.id=ov.id
           |""".stripMargin)
    } catch {
      case e: Exception => {
        e.printStackTrace()
        try{
          val exec = new MySqlBaseDao()

          exec.executeInsertOrUpdate(s"update t_check set flag=2, message='$day:t_dwd_video 运行失败' where name='t_dwd_video'",null)
        }catch {
          case e:Throwable=>{
            e.printStackTrace()
          }
        }
      }
    }
  }

  /**
   * 清洗文章原始数据到数据明细层
   *
   * @param day
   */
  def saveArticleDetail(day: String): Unit = {
    val spark = MySparkSession.conn("Logclean_PullBusinessData")
    try {
      spark.sql("set spark.sql.shuffle.partitions=1")
      spark.sql(
        s"""
           |INSERT OVERWRITE table t_dwd_article
           |SELECT nvl(ov.article_id,dv.article_id) as article_id,
           |       nvl(ov.title,dv.title) as title,
           |       nvl(ov.mtitle,dv.mtitle) as mtitle,
           |       nvl(ov.publish_time,dv.publish_time) as publish_time,
           |       nvl(ov.create_time,dv.create_time) as create_time,
           |       nvl(ov.first_classify_id,dv.first_classify_id) as first_classify_id,
           |       nvl(ov.first_classify_name,dv.first_classify_name) as first_classify_name,
           |       nvl(ov.second_classify_id,dv.second_classify_id) as second_classify_id,
           |       nvl(ov.second_classify_name,dv.second_classify_name) as second_classify_name,
           |       nvl(ov.content,dv.content) as content,
           |       nvl(ov.cate_id,dv.cate_id) as cate_id,
           |       nvl(ov.sub_cate_id,dv.sub_cate_id) as sub_cate_id,
           |       nvl(ov.brand_id,dv.brand_id) as brand_id,
           |       nvl(ov.series_id,dv.series_id) as series_id,
           |       nvl(ov.product_id,dv.product_id) as product_id,
           |       nvl(ov.label_id,dv.label_id) as label_id,
           |       nvl(ov.label_type_id,dv.label_type_id) as label_type_id,
           |       nvl(ov.source_type,dv.source_type) as source_type,
           |       nvl(ov.province,dv.province) as province,
           |       nvl(ov.city,dv.city) as city,
           |       nvl(ov.first_label,dv.first_label) as first_label,
           |       nvl(ov.second_label,dv.second_label) as second_label,
           |       nvl(ov.third_label,dv.third_label) as third_label,
           |       nvl(ov.is_del,dv.is_del) as is_del,
           |       from_unixtime(unix_timestamp(nvl(ov.publish_time,dv.publish_time),'yyyy-MM-dd HH:mm:ss'),'yyyyMMdd') as publish_date,
           |       nvl(ov.relation_id,dv.relation_id) as relation_id,
           |       nvl(ov.tonnage_type,dv.tonnage_type) as tonnage_type,
           |       if(ov.is_dealer="" or ov.is_dealer is null ,dv.is_dealer,ov.is_dealer) as is_dealer
           |from t_dwd_article dv
           |full outer join
           | (SELECT  DISTINCT v.article_id,title,mtitle,publish_time,create_time,first_classify_id,first_classify_name,
           |          second_classify_id,second_classify_name,content,
           |          cate_id,sub_cate_id,brand_id,series_id,product_id,label_id,label_type_id,
           |          source_type,province,city,first_label,second_label,third_label,relation_id,tonnage_type,is_dealer,
           |          if(v.operation_state='delete',1,0) as is_del
           | FROM t_ods_article v
           | INNER join
           | (SELECT article_id,max(`timestamp`) max_time from t_ods_article where day='$day' GROUP by article_id) t
           | ON v.article_id=t.article_id and v.`timestamp`=t.max_time where v.`day`='$day') ov
           |on dv.article_id=ov.article_id
           |""".stripMargin)
    } catch {
      case e: Exception => {
        e.printStackTrace()
        try{
          val exec = new MySqlBaseDao()

          exec.executeInsertOrUpdate(s"update t_check set flag=2, message='$day:t_ods_article 运行失败' where name='t_ods_article'",null)
        }catch {
          case e:Throwable=>{
            e.printStackTrace()
          }
        }
      }
    }
  }


  /**
   * 客服系统-用户基本信息，资讯信息
   */
  def kfSystem(spark:SparkSession,day: String): Unit = {
    val t_check_pre="aliyun_"
    try {
      val baseInfoTable = "t_ods_kf_user_info" //司机招聘 发布职位数据表
      val askRecordTable = "t_ods_kf_ask_record" //司机招聘 求职数据表
      //获取历史数据，做文件合并
      val baseInfoHistoryData = spark.sql("select id, user_tel, user_name, sex, age, province, city, county, wechat_num,wechat_name, " +
        s"identity, truckList, create_time,province_code,city_code,county_code, timestamp, operation_state, day from $baseInfoTable where day!='$day'")
      //获取基础信息
      spark.sql("select json_tuple(data,'id', 'userTel', 'userName', 'sex', 'age', 'province', " +
        "'city', 'county', 'wechatNum','wechatName', 'identity', 'truckList', 'createTime','provinceCode','cityCode','countyCode'),`timestamp`, operation_state,from_unixtime(substr(`timestamp`,0,10), 'yyyyMMdd') from t_biz_data_temp where billtype='kf_user_base_info'")
        .toDF("id", "user_tel", "user_name", "sex", "age", "province", "city", "county", "wechat_num", "wechat_name",
          "identity", "truckList", "create_time", "province_code", "city_code", "county_code", "timestamp", "operation_state", "day")
        .withColumn("id",col("id").cast("Int"))
        .union(baseInfoHistoryData).coalesce(1).createOrReplaceTempView("t_base_info_tmp")
      spark.sql(
        s"""
           | insert overwrite table $baseInfoTable select * from t_base_info_tmp
           |""".stripMargin)

      //客服记录
      //历史数据
      val recordHistory = spark.sql(
        s"""
           |select id, user_tel, ask_type, truck_list, is_buycar, buycar_province, buycar_city, buycar_county, buycar_price_min,
           |buycar_price_max, buycar_date, part_type, part_cateid, part_subcateid, part_brandid, part_seriesid, part_cate,
           |part_subcate, part_brand, part_series, ask_park_aim, policy, after_sale_service, complain, complain_type,
           |complain_name, complain_province, complain_city, complain_county, remarks, ask_date, create_time,
           |buycar_province_code,buycar_city_code,buycar_county_code,timestamp,
           |operation_state, day from $askRecordTable where day!='$day'
           |""".stripMargin)
      //最新数据清洗
      spark.sql("select json_tuple(data,'id', 'userTel', 'askType','truckList', 'isBuyCar', 'buyCarProvince', 'buyCarCity', " +
        "'buyCarCounty', 'buyCarPriceMin', 'buyCarPriceMax', 'buyCarDate', 'partType','partCateId','partSubcateId','partBrandId','partSeriesId', " +
        "'partCate', 'partSubcate', 'partBrand', 'partSeries', 'askParkAim','policy','afterSaleService','complain','complainType', " +
        "'complainName', 'complainProvince', 'complainCity', 'complainCounty', 'remarks','askDate', " +
        " 'createTime','buyCarProvinceCode','buyCarCityCode','buyCarCountyCode'),`timestamp`, operation_state,from_unixtime(substr(`timestamp`,0,10), 'yyyyMMdd') from t_biz_data_temp where billtype='kf_ask_record'")
        .toDF("id", "user_tel", "ask_type", "truck_list", "is_buycar", "buycar_province", "buycar_city", "buycar_county", "buycar_price_min",
          "buycar_price_max", "buycar_date", "part_type", "part_cateid", "part_subcateid", "part_brandid", "part_seriesid", "part_cate",
          "part_subcate", "part_brand", "part_series", "ask_park_aim", "policy", "after_sale_service", "complain", "complain_type",
          "complain_name", "complain_province", "complain_city", "complain_county", "remarks", "ask_date", "create_time",
          "buycar_province_code", "buycar_city_code", "buycar_county_code", "timestamp", "operation_state", "day")
        .union(recordHistory).coalesce(1)
        .withColumn("id",col("id").cast("Int"))
        .createOrReplaceTempView("t_ask_record_tmp")
      spark.sql(
        s"""
           | insert overwrite table $askRecordTable select * from t_ask_record_tmp
           |""".stripMargin)

      //数据并入数据明细层
      spark.sql("set spark.sql.shuffle.partitions=1")
      spark.sql("insert overwrite table t_dwd_kf_user_info " +
        "SELECT cast(nvl(ov.id,dv.id) as int) as id,nvl(ov.user_tel,dv.user_tel) as user_tel,nvl(ov.user_name,dv.user_name) as user_name," +
        "nvl(ov.sex,dv.sex) as sex," +
        "nvl(ov.age,dv.age) as age," +
        "nvl(ov.province,dv.province) as province," +
        "nvl(ov.city,dv.city) as city," +
        "nvl(ov.county,dv.county) as county," +
        "nvl(ov.wechat_num,dv.wechat_num)as wechat_num," +
        "nvl(ov.wechat_name,dv.wechat_name) as wechat_name," +
        "nvl(ov.identity,dv.identity) as identity," +
        "nvl(ov.trucklist,dv.trucklist) as trucklist," +
        "nvl(ov.create_time,dv.create_time) as create_time," +
        "nvl(ov.status,dv.status) as status," +
        "nvl(ov.day,dv.day) as day, " +
        "nvl(ov.province_code,dv.province_code) as province_code," +
        "nvl(ov.city_code,dv.city_code) as city_code," +
        "nvl(ov.county_code,dv.county_code) as county_code " +
        "from t_dwd_kf_user_info dv " +
        "full outer join (SELECT  DISTINCT v.id,user_tel,user_name,sex,age,province,city,county,wechat_num," +
        "wechat_name,identity,trucklist,create_time,province_code,city_code,county_code," +
        s"if(v.operation_state='delete',1,0) as status,day from $baseInfoTable v INNER join " +
        s"(SELECT id,max(`timestamp`) max_time from $baseInfoTable where day='$day' GROUP by id) t " +
        s"on v.id=t.id and v.`timestamp`=t.max_time where v.`day`='$day') ov on dv.id=ov.id")

      spark.sql(
        s"""
           |insert overwrite table t_dwd_kf_ask_record
           |SELECT nvl(ov.id,dv.id) as id,nvl(ov.user_tel,dv.user_tel) as user_tel,nvl(ov.ask_type,dv.ask_type) as ask_type,
           |nvl(ov.truck_list,dv.truck_list) as truck_list,
           |nvl(ov.is_buycar,dv.is_buycar) as is_buycar,
           |nvl(ov.buycar_province,dv.buycar_province) as buycar_province,
           |nvl(ov.buycar_city,dv.buycar_city) as buycar_city,
           |nvl(ov.buycar_county,dv.buycar_county) as buycar_county,
           |nvl(ov.buycar_price_min,dv.buycar_price_min)as buycar_price_min,
           |nvl(ov.buycar_price_max,dv.buycar_price_max) as buycar_price_max,
           |nvl(ov.buycar_date,dv.buycar_date) as buycar_date,
           |nvl(ov.part_type,dv.part_type) as part_type,
           |nvl(ov.part_cateid,dv.part_cateid) as part_cateid,
           |nvl(ov.part_subcateid,dv.part_subcateid) as part_subcateid,
           |nvl(ov.part_brandid,dv.part_brandid) as part_brandid,
           |nvl(ov.part_seriesid,dv.part_seriesid) as part_seriesid,
           |nvl(ov.part_cate,dv.part_cate) as part_cate,
           |nvl(ov.part_subcate,dv.part_subcate) as part_subcate,
           |nvl(ov.part_brand,dv.part_brand) as part_brand,
           |nvl(ov.part_series,dv.part_series) as part_series,
           |nvl(ov.ask_park_aim,dv.ask_park_aim) as ask_park_aim,
           |nvl(ov.policy,dv.policy) as policy,
           |nvl(ov.after_sale_service,dv.after_sale_service) as after_sale_service,
           |nvl(ov.complain,dv.complain) as complain,
           |nvl(ov.complain_type,dv.complain_type) as complain_type,
           |nvl(ov.complain_name,dv.complain_name) as complain_name,
           |nvl(ov.complain_province,dv.complain_province) as complain_province,
           |nvl(ov.complain_city,dv.complain_city) as complain_city,
           |nvl(ov.complain_county,dv.complain_county) as complain_county,
           |nvl(ov.remarks,dv.remarks) as remarks,
           |nvl(ov.ask_date,dv.ask_date) as ask_date,
           |nvl(ov.create_time,dv.create_time) as create_time,
           |nvl(ov.status,dv.status) as status,
           |nvl(ov.day,dv.day) as day,
           |nvl(ov.buycar_city_code,dv.buycar_city_code) as buycar_city_code,
           |nvl(ov.buycar_province_code,dv.buycar_province_code) as buycar_province_code,
           |nvl(ov.buycar_county_code,dv.buycar_county_code) as buycar_county_code
           |from t_dwd_kf_ask_record dv
           |full outer join (SELECT  DISTINCT v.id,user_tel, ask_type, truck_list, is_buycar, buycar_province, buycar_city, buycar_county, buycar_price_min,
           |buycar_price_max, buycar_date, part_type, part_cateid, part_subcateid, part_brandid, part_seriesid, part_cate,
           |part_subcate, part_brand, part_series, ask_park_aim, policy, after_sale_service, complain, complain_type,
           |complain_name, complain_province, complain_city, complain_county, remarks, ask_date, create_time,
           |buycar_province_code,buycar_city_code,buycar_county_code,
           |if(v.operation_state='delete',1,0) as status,day from $askRecordTable v INNER join
           |(SELECT id,max(`timestamp`) max_time from $askRecordTable where day='$day' GROUP by id) t
           |on v.id=t.id and v.`timestamp`=t.max_time where v.`day`='$day') ov on dv.id=ov.id
           |""".stripMargin)
      val exec = new MySqlBaseDao()
      exec.executeInsertOrUpdate(s"update t_check set flag=1 , message='$day:运行成功' where name='${t_check_pre}t_ods_kf_user_info'",null)
    } catch {
      case e: Exception => {
        e.printStackTrace()
        try{
          val exec = new MySqlBaseDao()

          exec.executeInsertOrUpdate(s"update t_check set flag=2, message='$day:${t_check_pre}t_ods_kf_user_info 运行失败' where name='${t_check_pre}t_ods_kf_user_info'",null)
        }catch {
          case e:Throwable=>{
            e.printStackTrace()
          }
        }
      }
    }
  }
  /**
   * 基于t_dwd_article文章明细，增加文章其它信息汇总成宽表到数据服务层t_dws_article
   */

  def saveArticleToDWS(spark:SparkSession,day:String): Unit ={

    val t_check_pre="aliyun_"
    try {
      import spark.implicits._
      val resultDF=spark.sql(
        s"""
           |select article_id, title,mtitle,publish_time, create_time, first_classify_id, first_classify_name, second_classify_id, second_classify_name, content, cate_id, sub_cate_id, brand_id, series_id, product_id, label_id, label_type_id, source_type, province, city, first_label, second_label, third_label, is_del, publish_date, relation_id, tonnage_type,is_dealer,is_dealer_push,scenario,census_typeone_id,census_typeone_name,census_typetwo_id,census_typetwo_name,user_type_id,user_type_name,authorid,talkids,program_id,program_name from t_dwd_article
           |""".stripMargin).mapPartitions(par => {
        val result = new mutable.MutableList[AtricleDWS]()
        for(item <- par){
          result.+=(AtricleDWS(item.getAs[Int]("article_id"),item.getAs[String]("title"),
            item.getAs[String]("mtitle"),item.getAs[String]("publish_time"),
            item.getAs[String]("create_time"),item.getAs[Int]("first_classify_id"),
            item.getAs[String]("first_classify_name"),item.getAs[Int]("second_classify_id"),
            item.getAs[String]("second_classify_name"),item.getAs[String]("content"),
            item.getAs[String]("cate_id"),item.getAs[String]("sub_cate_id"),
            item.getAs[String]("brand_id"),item.getAs[String]("series_id"),
            item.getAs[String]("product_id"),item.getAs[String]("label_id"),
            item.getAs[String]("label_type_id"),item.getAs[String]("source_type"),
            item.getAs[String]("province"),item.getAs[String]("city"),
            item.getAs[String]("first_label"),item.getAs[String]("second_label"),
            item.getAs[String]("third_label"),item.getAs[Int]("is_del"),
            item.getAs[String]("publish_date"),item.getAs[String]("relation_id"),
            item.getAs[String]("tonnage_type"),item.getAs[Int]("is_dealer"),item.getAs[Int]("is_dealer_push"),
            item.getAs[String]("scenario"),item.getAs[Int]("census_typeone_id"),item.getAs[String]("census_typeone_name"),
            item.getAs[Int]("census_typetwo_id"),item.getAs[String]("census_typetwo_name"),item.getAs[Int]("user_type_id"),item.getAs[String]("user_type_name"),
            segmenter.process(item.getAs[String]("mtitle"),SegMode.SEARCH)
              .toArray().map(_.asInstanceOf[SegToken].word)
              .filter(_.length>1),
            item.getAs[Int]("authorid"),
            item.getAs[String]("talkids"),
            item.getAs[String]("program_id"),
            item.getAs[String]("program_name")
          ))
        }
        result.iterator
      })
      spark.sql("truncate table t_dws_article")
      resultDF.repartition(1).write.mode("append").format("Hive").saveAsTable("t_dws_article")
      val exec = new MySqlBaseDao()
      exec.executeInsertOrUpdate(s"update t_check set flag=1 , message='$day:运行成功' where name='${t_check_pre}t_dws_article'",null)
    } catch {
      case e: Exception => {
        e.printStackTrace()
        try{
          val exec = new MySqlBaseDao()

          exec.executeInsertOrUpdate(s"update t_check set flag=2, message='$day:${t_check_pre}t_dws_article 运行失败' where name='${t_check_pre}t_dws_article'",null)
        }catch {
          case e:Throwable=>{
            e.printStackTrace()
          }
        }
      }
    }

  }

  /**
   * 通过接口方式请求文章数据并写入dwd层
   */
  def pullArticleToDWD(ss:SparkSession,startDate:String,endDate:String): Unit = {
    val t_check_pre="aliyun_"
    try {
      val resultTable="t_dwd_article"
      val sqlBase=new MySqlBaseDao()
      //文章数据字典
      val articleStruct = StructType(Array(
        StructField("article_id", IntegerType, true),
        StructField("title", StringType, true), StructField("mtitle", StringType, true),
        StructField("publish_time", StringType, true), StructField("create_time", StringType, true),
        StructField("first_classify_id", IntegerType, true), StructField("first_classify_name", StringType, true),
        StructField("second_classify_id", IntegerType, true), StructField("second_classify_name", StringType, true),
        StructField("content", StringType, true),
        StructField("cate_id", StringType, true), StructField("sub_cate_id", StringType, true),
        StructField("brand_id", StringType, true), StructField("series_id", StringType, true), StructField("product_id", StringType, true),
        StructField("label_id", StringType, true), StructField("label_type_id", StringType, true), StructField("source_type", StringType, true),
        StructField("province", StringType, true), StructField("city", StringType, true),
        StructField("first_label", StringType, true),
        StructField("second_label", StringType, true),
        StructField("third_label", StringType, true),
        StructField("is_del", IntegerType, true),
        StructField("publish_date", StringType, true),
        StructField("relation_id", StringType, true),
        StructField("tonnage_type", StringType, true),
        StructField("is_dealer", IntegerType, true),
        StructField("is_dealer_push", IntegerType, true),
        StructField("scenario", StringType, true), //场景id,行业#细分市场#货品#子类#运距#场
        StructField("census_typeone_id", IntegerType, true),
        StructField("census_typeone_name", StringType, true),
        StructField("census_typetwo_id", IntegerType, true),
        StructField("census_typetwo_name", StringType, true),
        StructField("user_type_id", IntegerType, true),
        StructField("user_type_name", StringType, true),
        StructField("authorid", IntegerType, true),
        StructField("talkids", StringType, true),
        StructField("program_id", StringType, true),
        StructField("program_name", StringType, true)
      ))
      //车系吨位映射数据
      val seriesTonnageM = mutable.Map[String, String]()
      val rs = sqlBase.executeQuery("select seriesId, tonnageType from db_userportrait.t_series_tonnage", null)
      while (rs.next()) {
        seriesTonnageM += (rs.getInt("seriesId").toString -> rs.getInt("tonnageType").toString)
      }
      val reader=ResourceBundle.getBundle("connection")
      val api=reader.getString("api.article.info")+s"?startDate=$startDate&endDate=$endDate"
      val entityStr=MyHttpService.getHttpRequest2(api)
      val entityObject=JSON.parseObject(entityStr)
      val status=entityObject.getIntValue("status")
      if (status==0){
        val datas=entityObject.getJSONArray("data")
        if(datas != null){
          val indexRDD=ss.sparkContext.parallelize(0 until datas.size())
          val rowRDD=indexRDD.map(index=>{
            val item=datas.getJSONObject(index)
            val articleId =item.getIntValue("id")
            val title=item.getString("title")
            val mtitle=item.getString("mTitle")
            val publishTime=item.getString("publishTime")
            val publishDate=publishTime.substring(0,10).replace("-","")
            val createDateTime=item.getString("createDateTime")
            val firstClassifyId=item.getIntValue("first_classifyId")
            val firstClassifyName=item.getString("first_classifyName")
            val secondClassifyId=item.getIntValue("second_classifyId")
            val secondClassifyName=item.getString("second_classifyName")
            val content=item.getString("content")
            val relationId=item.getString("relationId")
            //获取文章关联产品信息 埋点格式：1#66#17#6976#81218 大类#子类#品牌#车系#车型，多个产品用‘|’分隔
            val relations = Array(Set[String](), Set[String](), Set[String](), Set[String](), Set[String](),Set[String](), Set[String]())
            var i = 0
            for (item <- relationId.split("\\|")) {
              for (item1 <- item.split("#")) {
                relations(i).add(item1)
                if (i == 3 && seriesTonnageM.contains(item1)) {
                  relations(5).add(seriesTonnageM(item1))
                }
                i += 1
              }
              i = 0
            }
            val cateId=relations(0).filter(_ != "0").mkString(",")
            val subCateId=relations(1).filter(_ != "0").mkString(",")
            val brandId=relations(2).filter(_ != "0").mkString(",")
            val seriesId=relations(3).filter(_ != "0").mkString(",")
            val productId=relations(4).filter(_ != "0").mkString(",")
            val tonnageType=relations(5).filter(_ != "0").mkString(",")
            val lable=item.getJSONArray("lable")
            //文章标签埋点格式：[{"lableId":44,"TypeId":6},{"lableId":49,"TypeId":6},{"lableId":54,"TypeId":6}]
            var labelId = Set[String]()
            val labelTypeId = Set[String]()
            for (i <- 0 until lable.size()) {
              val m = lable.getJSONObject(i)
              labelId += m.getString("lableId")
              labelTypeId += m.getString("TypeId")
            }
            val lableIdStr=labelId.mkString(",")
            val labelTypeIdStr = labelTypeId.mkString(",")
            val sourceType=item.getString("sourceType")
            val provinceName=item.getString("proName")
            val cityName=item.getString("citName")
            val firstLabel=item.getString("firstLabel")
            val secondLabel=item.getString("secondLabel")
            val thirdLabel=item.getString("thirdLabel")
            val isDealer=item.getIntValue("isDealer")
            val isDealerPush=item.getIntValue("isDealerPush")
            val isDelete=item.getIntValue("isDelete")
            val scenario =item.getOrDefault("scenario","").toString
            val censusTypeOneId =item.getIntValue("censusTypeOneId")
            val censusTypeOneName =item.getOrDefault("censusTypeOneName","").toString
            val censusTypeTwoId =item.getIntValue("censusTypeTwoId")
            val censusTypeTwoName =item.getOrDefault("censusTypeTwoName","").toString
            val userTypeId =item.getIntValue("userTypeId")
            val userTypeName =item.getOrDefault("userTypeName","").toString
            val authorid=item.getOrDefault("authorid","0").toString.toInt
            val talkids=item.getOrDefault("talkids","").toString
            val programId=item.getOrDefault("programId","0").toString
            val programName=item.getOrDefault("programName","").toString

            Row(articleId,title,mtitle,publishTime,createDateTime,firstClassifyId,firstClassifyName,secondClassifyId,secondClassifyName,
              content,cateId,subCateId,brandId,seriesId,productId,lableIdStr,labelTypeIdStr,sourceType,provinceName,
              cityName,firstLabel,secondLabel,thirdLabel,isDelete,publishDate,relationId,tonnageType,isDealer,isDealerPush,scenario,
              censusTypeOneId,censusTypeOneName,censusTypeTwoId,censusTypeTwoName,userTypeId,userTypeName,authorid,talkids,programId,programName)
          })
          ss.sqlContext.createDataFrame(rowRDD,articleStruct)
            //          .show(false)
            .createOrReplaceTempView("t_article_tmp")
          ss.sql(
            s"""
               |INSERT OVERWRITE table $resultTable
               |(SELECT
               |       b.article_id,
               |       b.title,
               |       b.mtitle,
               |       b.publish_time,
               |       b.create_time,
               |       b.first_classify_id,
               |       b.first_classify_name,
               |       b.second_classify_id,
               |       b.second_classify_name,
               |       b.content,
               |       b.cate_id,
               |       b.sub_cate_id,
               |       b.brand_id,
               |       b.series_id,
               |       b.product_id,
               |       b.label_id,
               |       b.label_type_id,
               |       b.source_type,
               |       b.province,
               |       b.city,
               |       b.first_label,
               |       b.second_label,
               |       b.third_label,
               |       b.is_del,
               |       b.publish_date,
               |       b.relation_id,
               |       b.tonnage_type,
               |       b.is_dealer,
               |       b.is_dealer_push,
               |       b.scenario,
               |       b.census_typeone_id,
               |       b.census_typeone_name,
               |       b.census_typetwo_id,
               |       b.census_typetwo_name,
               |       b.user_type_id,
               |       b.user_type_name,
               |       b.authorid,
               |       b.talkids,
               |       b.program_id,
               |       b.program_name
               |from t_article_tmp a
               |right join
               | $resultTable b
               |on a.article_id=b.article_id
               |having a.article_id is null
               |union all
               |select
               |       article_id,
               |       title,
               |       mtitle,
               |       publish_time,
               |       create_time,
               |       first_classify_id,
               |       first_classify_name,
               |       second_classify_id,
               |       second_classify_name,
               |       content,
               |       cate_id,
               |       sub_cate_id,
               |       brand_id,
               |       series_id,
               |       product_id,
               |       label_id,
               |       label_type_id,
               |       source_type,
               |       province,
               |       city,
               |       first_label,
               |       second_label,
               |       third_label,
               |       is_del,
               |       publish_date,
               |       relation_id,
               |       tonnage_type,
               |       is_dealer,
               |       is_dealer_push,
               |       scenario,
               |       census_typeone_id,
               |       census_typeone_name,
               |       census_typetwo_id,
               |       census_typetwo_name,
               |       user_type_id,
               |       user_type_name,
               |       authorid,
               |       talkids,
               |       program_id,
               |       program_name
               |from t_article_tmp)
               |""".stripMargin)
        }
      }
      val exec = new MySqlBaseDao()
      exec.executeInsertOrUpdate(s"update t_check set flag=1 , message='$startDate:运行成功' where name='${t_check_pre}t_dwd_article'",null)
    } catch {
      case e: Exception => {
        e.printStackTrace()
        try{
          val exec = new MySqlBaseDao()
          exec.executeInsertOrUpdate(s"update t_check set flag=2, message='$startDate:${t_check_pre}t_dwd_article 运行失败' where name='${t_check_pre}t_dwd_article'",null)
        }catch {
          case e:Throwable=>{
            e.printStackTrace()
          }
        }
      }
    }

  }

  /**
   * 通过接口方式请求视频数据并写入dwd层
   */
  def pullVideoToDWD(ss:SparkSession,startDate:String,endDate:String): Unit = {
    val t_check_pre="aliyun_"
    try {
      val resultTable="t_dwd_video"
      val videoStruct = StructType(Array(
        StructField("id", StringType, true),
        StructField("title", StringType, true),
        StructField("url", StringType, true),
        StructField("publish_time", StringType, true),
        StructField("classify_id", StringType, true), StructField("classify_name", StringType, true),
        StructField("video_type", StringType, true), StructField("program_id", StringType, true),
        StructField("program_name", StringType, true),
        StructField("cate_id", StringType, true), StructField("sub_cate_id", StringType, true),
        StructField("brand_id", StringType, true), StructField("series_id", StringType, true), StructField("product_id", StringType, true),
        StructField("label_id", StringType, true), StructField("label_type_id", StringType, true),
        StructField("first_label", StringType, true),
        StructField("second_label", StringType, true),
        StructField("third_label", StringType, true),
        StructField("is_del", IntegerType, true),
        StructField("publish_date", StringType, true),
        StructField("relation_id", StringType, true),
        StructField("is_dealer", StringType, true),
        StructField("scenario", StringType, true), //行业#细分市场#货品#子类#运距#场
        StructField("f_total_time",StringType,true),
        StructField("census_typeone_id", IntegerType, true),
        StructField("census_typeone_name", StringType, true),
        StructField("census_typetwo_id", IntegerType, true),
        StructField("census_typetwo_name", StringType, true),
        StructField("user_type_id", IntegerType, true),
        StructField("user_type_name", StringType, true),
        StructField("first_classify_id", IntegerType, true), StructField("first_classify_name", StringType, true),
        StructField("second_classify_id", IntegerType, true), StructField("second_classify_name", StringType, true),
        StructField("authorid", IntegerType, true),
        StructField("talkids", StringType, true)

      ))
      val reader=ResourceBundle.getBundle("connection")
      val api=reader.getString("api.video.info")+s"?startDate=$startDate&endDate=$endDate"
      val entityStr=MyHttpService.getHttpRequest2(api)
      val entityObject=JSON.parseObject(entityStr)
      val status=entityObject.getIntValue("status")
      if (status==0) {
        val datas = entityObject.getJSONArray("data")
        if (datas != null) {
          val indexRDD = ss.sparkContext.parallelize(0 until datas.size())
          val rowRDD = indexRDD.map(index => {
            val item = datas.getJSONObject(index)
            val id = item.getString("id")
            val title = item.getString("title")
            val url = item.getString("url")
            val classifyId = item.getString("classifyId")
            val classifyName = item.getString("classifyName")
            val videoType = item.getString("videoType")
            val programId = item.getString("programId")
            val programName = item.getString("programName")
            val relationId = item.getString("relationId")
            //获取视频关联产品信息 埋点格式：1#66#17#6976#81218 大类#子类#品牌#车系#车型，多个产品用‘|’分隔
            val relations = Array(Set[String](), Set[String](), Set[String](), Set[String](), Set[String](), Set[String]())
            var i = 0
            for (item <- relationId.split("\\|")) {
              for (item1 <- item.split("#")) {
                relations(i).add(item1)
                i += 1
              }
              i = 0
            }
            val cateId = relations(0).filter(_ != "0").mkString(",")
            val subCateId = relations(1).filter(_ != "0").mkString(",")
            val brandId = relations(2).filter(_ != "0").mkString(",")
            val seriesId = relations(3).filter(_ != "0").mkString(",")
            val productId = relations(4).filter(_ != "0").mkString(",")
            val labelId = item.getString("labelId")
            val labelTypeId = item.getString("labelTypeId")
            val firstLabel = item.getString("firstLabel")
            val secondLabel = item.getString("secondLabel")
            val thirdLabel = item.getString("thirdLabel")
            val isDealer = item.getString("isDealer")
            val isDelete = item.getIntValue("isDelete")
            val publishTime = item.getString("publishTime")
            val publishDate = publishTime.substring(0, 10).replace("-", "")
            val scenario =item.getOrDefault("scenario","").toString
            val totalTime = item.getString("f_total_time")
            val censusTypeOneId =item.getIntValue("censusTypeOneId")
            val censusTypeOneName =item.getOrDefault("censusTypeOneName","").toString
            val censusTypeTwoId =item.getIntValue("censusTypeTwoId")
            val censusTypeTwoName =item.getOrDefault("censusTypeTwoName","").toString
            val userTypeId =item.getIntValue("userTypeId")
            val userTypeName =item.getOrDefault("userTypeName","").toString
            val firstClassifyId=item.getIntValue("first_classifyId")
            val firstClassifyName=item.getOrDefault("first_classifyName","")
            val secondClassifyId=item.getIntValue("second_classifyId")
            val secondClassifyName=item.getOrDefault("second_classifyName","")
            val authorid=item.getOrDefault("authorid","0").toString.toInt
            val talkids=item.getOrDefault("talkids","").toString
            Row(id, title, url, publishTime, classifyId, classifyName, videoType, programId, programName, cateId, subCateId,
              brandId, seriesId, productId, labelId, labelTypeId, firstLabel, secondLabel, thirdLabel, isDelete, publishDate, relationId, isDealer,
              scenario,totalTime,censusTypeOneId,censusTypeOneName,censusTypeTwoId,censusTypeTwoName,userTypeId,userTypeName,firstClassifyId,
              firstClassifyName,secondClassifyId,secondClassifyName,authorid,talkids)
          })
          ss.sqlContext.createDataFrame(rowRDD, videoStruct).createOrReplaceTempView("t_video_tmp")
          //        ss.sql(
          //          """
          //            |select * from t_video_tmp
          //            |""".stripMargin).show()
          ss.sql(
            s"""
               |INSERT OVERWRITE table $resultTable
               |(SELECT
               |   b.id,
               |   b.title,
               |   b.url,
               |   b.publish_time,
               |   b.classify_id,
               |   b.classify_name,
               |   b.video_type,
               |   b.program_id,
               |   b.program_name,
               |   b.cate_id,
               |   b.sub_cate_id,
               |   b.brand_id,
               |   b.series_id,
               |   b.product_id,
               |   b.label_id,
               |   b.label_type_id,
               |   b.first_label,
               |   b.second_label,
               |   b.third_label,
               |   b.is_del,
               |   b.publish_date,
               |   b.relation_id,
               |   b.is_dealer,
               |   b.scenario,
               |   b.f_total_time,
               |   b.census_typeone_id,
               |   b.census_typeone_name,
               |   b.census_typetwo_id,
               |   b.census_typetwo_name,
               |   b.user_type_id,
               |   b.user_type_name,
               |   b.first_classify_id,
               |   b.first_classify_name,
               |   b.second_classify_id,
               |   b.second_classify_name,
               |   b.authorid,
               |   b.talkids
               |from t_video_tmp as a
               |right join
               | $resultTable as b
               |ON a.id=b.id
               |having a.id is null
               |union all
               |select
               |   id,
               |   title,
               |   url,
               |   publish_time,
               |   classify_id,
               |   classify_name,
               |   video_type,
               |   program_id,
               |   program_name,
               |   cate_id,
               |   sub_cate_id,
               |   brand_id,
               |   series_id,
               |   product_id,
               |   label_id,
               |   label_type_id,
               |   first_label,
               |   second_label,
               |   third_label,
               |   is_del,
               |   publish_date,
               |   relation_id,
               |   is_dealer,
               |   scenario,
               |   f_total_time,
               |   census_typeone_id,
               |   census_typeone_name,
               |   census_typetwo_id,
               |   census_typetwo_name,
               |   user_type_id,
               |   user_type_name,
               |   first_classify_id,
               |   first_classify_name,
               |   second_classify_id,
               |   second_classify_name,
               |   authorid,
               |   talkids
               |from t_video_tmp)
               |""".stripMargin)
        }
      }
      val exec = new MySqlBaseDao()
      exec.executeInsertOrUpdate(s"update t_check set flag=1 , message='$startDate:运行成功' where name='${t_check_pre}t_dwd_video'",null)
    } catch {
      case e: Exception => {
        e.printStackTrace()
        try{
          val exec = new MySqlBaseDao()
          exec.executeInsertOrUpdate(s"update t_check set flag=2, message='$startDate:${t_check_pre}t_dwd_video 运行失败' where name='${t_check_pre}t_dwd_video'",null)
        }catch {
          case e:Throwable=>{
            e.printStackTrace()
          }
        }
      }
    }

  }

  /**
   * 通过接口方式请求直播数据并写入dwd层
   */
  def pullLiveToDWD(ss:SparkSession,startDate:String,endDate:String): Unit = {
    val t_check_pre="aliyun_"
    try {
      val liveStruct = StructType(Array(
        StructField("id", StringType, true),
        StructField("title", StringType, true),
        StructField("user_id", StringType, true),
        StructField("nickname", StringType, true),
        StructField("billtype", StringType, true),
        StructField("display", StringType, true),
        StructField("is_dealer", StringType, true),
        StructField("zan_num", StringType, true),
        StructField("history_num", StringType, true),
        StructField("relationId", StringType, true),
        StructField("cate_Id", StringType, true),
        StructField("subCate_Id", StringType, true),
        StructField("brand_Id", StringType, true),
        StructField("series_Id", StringType, true),
        StructField("product_Id", StringType, true),
        StructField("create_time", StringType, true),
        StructField("createDate", StringType, true),
        StructField("start_time", StringType, true),
        StructField("startDate", StringType, true),
        StructField("end_time", StringType, true),
        StructField("endDate", StringType, true)
      ))
      val tokenValue="enddate="+URLEncoder.encode(endDate)+"&startdate="+URLEncoder.encode(startDate)+"xHk0C7dzl3SEwO1Z"
      val reader=ResourceBundle.getBundle("connection")
      val api=reader.getString("api.live.info")
      val paramMap:Map[String,String]=Map(("startDate",startDate),("endDate",endDate))
      val entityStr=MyHttpService.post(api,paramMap, function_.MD5(tokenValue) ,"application/x-www-form-urlencoded")
      println(entityStr)
      val entityObject=JSON.parseObject(entityStr)
      val status=entityObject.getIntValue("status")
      if (status==0) {
        val datas = entityObject.getJSONArray("data")
        if (datas != null) {
          val indexRDD = ss.sparkContext.parallelize(0 until datas.size())
          val rowRDD = indexRDD.map(index => {
            val item = datas.getJSONObject(index)
            val id = item.getString("id")
            val title = item.getString("title")
            val user_id = item.getString("user_id")
            val nickname = item.getString("nickname")
            val billtype = item.getString("type")
            val display = item.getString("display")
            val is_dealer = item.getString("is_dealer")
            val zan_num = item.getString("zan_num")
            val history_num = item.getString("history_num")
            val relationId = item.getString("relationId")
            //获取视频关联产品信息 埋点格式：1#66#17#6976#81218 大类#子类#品牌#车系#车型，多个产品用‘|’分隔
            val relations = Array(Set[String](), Set[String](), Set[String](), Set[String](), Set[String]())
            var i = 0
            for (item <- relationId.split("\\|")) {
              for (item1 <- item.split("#")) {
                relations(i).add(item1)
                i += 1
              }
              i = 0
            }
            val cate_Id = relations(0).mkString(",")
            val subCate_Id = relations(1).mkString(",")
            val brand_Id = relations(2).mkString(",")
            val series_Id = relations(3).mkString(",")
            val product_Id = relations(4).mkString(",")
            val create_Time = item.getString("create_time")
            val createDate = create_Time.substring(0, 10).replace("-", "")
            val start_Time = item.getString("start_time")
            val startDate = start_Time.substring(0, 10).replace("-", "")
            val end_Time = item.getString("end_time")
            val endDate = end_Time.substring(0, 10).replace("-", "")
            Row(id, title, user_id, nickname, billtype, display, is_dealer, zan_num,history_num,relationId, cate_Id, subCate_Id,
              brand_Id, series_Id, product_Id,create_Time,createDate,start_Time,startDate,end_Time,endDate)
          })
          ss.sqlContext.createDataFrame(rowRDD, liveStruct).createOrReplaceTempView("t_live_tmp")
          ss.sql(
            s"""
               |INSERT OVERWRITE table t_dwd_live
               | SELECT
               |   nvl(a.id,b.id) as id,
               |   nvl(a.title,b.title) as title,
               |   nvl(a.user_id,b.user_id) as user_id,
               |   nvl(a.nickname,b.nickname) as nickname,
               |   nvl(a.billtype,b.billtype) as billtype,
               |   nvl(a.display,b.display) as display,
               |   nvl(a.relationId,b.relationId) as relationId,
               |   nvl(a.is_dealer,b.is_dealer) as is_dealer,
               |   nvl(a.zan_num,b.zan_num)as zan_num,
               |   nvl(a.cate_Id,b.cate_Id) as cate_Id,
               |   nvl(a.subCate_Id,b.subCate_Id) as subCate_Id,
               |   nvl(a.brand_Id,b.brand_Id) as brand_Id,
               |   nvl(a.series_Id,b.series_Id) as series_Id,
               |   nvl(a.product_Id,b.product_Id) as product_Id,
               |   nvl(a.history_num,b.history_num) as history_num,
               |   nvl(a.create_time,b.create_time) as create_time,
               |   nvl(b.start_time,a.start_time) as start_time,
               |   nvl(b.end_time,a.end_time) as end_time,
               |   nvl(a.createDate,b.createDate) as createDate,
               |   nvl(b.startDate,a.startDate) as startDate,
               |   nvl(b.endDate,a.endDate) as endDate
               |from t_live_tmp as a
               |full outer join
               | t_dwd_live as b
               |ON a.id=b.id
               |""".stripMargin)
        }
      }
      val exec = new MySqlBaseDao()
      exec.executeInsertOrUpdate(s"update t_check set flag=1 , message='$startDate:运行成功' where name='${t_check_pre}t_dwd_live'",null)
    } catch {
      case e: Exception => {
        e.printStackTrace()
        try{
          val exec = new MySqlBaseDao()
          exec.executeInsertOrUpdate(s"update t_check set flag=2, message='$startDate:${t_check_pre}t_dwd_live 运行失败' where name='${t_check_pre}t_dwd_live'",null)
        }catch {
          case e:Throwable=>{
            e.printStackTrace()
          }
        }
      }
    }

  }



  /**
   * 通过接口方式请求图集数据并写入dwd层
   */
  def pullPictureToDWD(ss:SparkSession,startDate:String,endDate:String): Unit = {
    val t_check_pre="aliyun_"
    try {
      val picStruct = StructType(Array(
        StructField("id", StringType, true),
        StructField("title", StringType, true),
        StructField("sourceType", StringType, true),
        StructField("relationId", StringType, true),
        StructField("createTime", StringType, true),
        StructField("publishTime", StringType, true),
        StructField("cate_id", StringType, true),
        StructField("subcate_id", StringType, true),
        StructField("brand_id", StringType, true),
        StructField("series_id", StringType, true),
        StructField("product_id", StringType, true),
        StructField("createDate", StringType, true),
        StructField("publishDate", StringType, true),
        StructField("isEnable", IntegerType, true),
        StructField("image_Num", IntegerType, true)
      ))
      val reader=ResourceBundle.getBundle("connection")
      val api=reader.getString("api.picture.info")+s"?startDate=$startDate&endDate=$endDate"
      val entityStr=MyHttpService.getHttpRequest2(api)
      val entityObject=JSON.parseObject(entityStr)
      val status=entityObject.getIntValue("status")
      if (status==0) {
        val datas = entityObject.getJSONArray("data")
        if (datas != null) {
          val indexRDD = ss.sparkContext.parallelize(0 until datas.size())
          val rowRDD = indexRDD.map(index => {
            val item = datas.getJSONObject(index)
            val id = item.getString("id")
            val title = item.getString("title")
            val sourceType = item.getString("sourceType")
            val relationId = item.getString("relationId")
            val createTime = item.getString("createTime")
            val publishTime = item.getString("publishTime")
            //获取视频关联产品信息 埋点格式：1#66#17#6976#81218 大类#子类#品牌#车系#车型，多个产品用‘|’分隔
            val relations = Array(Set[String](), Set[String](), Set[String](), Set[String](), Set[String]())
            var i = 0
            for (item <- relationId.split("\\|")) {
              for (item1 <- item.split("#")) {
                relations(i).add(item1)
                i += 1
              }
              i = 0
            }
            val cateId = relations(0).mkString(",")
            val subCateId = relations(1).mkString(",")
            val brandId = relations(2).mkString(",")
            val seriesId = relations(3).mkString(",")
            val productId = relations(4).mkString(",")
            val createDate = createTime.substring(0, 10).replace("-", "")
            val publishDate = publishTime.substring(0, 10).replace("-", "")
            val isEnable = item.getIntValue("isenable")
            val image_Num = if(item.getString("image_num")!=null&&item.getString("image_num")!="") {item.getString("image_num").toInt} else  0
            Row(id, title,sourceType, relationId,createTime,publishTime,cateId, subCateId,
              brandId, seriesId, productId,createDate, publishDate, isEnable,image_Num)
          })
          ss.sqlContext.createDataFrame(rowRDD, picStruct).createOrReplaceTempView("t_picture_tmp")
          //          ss.sql(
          //            """
          //              |select * from t_picture_tmp
          //              |""".stripMargin).show()
          ss.sql(
            s"""
               |INSERT OVERWRITE table t_dwd_picture
               |(SELECT
               |   b.id,
               |   b.title,
               |   b.sourceType,
               |   b.relationId,
               |   b.createTime,
               |   b.publishTime,
               |   b.cate_id,
               |   b.subcate_id,
               |   b.brand_id,
               |   b.series_id,
               |   b.product_id,
               |   b.createDate,
               |   b.publishDate,
               |   b.isEnable,
               |   b.image_Num
               |from t_picture_tmp as a
               |right join
               | t_dwd_picture as b
               |ON a.id=b.id
               |having a.id is null
               |union all
               |select
               |   id,
               |   title,
               |   sourceType,
               |   relationId,
               |   createTime,
               |   publishTime,
               |   cate_id,
               |   subcate_id,
               |   brand_id,
               |   series_id,
               |   product_id,
               |   createDate,
               |   publishDate,
               |   isEnable,
               |   image_Num
               |from t_picture_tmp)
               |""".stripMargin)
        }
      }
      val exec = new MySqlBaseDao()
      exec.executeInsertOrUpdate(s"update t_check set flag=1 , message='$startDate:运行成功' where name='${t_check_pre}t_dwd_picture'",null)
    } catch {
      case e: Exception => {
        e.printStackTrace()
        try{
          val exec = new MySqlBaseDao()
          exec.executeInsertOrUpdate(s"update t_check set flag=2, message='$startDate:${t_check_pre}t_dwd_picture 运行失败' where name='${t_check_pre}t_dwd_picture'",null)
        }catch {
          case e:Throwable=>{
            e.printStackTrace()
          }
        }
      }
    }
  }


  /**
   * 业务数据同步，点赞数据和收藏数据
   * @param spark SparkSession
   * @param sqlBase mysql数据库操作实例
   * @param day 计算日期
   */
  def pullContentIteract(spark:SparkSession,sqlBase:MySqlBaseDao,day:String): Unit ={
    try{
      val resultHiveTable="default.t_ods_content_interact"
      val myKudu=new MyKudu(spark)
      myKudu.select("t_logdata_thba_streaming").select("data", "billtype","timestamp","operation_state","env").where(
        s"""
          |day='$day'
          |and env <> 'test'
          |""".stripMargin).createOrReplaceTempView("t_biz_data_temp")
      val resultDF=spark.sql(
        s"""
           |select c0 as vid,if(c1=0,null,c1) as uid,c2 as content_id,nvl(c3,'') as pid,c4 as type_id,c5 as is_main, nvl(c6,1) as `action`,c7 as client,c8 as create_time,nvl(c9,'') as content,nvl(c10,0) as old_content_id,nvl(c11,0) as is_experience,c12 as topid,case when billType in ('content_praise') then 'ck_praise' when billType in ('content_favorite') then 'ck_favorite' when billType in ('content_subscription') then 'ck_subscription' when billType in ('content_share') then 'ck_share' when billType in ('content_comment') then 'ck_comment' end as eventid,'$day' as day  from
           |(select json_tuple(data, 'vid', 'uid', 'content_id','pid', 'type_id','isMain', 'action', 'client','createTime','content_name','old_content_id','is_experience','topid'),`timestamp`,billType
           |from t_biz_data_temp where billType in('content_praise','content_favorite','content_subscription','content_share','content_comment')) as t
           |""".stripMargin)
        .withColumn("uid",col("uid").cast("Int"))
        .withColumn("content_id",col("content_id").cast("Int"))
        .withColumn("type_id",col("type_id").cast("Int"))
        .withColumn("is_main",col("is_main").cast("Int"))
        .withColumn("action",col("action").cast("Int"))
      spark.sql(
        s"""
          |alter table $resultHiveTable drop if exists partition(day='$day')
          |""".stripMargin)
      resultDF.repartition(1).write.format("hive").mode("append").partitionBy("day").saveAsTable(resultHiveTable)
      //数据只有insert直接保存
      sqlBase.execute(s"update t_check set flag=1, message='$day:t_ods_content_interact 运行成功' where name='aliyun_t_ods_content_interact'")
    }catch {
      case e: Exception => {
        e.printStackTrace()
        try{
          val exec = new MySqlBaseDao()
          exec.execute(s"update t_check set flag=2, message='$day:t_ods_content_interact 运行失败' where name='aliyun_t_ods_content_interact'")
        }catch {
          case e:Throwable=>{
            e.printStackTrace()
          }
        }
      }
    }
  }
  /**
   * 同步用户关注数据，数据写入MySQL
   * @param spark SparkSession
   * @param sqlBase mysql数据库操作实例
   * @param day 计算日期
   * */
  def pullUserFocus(spark:SparkSession,sqlBase:MySqlBaseDao,day:String): Unit ={
    spark.sql(
      s"""
         |select data, billtype,timestamp,operation_state,env from t_ods_thba_logdata
         |where day='$day'
         |""".stripMargin).createOrReplaceTempView("t_biz_data_temp")
    try {
      val resultMysqlTable = "db_userportrait.t_bbs_user_focus"
      val resultDF = spark.sql(
        s"""
           |select cast(c0 as int) as fan_uid,cast(c1 as int) as uid,cast(c2 as int) as focus_state,`timestamp` as update_timestamp,from_unixtime(CAST (SUBSTR(`timestamp`,1,10) as bigint) ,'yyyyMMdd') as day,ROW_NUMBER() OVER(partition by c0,c1 order by `timestamp` desc) as rank from
           |(select json_tuple(data, 'fanUid', 'uid', 'focusState'),`timestamp`
           |from t_biz_data_temp where billType='bbs_user_focus') as t
           |""".stripMargin).filter("rank=1").drop("rank")
      sqlBase.executeMany2(
        s"""
           |REPLACE INTO $resultMysqlTable (fan_uid, uid, focus_state,update_timestamp,day) VALUES(?,?,?,?,?)
           |""".stripMargin, resultDF.collect())
      sqlBase.execute(s"update t_check set flag=1, message='$day:t_bbs_user_focus 运行成功' where name='aliyun_t_bbs_user_focus'")
    } catch {
      case e: Exception => {
        e.printStackTrace()
        try {
          val exec = new MySqlBaseDao()
          exec.execute(s"update t_check set flag=2, message='$day:t_bbs_user_focus 运行失败' where name='aliyun_t_bbs_user_focus'")
        } catch {
          case e: Throwable => {
            e.printStackTrace()
          }
        }
      }
    }
  }

  /**
   * 将400电话语音解析成文字内容，并且通过大模型解析子类车系和场景
   * */
  def saveProduct400Dwd(spark:SparkSession,sqlBase:MySqlBaseDao,day:String): Unit = {
    val resultTable="t_dwd_product_400"
    import spark.implicits._
    val brandInfoMap = mutable.Map[Int, (String, String)]()
    //获取品牌对应的车系数据和子类数据
    sqlBase.getDfTable(
      """
        |(select brandId,group_concat(name separator ",") as subcate_str  from (
        |select distinct brandId,concat_ws("_",subCategoryId,c.name) as name from
        |(select distinct brandId,seriesId,subCategoryId  from db_userportrait.t_product tp ) as a
        |left join db_userportrait.t_series as b on a.seriesId=b.id
        |left join db_userportrait.t_subcategory as c  on a.subCategoryId=c.id) as t group by brandId)t
        |""".stripMargin, spark).collect().foreach(x => {
      brandInfoMap += (x.getInt(0) -> (x.getString(1), ""))
    })
    sqlBase.getDfTable(
      """
        |(select brandId,group_concat(name separator ",") as series_str  from (
        |select distinct brandId,concat_ws("_",seriesId,b.name) as name from
        |(select distinct brandId,seriesId,subCategoryId  from db_userportrait.t_product tp ) as a
        |left join db_userportrait.t_series as b on a.seriesId=b.id
        |left join db_userportrait.t_subcategory as c  on a.subCategoryId=c.id) as t group by brandId)t
        |""".stripMargin, spark).collect().foreach(x => {
      brandInfoMap += (x.getInt(0) -> (brandInfoMap(x.getInt(0))._1, x.getString(1)))
    })
    //广播变量广播
    val brandInfoMapBC = spark.sparkContext.broadcast(brandInfoMap)
    val resultDF = sqlBase.getDfTable(
      s"""
        |(select id,tel,tape_url ,brand_id ,create_time ,day from db_userportrait.t_product_400 where day='$day')t
        |""".stripMargin, spark).repartition(15).as[product400].map(row => {
      val id = row.id
      val tel = row.tel
      val tape_url = row.tape_url
      val brandId = row.brand_id
      val create_time = row.create_time
      val voice2TextApi = reader.getString("voice2text")
      //通过接口获取通话记录
      println(voice2TextApi + tape_url)
      val result1 = MyHttpService.getHttpRequest2(voice2TextApi + tape_url)
      val content = JSON.parseObject(result1).getString("message")
      //获取品牌下售卖的车系和子类数据
      val seriesSubcate = brandInfoMapBC.value.getOrElse(brandId, ("",""))
      //最终拼接的input输入
      val input =
        s"""
           |售卖车系（车系id_车系名称）：${seriesSubcate._2}。售卖子类（子类id_子类名称）：${seriesSubcate._1}。通话记录：$content
           |""".stripMargin
      val workflow_id = "7472647524126539810"
      //请求coze获取解析结果
      val cozeApi = reader.getString("cozeFlowAPI")
      val param =
        s"""
           |{"params":{"input":"$input"},"workflow_id":"$workflow_id"}
           |""".stripMargin.replace("\r\n", "")
      val result2 = MyHttpService.GetHttpPost2(cozeApi, param)
      val output = JSON.parseObject(result2).getJSONObject("data").getString("output")
      (id, tel,tape_url, brandId, create_time, day, content,seriesSubcate._2,seriesSubcate._1,output)
      }).toDF("id","tel","tape_url","brand_id","create_time","day","content","series_str","subcate_str","output")
    spark.sql(s"ALTER TABLE $resultTable DROP IF EXISTS PARTITION(day='$day')")
    resultDF.write.mode(SaveMode.Append).format("hive").partitionBy("day").saveAsTable("t_dwd_product_400")
  }


  /**
   * 微信群聊或者私信消息大模型分析后
   * */
  def saveWechatDwd(spark: SparkSession, sqlBase: MySqlBaseDao, day: String): Unit = {
    val resultTable = "db_test.t_dwd_wechat_record"
    val sqlBase_Userportrait = new MySqlBaseDao("userportrait")
    import spark.implicits._
    //将userid转换成unionid,去除不能转换成unionid的数据
    val userid2unionidMap=sqlBase_Userportrait.getDfTable(
      """
        |(
        |select distinct user_id ,unionid  from davinci.wechat_group_member_list where (room_id not regexp '@' or unionid<>'')
        |union
        |select 'KaJiaXiaoGuanYuanDaBai' as user_id ,'KaJiaXiaoGuanYuanDaBai' as unionid
        |)t
        |""".stripMargin,spark).map(x=>(x.getAs[String]("user_id"),x.getAs[String]("unionid"))).collect().toMap
    //广播变量广播
    val userid2unionidMapBC = spark.sparkContext.broadcast(userid2unionidMap)
    sqlBase_Userportrait.getDfTable(
      s"""
         |(
         |select concat_ws('#',from_userid,to_userid,msg_content) as msg_content,create_time ,replace(REPLACE(REPLACE(concat(from_userid,to_userid),"KaJiaXiaoGuanYuanDaBai",""),"KaCheZhiJiaXiaoTeng",""),"ZhangYinZhi","") as room_id from davinci.wechat_room_messages
         |where date_format(msg_time,'%Y%m%d')>='$day' and room_id ='' and msg_type ='text'
         |union
         |select concat_ws('#',from_userid,room_id,msg_content) as msg_content,create_time ,room_id from davinci.wechat_room_messages
         |where date_format(msg_time,'%Y%m%d')>='$day' and room_id not regexp '@' and room_id<>'' and msg_type ='text'
         |)t
         |""".stripMargin, spark).createOrReplaceTempView("t1")
    val resultDF =spark.sql(
      """
        |select room_id,concat_ws(";",collect_list(msg_content)) as msg_content from t1 where room_id<>'' group by room_id
        |""".stripMargin)

//    val resultDF = sqlBase_Userportrait.getDfTable(
//      s"""
//         |(
//         |select room_id,group_concat(msg_content order by create_time asc separator ';') as msg_content from
//         |(select concat_ws('#',from_userid,to_userid,msg_content) as msg_content,create_time ,replace(REPLACE(REPLACE(concat(from_userid,to_userid),"KaJiaXiaoGuanYuanDaBai",""),"KaCheZhiJiaXiaoTeng",""),"ZhangYinZhi","") as room_id from davinci.wechat_room_messages
//         |where date_format(msg_time,'%Y%m%d')='$day' and room_id ='' and msg_type ='text'
//         |having room_id<>'') as t
//         |group by room_id
//         |union
//         |select room_id,group_concat(msg_content order by create_time asc separator ';') as msg_content from
//         |(select concat_ws('#',from_userid,room_id,msg_content) as msg_content,create_time ,room_id from davinci.wechat_room_messages
//         |where date_format(msg_time,'%Y%m%d')='$day' and room_id not regexp '@' and room_id<>'' and msg_type ='text') as t
//         |group by room_id
//         |)t
//         |""".stripMargin, spark)
      .repartition(10).as[WechatRecord]
//      .filter(_.room_id =="wrHO8sDQAAsd5kFYFdX2KAAfl6qmIvRQ")
      .map(row=>{
        val msg_content = row.msg_content
        val room_id=row.room_id
        //最终拼接的input输入
        val input =msg_content.replace("\"","")
        val workflow_id = "7488958215154696219"
        //请求coze获取解析结果
        val cozeApi = reader.getString("cozeFlowAPI")
        val param =
          s"""
             |{"params":{"input":"$input"},"workflow_id":"$workflow_id"}
             |""".stripMargin.replace("\r\n", "")
            .replace("\n", "")
        println(param)
        val result = MyHttpService.GetHttpPost2(cozeApi, param)
        val code = JSON.parseObject(result).getInteger("code")
        var output="[]"
        if(code == 1){
          output= JSON.parseObject(result).getJSONObject("data").getString("output")
        }
//        val output = result
        (room_id,msg_content.replace(";","\n"), output,day)
    }).toDF("room_id","msg_content","output","day")
    spark.sql(s"ALTER TABLE $resultTable DROP IF EXISTS PARTITION(day='$day')")
    resultDF.write.mode(SaveMode.Append).format("hive").partitionBy("day").saveAsTable(resultTable)
  }

  /**
   * 文章新增商配字段，历史数据处理
   */
  def test(): Unit ={
    val spark=MySparkSession.conn()
    val sqlBase=new MySqlBaseDao("che_test")
    sqlBase.getCacheTable("t_dwd_video_temp1031","t_dwd_video_temp1031",spark)
    val myKudu=new MyKudu(spark)
//    myKudu.select("t_dwd_video_streaming").createOrReplaceTempView("t_dwd_video_kudu")
    val df=spark.sql(
      """
        |select * from t_dwd_video
        |""".stripMargin)
    myKudu.upsert(df,"t_dwd_video_streaming")
  }

  /**
   * 微信群聊或者私信消息大模型分析后
   * */
  def test2(spark: SparkSession, sqlBase: MySqlBaseDao, day: String): Unit = {
    val resultTable = "db_test.t_dwd_wechat_record"
    val sqlBase_Userportrait = new MySqlBaseDao("userportrait")
    import spark.implicits._
    //将userid转换成unionid,去除不能转换成unionid的数据
    val userid2unionidMap = sqlBase_Userportrait.getDfTable(
      """
        |(
        |select distinct user_id ,unionid  from davinci.wechat_group_member_list where (room_id not regexp '@' or unionid<>'')
        |union
        |select 'KaJiaXiaoGuanYuanDaBai' as user_id ,'KaJiaXiaoGuanYuanDaBai' as unionid
        |)t
        |""".stripMargin, spark).map(x => (x.getAs[String]("user_id"), x.getAs[String]("unionid"))).collect().toMap
    //广播变量广播
    val userid2unionidMapBC = spark.sparkContext.broadcast(userid2unionidMap)
    sqlBase_Userportrait.getDfTable(
      s"""
         |(
         |select concat_ws('#',from_userid,to_userid,msg_content) as msg_content,date_format(msg_time,'%Y%m%d') as msg_time ,replace(REPLACE(REPLACE(concat(from_userid,to_userid),"KaJiaXiaoGuanYuanDaBai",""),"KaCheZhiJiaXiaoTeng",""),"ZhangYinZhi","") as room_id from davinci.wechat_room_messages
         |where date_format(msg_time,'%Y%m%d')>='$day' and room_id ='' and msg_type ='text'
         |union
         |select concat_ws('#',from_userid,room_id,msg_content) as msg_content,date_format(msg_time,'%Y%m%d') as msg_time ,room_id from davinci.wechat_room_messages
         |where date_format(msg_time,'%Y%m%d')>='$day' and room_id not regexp '@' and room_id<>'' and msg_type ='text'
         |)t
         |""".stripMargin, spark).createOrReplaceTempView("t1")
    val resultDF = spark.sql(
        """
          |select room_id,msg_time as day,concat_ws(";",collect_list(msg_content)) as msg_content from t1 where room_id<>''
          |group by room_id,msg_time
          |""".stripMargin)
      .repartition(10).as[WechatRecordTest]
      //      .filter(_.room_id =="wrHO8sDQAAsd5kFYFdX2KAAfl6qmIvRQ")
      .map(row => {
        val msg_content = row.msg_content
        val room_id = row.room_id
        val day = row.day
        val output = ""
        (room_id, msg_content.replace(";", "\n"), output, day)
      }).toDF("room_id", "msg_content", "output", "day")
    sqlBase.saveMysql(resultDF, "db_hitsstatistic_test.t_dwd_wechat_record_test","overwrite")
//    spark.sql(s"ALTER TABLE $resultTable DROP IF EXISTS PARTITION(day='$day')")
//    resultDF.write.mode(SaveMode.Append).format("hive").partitionBy("day").saveAsTable(resultTable)
  }

}
