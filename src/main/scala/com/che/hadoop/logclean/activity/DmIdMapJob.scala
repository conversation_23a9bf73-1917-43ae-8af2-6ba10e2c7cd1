package com.che.hadoop.logclean.activity

import com.che.hadoop.logclean.bo.DmIdMapNew
import com.che.hadoop.underlay.dao.MySparkSession
import com.che.hadoop.underlay.tool.date.DateTranslate

/**
 * dmid 从kudu同步到hive表中保存快照
 */
object DmIdMapJob {
  def main(args: Array[String]): Unit = {
    var startDay,endDay=DateTranslate.getDate(1)
    var dbEnv="default."
    if(args.length>=1){
      dbEnv=args(0)
      if(args.length==2){
        startDay=args(1)
        endDay=startDay
      }
      if(args.length==3){
        startDay=args(1)
        endDay=args(2)
      }
    }
    val spark = MySparkSession.conn()
    //dmid数据处理，从kudu数据拉取到hive
    DateTranslate.getDatesBetween(startDay,endDay).foreach(day=>{
      println(s"计算日期：$day")
      DmIdMapNew.dmId2Hive(spark,day)
    })
    spark.stop()
  }
}
