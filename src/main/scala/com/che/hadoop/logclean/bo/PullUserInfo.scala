package com.che.hadoop.logclean.bo

import com.che.hadoop.underlay.tool.http.MyHttpService
import java.util
import java.util.{Date, ResourceBundle}
import com.che.hadoop.logclean.utils.{Common, DingRobot, JsonQuery, StrEcode, UrlDecodeUtil}
import com.che.hadoop.underlay.dao.{MySparkSession,MySqlBaseDao}
import org.apache.spark.sql.types.{StringType, StructField, StructType}
import org.apache.spark.sql.{DataFrame, Row, SparkSession}
import scala.collection.mutable.ListBuffer
import scala.collection.JavaConversions.asScalaBuffer

/**
  *
  * @ author dianwei
  * @ date 2020-08-26 15:12
  * 拉取认证数据
  * 相关数据由于gsa废弃，数据没有应用 （20231203）
  */
object PullUserInfo {
    def main(args: Array[String]): Unit = {
        val spark=MySparkSession.conn()
        val sqlBase=new MySqlBaseDao()
        getVerifyCar(spark,sqlBase,"20230418")
    }
    /**
      * 身份认证 和 驾照认证是在一起的
      * 车辆认证 待测
      *
      * @param day
      */
    def start(spark:SparkSession,sqlBase:MySqlBaseDao,day: String): Unit = {
        val t_check_pre="aliyun_"
        try {
            val environment = "default."
            getVerifyCar(spark,sqlBase,day)
            pullIdCardVerify(spark,day, environment)
            pullLicenseVerify(spark,day, environment)
            pullRegisterUser(spark,day, environment)
            val exec = new MySqlBaseDao()
            exec.executeInsertOrUpdate(s"update t_check set flag=1 , message='$day:运行成功' where name='${t_check_pre}t_ods_car_verify'",null)
        } catch {
            case e: Exception => {
                e.printStackTrace()
                try{
                    val exec = new MySqlBaseDao()

                    exec.executeInsertOrUpdate(s"update t_check set flag=2, message='$day:${t_check_pre}t_ods_car_verify 运行失败' where name='${t_check_pre}t_ods_car_verify'",null)
                }catch {
                    case e:Throwable=>{
                        e.printStackTrace()
                    }
                }
            }
        }
    }

    /**
      * 注册用户
      *
      * @param day 日期
      */
    def pullRegisterUser(spark:SparkSession,day: String, environment: String): Unit = {
        val t_check_pre="aliyun_"
        var url: String = ""
        val reader=ResourceBundle.getBundle("connection")
        if (environment == "default.") {
            url = reader.getString("api.bbs.regin")
        } else {
            url = reader.getString("api.bbs.regin.test")
        }
        println(MyHttpService.getHttpRequest2(url))
        val result = new JsonQuery(StrEcode.unicodeStr2String(MyHttpService.getHttpRequest2(url)))
        println(MyHttpService.getHttpRequest2(url))
        var userList: util.List[String] = result.m("data").getL
        var listBuffer = new ListBuffer[String]
        for (a <- userList) {
            listBuffer.append(a)
        }
        var uid = ""
        var nickname = ""
        var sjyz = ""
        var unionId = ""
        var status = ""
        var gender = ""
        var bDay = ""
        var level = ""
        var title = ""
        var regDate = ""
        var source = ""
        var timestamp = ""
        val structType = StructType(Array(
            StructField("uid", StringType, true),
            StructField("nickname", StringType, true),
            StructField("sjyz", StringType, true),
            StructField("unionId", StringType, true),
            StructField("status", StringType, true),
            StructField("gender", StringType, true),
            StructField("bDay", StringType, true),
            StructField("level", StringType, true),
            StructField("title", StringType, true),
            StructField("regDate", StringType, true),
            StructField("source", StringType, true),
            StructField("timestamp", StringType, true),
            StructField("operationState", StringType, true),
            StructField("day", StringType, true)
        ))

        val rdd = spark.sparkContext.parallelize(listBuffer).map(
            (u: String) => {
                val user = new JsonQuery(u)
                uid = user.ms("uid")
                nickname = user.ms("nickname")
                sjyz = user.ms("sjyz")
                unionId = user.ms("unionId")
                status = user.ms("status")
                gender = user.ms("gender")
                bDay = user.ms("bDay")
                level = user.ms("level")
                title = user.ms("title")
                regDate = user.ms("regDate")
                source = user.ms("source")
                timestamp = new Date().getTime.toString.substring(0, 10)
                Row(uid, nickname, sjyz, unionId, status, gender, bDay, level, title, regDate, source, timestamp, "insert", day)
            }
        )
//        spark.sql(s"alter table ${environment}t_ods_reg_user drop if exists partition(day='" + day + "')")



        val data: DataFrame = spark.sql(
            s"""
               |select json_tuple(data, 'uid','nickname','sjyz','unionId','status','gender','bDay','level','title','regDate','source'),
               | `timestamp`, operation_state, from_unixtime(substr(`timestamp`, 0, 10), 'yyyyMMdd')
               |from t_biz_data_temp
               |where billtype = 'register_user'
               """.stripMargin)
            .toDF("uid", "nickname", "sjyz", "unionId", "status", "gender", "bDay", "level", "title", "regDate", "source",
                "timestamp", "operationState", "day")
        data.createOrReplaceTempView("t_update")
        val data2: DataFrame = spark.sql(
            s"""
               |select nvl(a.uid,b.uid),
               |       nvl(a.nickname,b.nickname)  ,
               |      nvl(a.sjyz,b.sjyz) ,
               |    nvl(a.unionId,b.unionId),
               |    nvl(a.status,b.status) ,
               |    nvl(a.gender,b.gender),
               |    nvl(a.bDay,a.bDay ),
               |    nvl(a.level,b.user_level ),
               |    nvl(a.title,b.user_level_title ),
               |    nvl(a.regDate,from_unixtime(cast(b.regdate as int))),
               |    nvl(a.source, 0 ),
               |    nvl(a.`timestamp`,a.`timestamp`) ,
               |    nvl(a.operationState,a.operationState) ,
               |    nvl(a.day,a.day )
               |
               |from t_update a
               |left join t_user b
               |on a.uid = b.uid
               |where b.uid is not null
             """.stripMargin)
            .toDF("uid", "nickname", "sjyz", "unionId", "status", "gender", "bDay", "level", "title", "regDate", "source",
                "timestamp", "operationState", "day")
        spark.createDataFrame(rdd, structType).show(100)
        spark.createDataFrame(rdd, structType).union(data2).repartition(1).write.mode("append").format("Hive").saveAsTable(environment + "t_ods_reg_user")
    }




    /**
      * 身份认证
      *
      * @param day 日期
      */
    def pullIdCardVerify(spark:SparkSession,day: String, environment: String): Unit = {
        val t_check_pre="aliyun_"
        spark.sql(s"alter table ${environment}t_ods_id_verify drop if exists partition(day='" + day + "')")

        val data: DataFrame = spark.sql(
            s"""
               |select json_tuple(data, 'uid', 'idcard', 'name','sex','nation','birth','address','identify','verifyType','createTime'),
               | `timestamp`, operation_state, from_unixtime(substr(`timestamp`, 0, 10), 'yyyyMMdd')
               |from t_biz_data_temp
               |where billtype = 'idcard_verify'
             """.stripMargin)
            .toDF("uid", "idcard", "name", "sex", "nation", "birth", "address", "identity", "verifyType", "createTime",
                "timestamp", "operationState", "day").filter("identity is not null")
        data.repartition(1).write.mode("append").partitionBy("day").format("Hive").saveAsTable(environment + "t_ods_id_verify")

    }

    /**
      * 车辆认证 业务数据切换，取消
      *
      * @param day 日期
      */
    def pullCarVerifyOld(spark:SparkSession,day: String, environment: String): Unit = {
        val t_check_pre="aliyun_"
        spark.sql(s"alter table ${environment}t_ods_car_verify drop if exists partition(day='" + day + "')")
        val data: DataFrame = spark.sql(
            s"""
               |select json_tuple(data, 'uid','plateNumber', 'brandId', 'brandName','seriesId','seriesName','subCategoryId','truckName','truckId','verifyType','createTime'),
               | `timestamp`, operation_state, from_unixtime(substr(`timestamp`, 0, 10), 'yyyyMMdd')
               |from t_biz_data_temp
               |where billtype = 'car_verify'
             """.stripMargin)
            .toDF("uid", "plateNumber", "brandId", "brandName", "seriesId", "seriesName", "subCategoryId", "truckName", "truckId", "verifyType", "createTime",
                "timestamp", "operationState", "day")
        data.repartition(1).write.mode("append").partitionBy("day").format("Hive").saveAsTable(environment + "t_ods_car_verify")

    }

    /**
      * 驾照认证
      * @param day 日期
      */
    def pullLicenseVerify(spark:SparkSession,day: String, environment: String): Unit = {
        val t_check_pre="aliyun_"
        spark.sql(s"alter table ${environment}t_ods_license_verify drop if exists partition(day='" + day + "')")
        val data: DataFrame = spark.sql(
            s"""
               |select json_tuple(data, 'uid', 'plateNumber', 'carType','owner','address','useCharacter','carBrand','idCode','registerDate','issueDate','stamp','licenseLevel','verifyType','createTime'),
               | `timestamp`, operation_state, from_unixtime(substr(`timestamp`, 0, 10), 'yyyyMMdd')
               |from t_biz_data_temp
               |where billtype = 'license_verify'
             """.stripMargin)
            .toDF("uid", "plateNumber", "carType", "owner", "address", "useCharacter", "carBrand", "idCode", "registerDate", "issueDate", "stamp", "licenseLevel", "verifyType", "createTime",
                "timestamp", "operationState", "day").filter("verifyType is not null")
        data.repartition(1).write.mode("append").partitionBy("day").format("Hive").saveAsTable(environment + "t_ods_license_verify")

    }

    /**
     * 业务数据同步，用户认证车辆
     * @param spark SparkSession
     * @param sqlBase mysql数据库操作实例
     * @param day 计算日期
     */
    def getVerifyCar(spark:SparkSession,sqlBase:MySqlBaseDao,day:String): Unit ={
        try{
            val resultMysqlTable="db_userportrait.t_verify_car"
            val resultDF=spark.sql(
                s"""
                   |select c0 as id,c1 as uid,c2 as plate_number,c3 as engine_number,c4 as vin,c5 as product_id,c6 as product_name,c7 as verify_type,c8 as create_time,`timestamp` as update_timestamp,ROW_NUMBER() OVER(partition by c0 order by `timestamp` desc) as rank from
                   |(select json_tuple(data, 'id', 'uid', 'plateNumber','engineNumber', 'vin','productId', 'productName','verifyType', 'createTime'),`timestamp`
                   |from t_biz_data_temp where billType='car_verify') as t
                   |""".stripMargin).filter("rank=1").drop("rank")
            //保存数据
            val args=resultDF.collect().map(row=>List(row.getAs[Int]("id") ,row.getAs[String]("uid"),row.getAs[String]("product_id"),row.getAs[String]("product_name"),row.getAs[String]("engine_number"),row.getAs[String]("vin"),row.getAs[Int]("plate_number"),row.getAs[String]("verify_type"),row.getAs[String]("create_time"),row.getAs[String]("update_timestamp"))).toList
            sqlBase.executeMany(
                s"""
                   |REPLACE INTO $resultMysqlTable (id, uid, product_id, product_name, engine_number, vin, plate_number, verify_type, create_time, create_timestamp) VALUES(?,?,?,?,?,?,?,?,?,?)
                   |""".stripMargin,args)
            sqlBase.execute(s"update t_check set flag=1, message='$day:t_verify_car 运行成功' where name='aliyun_t_verify_car'")
        }catch {
            case e: Exception => {
                e.printStackTrace()
                try{
                    val exec = new MySqlBaseDao()
                    exec.execute(s"update t_check set flag=2, message='$day:t_verify_car 运行失败' where name='aliyun_t_verify_car'")
                }catch {
                    case e:Throwable=>{
                        e.printStackTrace()
                    }
                }
            }
        }
    }


    def pullHistoryVerify(spark:SparkSession,day: String, environment: String): Unit = {
        val sqlBase = new MySqlBaseDao()

        sqlBase.getCacheTable("t_user_info", "(select * from db_hitsstatistic_test.cdb_verify_car ) as t",spark)
        //        spark.sql(s"alter table ${environment}t_ods_car_verify drop if exists partition(day='" + day + "')")

        val mouthData: DataFrame = spark.sql(
            s"""
               |select uid,plateNumber, '', brand,'','','','其他','',CONCAT_WS("-",verifytype,verified),from_unixtime(submittime,'yyyyMMdd'),
               |submittime,'insert',from_unixtime(operatetime,'yyyyMMdd')
               |from t_user_info
             """.stripMargin)
            .toDF("uid", "plateNumber", "brandId", "brandName", "seriesId", "seriesName", "subCategoryId", "truckName", "truckId", "verifyType", "createTime",
                "timestamp", "operationState", "day")
        mouthData.repartition(1).write.mode("append").partitionBy("day").format("Hive").saveAsTable(environment + "t_ods_car_verify")
    }

    def pullHistoryVerify2(spark:SparkSession,day: String, environment: String): Unit = {
        val sqlBase = new MySqlBaseDao()

        sqlBase.getCacheTable("t_user_info", "(select * from db_hitsstatistic_test.cdb_verify_idcard ) as t",spark)
        //        spark.sql(s"alter table ${environment}t_ods_id_verify drop if exists partition(day='" + day + "')")
        val mouthData: DataFrame = spark.sql(
            s"""
               |select uid,idcardnum, name, gender,'','','',
               |
               |case when identity =='司机' then 1
               |     when identity =='经销商' then 2
               |     when identity =='厂商' then 3
               |     when identity =='服务商' then 4
               |     when identity =='其他' then 5
               |     end as identity
               |
               |,CONCAT_WS("-",verifytype,verified),from_unixtime(submittime,'yyyyMMdd'),
               |submittime,'insert',from_unixtime(operatetime,'yyyyMMdd')
               |from t_user_info
             """.stripMargin)
            .toDF("uid", "idcard", "name", "sex", "nation", "birth", "address", "identity", "verifyType", "createTime",
                "timestamp", "operationState", "day")
        mouthData.repartition(1).write.mode("append").partitionBy("day").format("Hive").saveAsTable(environment + "t_ods_id_verify")
    }


    def pullHistoryVerify3(spark:SparkSession,day: String, environment: String): Unit = {
        val sqlBase = new MySqlBaseDao()

        sqlBase.getCacheTable("t_user_info", "(select * from db_hitsstatistic_test.cdb_user ) as t",spark)
        //        spark.sql(s"alter table ${environment}t_ods_license_verify drop if exists partition(day='" + day + "')")
        val mouthData: DataFrame = spark.sql(
            s"""
               |select uid,username,phone,openid,0,0,'',1,'',regDate,platform, unix_timestamp(regDate, "yyyy-MM-dd HH:mm:ss") ,'insert',date_format(regDate,'yyyyMMdd')
               |from t_user_info
             """.stripMargin)
            .toDF("uid", "nickname", "sjyz", "unionId", "status", "gender", "bDay", "level", "title", "regDate", "source",
                "timestamp", "operationState", "day")
        mouthData.repartition(1).write.mode("append").format("Hive").saveAsTable(environment + "t_ods_reg_user")
    }
}
