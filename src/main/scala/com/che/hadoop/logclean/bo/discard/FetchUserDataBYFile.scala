package com.che.hadoop.logclean.bo.discard
/**
 * 通过读文件方式处理bbs用户数据写入hive，处理历史数据
 *@auther:l<PERSON><PERSON><PERSON><PERSON>an
 * */
import com.che.hadoop.underlay.dao
import org.apache.spark.sql.types.{StructField, _}
import com.che.hadoop.logclean.utils.JsonQuery
import org.apache.spark.sql.Row
import scala.collection.JavaConversions._
import scala.collection.mutable.ListBuffer
import com.alibaba.fastjson.{JSON, JSONException, JSONObject}

//通过读文件方式处理bbs用户数据
object FetchUserDataBYFile {
  //定义structType
  val structType = StructType(Array(
    StructField("uid", IntegerType, true),
    Struct<PERSON>ield("nickname", StringType, true),
    StructField("sjyz", StringType, true),
    Struct<PERSON>ield("unionid", StringType, true),
    Struct<PERSON>ield("status", IntegerType, true),
    <PERSON>ruct<PERSON>ield("user_level", IntegerType, true),
    StructField("user_level_title", StringType, true),
    StructField("bday", StringType, true),
    StructField("gender", IntegerType, true),
    StructField("regdate", StringType, true),
    StructField("is_verify_idcard", IntegerType, true),
    StructField("idcardnum", StringType, true),
    StructField("name", StringType, true),
    StructField("verify_idcard_dateline", StringType, true),
    StructField("is_verify_car", IntegerType, true),
    StructField("platenumber", StringType, true),
    StructField("brand", StringType, true),
    StructField("verify_car_dateline", StringType, true),
    StructField("medals", ArrayType(MapType(StringType, StringType)), true),
    StructField("subforum", ArrayType(MapType(StringType, StringType)), true),
    StructField("idcard", MapType(StringType, StringType), true),
    StructField("driver_license", MapType(StringType, StringType), true),
    StructField("travel_card", MapType(StringType, StringType), true),
    StructField("other_info", MapType(StringType, StringType), true)
  ))

  def saveHive(dataFile: String): Unit = {
//    val dirPath = "hdfs://mycluster/" + dataFile
    val dirPath = "D:\\" + dataFile
    val userDF = dao.MySparkSession.conn().read.text(dirPath)
    val rowRdd = userDF.rdd.map(line => {
      val jsonData = JSON.parseArray(line.toString())
      val uidInfo = JSON.parseObject(jsonData.get(0).toString())
      val uid = uidInfo.getInteger("uid")
      val nickname = uidInfo.getString("nickname")
      val sjyz = uidInfo.getString("sjyz")
      val unionid = uidInfo.getString("unionid")
      val status = uidInfo.getInteger("status")
      val userLevel = uidInfo.getInteger("user_level")
      val userLevelTtitle = uidInfo.getString("user_level_title")
      val bday = uidInfo.getString("bday")
      val gender = uidInfo.getInteger("gender")
      val regdate = uidInfo.getString("regdate")
      val isVerifyIdcard = uidInfo.getInteger("is_verify_idcard")
      val idcardNum = uidInfo.getString("idcardnum")
      val name = uidInfo.getString("name")
      val verifyIdcardDateline = uidInfo.getString("verify_idcard_dateline")
      val isVerifyCar = uidInfo.getInteger("is_verify_car")
      val platenumber = uidInfo.getString("platenumber")
      val brand = uidInfo.getString("brand")
      val verifyCarDateline = uidInfo.getString("verify_car_dateline")
      var medals = new ListBuffer[scala.collection.mutable.Map[String, String]]
      var subforum = new ListBuffer[scala.collection.mutable.Map[String, String]]
      var idcard = scala.collection.mutable.Map[String, String]()
      var driverLicense = scala.collection.mutable.Map[String, String]()
      var travelCard = scala.collection.mutable.Map[String, String]()
      var otherInfo = scala.collection.mutable.Map[String, String]()
      for (i <- uidInfo.getJSONArray("medals")) {
        val tJsonMap = JSON.parseObject(i.toString)
        var tMap = scala.collection.mutable.Map[String, String]()
        if (!tJsonMap.isEmpty()) {
          tJsonMap.keys.foreach { i =>
            tMap.put(i, tJsonMap.get(i).toString())
          }
          medals.append(tMap)
        }
      }
      for (i <- uidInfo.getJSONArray("subforum")) {
        val tJsonMap = JSON.parseObject(i.toString)
        var tMap = scala.collection.mutable.Map[String, String]()
        if (!tJsonMap.isEmpty()) {
          tJsonMap.keys.foreach { i =>
            tMap.put(i, tJsonMap.get(i).toString())
          }
          subforum.append(tMap)
        }
      }
      val idcardJsonObject = uidInfo.getJSONObject("idcard")
      if (!idcardJsonObject.isEmpty()) {
        idcardJsonObject.keys.foreach { i =>
          idcard.put(i, idcardJsonObject.get(i).toString())
        }
      }
      val driverLicenseJsonObject = uidInfo.getJSONObject("driver_license")
      if (!driverLicenseJsonObject.isEmpty()) {
        driverLicenseJsonObject.keys.foreach { i =>
          idcard.put(i, driverLicenseJsonObject.get(i).toString())
        }
      }
      val travelCardJsonObject = uidInfo.getJSONObject("travel_card")
      if (!travelCardJsonObject.isEmpty()) {
        travelCardJsonObject.keys.foreach { i =>
          idcard.put(i, travelCardJsonObject.get(i).toString())
        }
      }
      val otherInfoJsonObject = uidInfo.getJSONObject("other_info")
      if (!otherInfoJsonObject.isEmpty()) {
        otherInfoJsonObject.keys.foreach { i =>
          idcard.put(i, otherInfoJsonObject.get(i).toString())
        }
      }
      Row(uid, nickname, sjyz, unionid, status, userLevel, userLevelTtitle, bday, gender,regdate, isVerifyIdcard, idcardNum, name, verifyIdcardDateline, isVerifyCar, platenumber, brand, verifyCarDateline, medals, subforum, idcard, driverLicense, travelCard, otherInfo)
    })
    //更新部分数据到default.t_user
//    dao.MySparkSession.conn.sqlContext.createDataFrame(rowRdd, structType).createOrReplaceTempView("t_user_view")
//    val saveDF=dao.MySparkSession.conn.sql("select a.* from t_user_view a left join default.t_user b on a.uid=b.uid having b.uid is null")
//    saveDF.write.mode("append").format("Hive").saveAsTable("default.t_user")
    //追加操作db_test.t_user
//    dao.MySparkSession.conn.sqlContext.createDataFrame(rowRdd, structType).write.mode("append").format("Hive").saveAsTable("db_test.t_user_temp")
    //追加操作default.t_user
    dao.MySparkSession.conn().sqlContext.createDataFrame(rowRdd, structType).repartition(1).write.mode("append").format("Hive").saveAsTable("default.t_user")
  }
  def gender():Unit={
    val sch = StructType(Array(
      StructField("uid", IntegerType),
      StructField("gender", IntegerType)
    )
    )
    val dirPath = "hdfs://mycluster/360chetest/360che/gender.csv"
    val spark=dao.MySparkSession.conn()
    spark.read.schema(sch).format("csv").option("header","true").load(dirPath).createOrReplaceTempView("gender")
    spark.sql("select a.*,b.gender from db_test.t_user a left join gender b on a.uid=b.uid").write.mode("append").format("Hive").saveAsTable("default.t_user")
//    spark.sql("select * from t_user a left join gender b on a.uid=b.uid where a.uid<=10").show()
//    spark.sql("select max(uid) from gender where uid<210000").show()
//    println(spark.sql("select 1 from gender where uid>2000000").count())
  }
  def test():Unit={
    val spark=dao.MySparkSession.conn()
//    val num =spark.sql("select * from default.t_user where uid>=2069047 and uid<2072200").count()
//    println(num)
//    spark.sql("insert into db_test.t_user_temp select * from default.t_user")
  }
  def main(args: Array[String]): Unit = {
    saveHive("user_data.json")
//    gender()
//    test()
  }
}
