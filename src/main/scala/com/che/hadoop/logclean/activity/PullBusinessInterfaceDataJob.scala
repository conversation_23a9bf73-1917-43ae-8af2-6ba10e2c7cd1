package com.che.hadoop.logclean.activity

import com.che.hadoop.logclean.bo._
import com.che.hadoop.underlay.dao.{MySparkSession, MySqlBaseDao}
import com.che.hadoop.underlay.tool.date.DateTranslate

/**
 * 业务数据采集，从外部接口采集的数据
 * 接口
 * 十大热帖排名 t_ods_bbs_post_rank
 * 文章 t_dwd_article，t_dws_article
 * 视频 t_dwd_video
 * 直播 t_dwd_live
 * 图集 t_dwd_picture
 * 经销商关联车型信息 t_ods_dealer_product （从sqlserver读取）
 */
object PullBusinessInterfaceDataJob {
  def main(args: Array[String]): Unit = {
    var startDay,endDay=DateTranslate.getDate(1)
    var dbEnv="default."
    if(args.length>=1){
      dbEnv=args(0)
      if(args.length==2){
        startDay=args(1)
        endDay=startDay
      }
      if(args.length==3){
        startDay=args(1)
        endDay=args(2)
      }
    }
    val spark = MySparkSession.conn()
    val sqlBase=new MySqlBaseDao()
    /*资讯数据-通过接口拉取*/
    DateTranslate.getDatesBetween(startDay,endDay).foreach(day=>{
      println(s"计算日期：$day")
      val endDate =DateTranslate.getAddDate(1,day)
      println(day,endDate)
      /*资讯数据-通过接口拉取*/
      PullBusinessData.pullArticleToDWD(spark,day,endDate)
      PullBusinessData.pullVideoToDWD(spark,day,endDate)
      PullBusinessData.saveArticleToDWS(spark,day)
      /*直播-通过接口拉取*/
      PullBusinessData.pullLiveToDWD(spark,day,endDate)
      /*图集-通过接口拉取*/
      PullBusinessData.pullPictureToDWD(spark,day,endDate)
      /*拉取十大热帖数据-接口*/
      PullBBSPost.tenPosts(spark,day)
      //附近商家接口地址
      //*todo nearbyShops没有按天分区，重复执行数据会重复
      FetchWebData.nearbyShops(spark,day, "t_nearby","uid", "shopId", "shopName", "shopCreateTime", "isDelete")
      //经销商和二手车
      //todo 经销商数据由于公用一个mysql偏移量表,cdh集群暂时不使用mysql
      FetchWebData.dealer(spark,dbEnv,day)
      FetchWebData.secondHandTruck(spark,dbEnv,day)
      FetchWebData.secondHandPostTruck(spark,dbEnv,day)
      //拉取关键字别名数据
      FetchWebData.getKeyWordAlias(spark,sqlBase,day)
      //经销商用户数据
      PullInquiryClues.getDealerVerify(sqlBase,day)
      //拉取用户信息写到 t_user
      FetchUserDataAlibaba.dealDailyUid(spark,day)
      //拉取司机招聘用户注册数据
      PullDriverJobData.getDriverRegisterData(spark,day)
      //拉取付费经销商近三个月发布热销车型数据
      PullInquiryClues.getDealerProvinceHotProduct(sqlBase,day)
    })
    spark.stop()
  }
}
