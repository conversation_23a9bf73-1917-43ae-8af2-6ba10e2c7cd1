package com.che.hadoop.logclean.activity

import com.che.hadoop.logclean.bo.LogCleanSession
import com.che.hadoop.underlay.dao.MySparkSession
import com.che.hadoop.underlay.tool.date.DateTranslate

/**
 * appsc数据，切割session 计算停留时长
 */
object LogCleanSessionJob {
  def main(args: Array[String]): Unit = {
    var startDay,endDay=DateTranslate.getDate(1)
    var dbEnv="default."
    if(args.length>=1){
      dbEnv=args(0)
      if(args.length==2){
        startDay=args(1)
        endDay=startDay
      }
      if(args.length==3){
        startDay=args(1)
        endDay=args(2)
      }
    }
    val spark = MySparkSession.conn()
    DateTranslate.getDatesBetween(startDay,endDay).foreach(day=>{
      println(s"计算日期：$day")
      LogCleanSession.session(spark,day)
    })
    spark.stop()
  }

}
