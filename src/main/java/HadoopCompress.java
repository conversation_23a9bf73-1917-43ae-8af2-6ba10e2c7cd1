import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.LocatedFileStatus;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.fs.RemoteIterator;
import org.apache.hadoop.io.IOUtils;
import org.apache.hadoop.io.compress.*;
import org.apache.hadoop.util.ReflectionUtils;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URI;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * 以gz格式压缩flume采集日志文件
 * 解压gz格式压缩日志文件
 *
 */
public class HadoopCompress {

    public static void main(String[] args) throws IOException, ParseException, InterruptedException {

        // 获取执行参数
        ResourceBundle conf = ResourceBundle.getBundle("compress");
        String startDate = conf.getString("date.start");
        String endDate = conf.getString("date.end");
        String sourcePath = conf.getString("path.source");
        String destPath = conf.getString("path.destination");
        // 定义程序执行日志文件
        String filename = "compress.log";
        // 获取需要压缩的所有日期
        List<String> dates = getTimesBetweenDates(startDate, endDate);

        for(int i = 0; i < dates.size(); i++){

            System.out.println(dates.get(i) + "开始压缩.");
            LogUtil.writeLogToFile(filename, dates.get(i) + "开始压缩.");
            Long start = System.currentTimeMillis();

            // 生成输入输出路径
            String inputPath = sourcePath + dates.get(i);
            String outPath = destPath + dates.get(i) + ".gz";

            // 开始压缩
            fileCompress(inputPath, outPath);
//            fileCompress("hdfs://nameservice1/pv-nginx/201904/", "hdfs://nameservice1/cold_data/pv-nginx/201904.gz");
            // 计算压缩时间
            Long tookTime = (System.currentTimeMillis() - start)/60000;
            System.out.println(dates.get(i) + "压缩完成. 耗时: " + tookTime + "分钟");
            LogUtil.writeLogToFile(filename, dates.get(i) + "压缩完成. 耗时: " + tookTime + "分钟");

            Thread.sleep(1000);

        }
    }

    /**
     * 压缩
     * @param uri 原始文件路径
     * @param outputPath 文件压缩后的保存路径
     * @throws IOException
     */
    public static void fileCompress(String uri, String outputPath) throws IOException {

        // 定义Hadoop配置
        Configuration conf = new Configuration();
        FileSystem fileSystem = FileSystem.get(URI.create(uri), conf);
        RemoteIterator<LocatedFileStatus> fileIter = fileSystem.listFiles(new Path(uri), false);

        // 定义压缩格式并初始化流
        CompressionCodecFactory ccf = new CompressionCodecFactory(conf);
        CompressionCodec codec = ccf.getCodecByClassName(GzipCodec.class.getName());
        OutputStream compressionOutputStream = codec.createOutputStream(fileSystem.create(new Path(outputPath)));

        // 定义压缩格式并初始化流(此写法在线上报空指针异常)
        //GzipCodec gzipCodec = new GzipCodec();
        //CompressionOutputStream compressionOutputStream = gzipCodec.createOutputStream(fileSystem.create(new Path(outputPath)));

        //循环一天的日志文件将其合并到一个流进行压缩
        InputStream inputStream = null;
        while (fileIter.hasNext()) {

            // 获取文件输入流
            inputStream = fileSystem.open(fileIter.next().getPath());
            // 复制到文件压缩输出流
            IOUtils.copyBytes(inputStream, compressionOutputStream, 4096, false);
            inputStream.close();

        }

        compressionOutputStream.close();

    }

    /**
     * 解压缩
     * @param uri 原始文件路径
     * @param outputPath 文件解压缩后的保存路径
     * @throws IOException
     */
    public static void fileDecompress(String uri, String outputPath) throws IOException {

        Configuration conf = new Configuration();
        FileSystem fileSystem = FileSystem.get(URI.create(uri), conf);
        Class clazz = GzipCodec.class;
        //创建gzipo codec实例
        CompressionCodec gzipCodec= (CompressionCodec) ReflectionUtils.newInstance(clazz, conf);

        //解压器
        Decompressor decompressor = gzipCodec.createDecompressor();

        // 创建文件输入流，传入解压器
        Path p = new Path(uri);
        InputStream inputStream = gzipCodec.createInputStream(fileSystem.open(p), decompressor);
        // 创建文件输出流
        OutputStream outputStream = fileSystem.create(new Path(outputPath));
        // 复制到文件输出流
        IOUtils.copyBytes(inputStream, outputStream, 4096);
        inputStream.close();
        outputStream.close();

    }

    /**
     * 获取指定起止日期之间的所有日期
     * @param startTime 起始日期
     * @param endTime 截止日期
     * @return 日期集合List<String>
     * @throws ParseException
     */
    public static List<String> getTimesBetweenDates(String startTime, String endTime) throws ParseException {

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        Date start = dateFormat.parse(startTime);
        Date end = dateFormat.parse(endTime);

        long time1 = start.getTime();
        long time2 = end.getTime();
        Date midTime = new Date();
        if (time1 > time2) {
            midTime = start;
            start = end;
            end = midTime;
        }
        List<String> result = new ArrayList<String>();
        Calendar startCal = Calendar.getInstance();
        startCal.setTime(start);

        Calendar endCal = Calendar.getInstance();
        endCal.setTime(end);
        endCal.add(Calendar.DATE, +1);
        while (startCal.before(endCal)) {
            result.add(dateFormat.format(startCal.getTime()));
            startCal.add(Calendar.DAY_OF_YEAR, 1);
        }
        return result;

    }


}
