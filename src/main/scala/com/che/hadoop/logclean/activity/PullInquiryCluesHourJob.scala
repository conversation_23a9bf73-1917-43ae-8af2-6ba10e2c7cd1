package com.che.hadoop.logclean.activity

import java.util.Calendar

import com.che.hadoop.logclean.bo.PullInquiryClues
import com.che.hadoop.underlay.dao.MySparkSession
import com.che.hadoop.logclean.utils.Common
/**
 * 拉取线索数据,数据写到hive表t_inquiry_clue
 */
object PullInquiryCluesHourJob {

  def main(args: Array[String]): Unit = {

    val cal: Calendar = Calendar.getInstance()
    val hour = cal.get(Calendar.HOUR_OF_DAY)
    var beforeDay = 0
    if(hour == 0){
      beforeDay=1
    }
    val spark=MySparkSession.conn()
    PullInquiryClues.getClues(spark,beforeDay)
    spark.stop()
  }

}
