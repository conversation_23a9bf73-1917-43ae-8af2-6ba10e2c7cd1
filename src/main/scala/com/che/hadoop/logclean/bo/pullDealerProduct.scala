package com.che.hadoop.logclean.bo

import java.util.ResourceBundle

import org.apache.spark
import com.che.hadoop.logclean.utils.{Common, DingRobot}
import com.che.hadoop.underlay.dao.{MyKudu, MySparkSession, MySqlBaseDao}
import org.apache.spark.sql.SparkSession
import org.apache.spark.sql.types.{IntegerType, StructField, StructType}
/**
 * 拉取经销商售卖车型数据，从sqlserver备份库读取数据
 */
object pullDealerProduct {
  def main(args: Array[String]): Unit = {
    val spark=MySparkSession.conn()
    val sqlBase=new MySqlBaseDao()
    val day="20241110"
//    pullDealerArticle(spark,sqlBase,day)
    pullDealerProduct(spark,day)
  }

  /**
   * 拉取经销商关联车型信息
   * @param day String (yyyyMMdd)
   */
  def pullDealerProduct(ss:SparkSession, day:String): Unit ={
    val sqlBase=new MySqlBaseDao("truckhome")
    val t_check_pre="aliyun_"
    try{
      val resultTable="t_ods_dealer_product"
      val shortDay=Common.transFormat(day,"yyyyMMdd","yyyy-MM-dd")
      sqlBase.getCacheTable("t_dealer_product_tmp_view",
        s"""
           |(select F_id,F_DealerId,SubdealerId,F_ProductId,cast(F_IsEnable as int) as F_IsEnable
           |from TruckDealer.dbo.Tab_Dealer_ProductPrice
           |where F_LastUpdateTime>='$shortDay' and
           |EXISTS ( SELECT Id FROM TruckDealer.dbo.Dealers_Sub WITH (NOLOCK)
           |        WHERE IsEnable = 1  AND ChargeType > 0 AND Dealers_Sub.Id = Tab_Dealer_ProductPrice.SubdealerId)
           |)t
           |""".stripMargin,ss)
      ss.sql(" set mapred.reduce.tasks=3")
      ss.sql(
        s"""
           |insert overwrite table $resultTable
           |(select F_id,F_DealerId,SubdealerId,F_ProductId,F_IsEnable from t_dealer_product_tmp_view
           |union
           |select F_id,F_DealerId,SubdealerId,F_ProductId,F_IsEnable from $resultTable
           |)
           |""".stripMargin)
      val exec = new MySqlBaseDao()
      exec.executeInsertOrUpdate(s"update t_check set flag=1 , message='$day:运行成功' where name='${t_check_pre}t_ods_dealer_product'",null)
    }catch {
      case e: Exception => {
        e.printStackTrace()
        try{
          val exec = new MySqlBaseDao()

          exec.executeInsertOrUpdate(s"update t_check set flag=2, message='$day:${t_check_pre}t_ods_dealer_product 运行失败' where name='${t_check_pre}t_ods_dealer_product'",null)
        }catch {
          case e:Throwable=>{
            e.printStackTrace()
          }
        }
      }
    }
  }

  /**
   * 业务数据同步，公布经销商发布文章数据 **数据实时计算，离线暂停
   * @param spark SparkSession
   * @param sqlBase mysql数据库操作实例
   * @param day 计算日期
   */
  def pullDealerArticle(spark:SparkSession,sqlBase:MySqlBaseDao,day:String): Unit ={
    try{
      val resultTable="db_userportrait_test.t_dealer_article"
      val myKudu=new MyKudu(spark)
      myKudu.select("t_logdata_thba_streaming").select("data", "billtype","timestamp","operation_state","env").where(
        s"""
          |day='$day'
          |and env = 'test'
          |""".stripMargin).createOrReplaceTempView("t_biz_data_temp")
      val resultDF = spark.sql(
        s"""
           |select c0 as id,nvl(c1,'') as title,nvl(c2,'') as create_time,nvl(c3,'') as publish_time,nvl(c4,'') as start_time,nvl(c5,'') as end_time,nvl(c6,'') as relation_id,nvl(c7,0) as articletype_id,nvl(c8,'') as articletype,nvl(c9,'') as shopid,nvl(c10,'') as dealerid,nvl(c10,0) as province_code,nvl(c12,0) as city_code,nvl(c13,'') as province,nvl(c14,'') as city,if(operation_state='delete',1,0) as is_del,timestamp,from_unixtime(cast(substr(`timestamp`,1,10) as int),'yyyyMMdd') as day,c15 as relation_products,ROW_NUMBER() over(partition by c0 order by timestamp desc) as rank  from
           |(select json_tuple(data,'articleId','title','createTime','publishTime','startTime','endTime','relationId','articleTypeId','articleType','shopID','dealerId','provinceCode','cityCode','province','city','relationProducts'),`timestamp`,billType,operation_state
           |from t_biz_data_temp where billType ='dealer_article') as t
           |""".stripMargin).filter("rank=1").drop("rank").collect()

      sqlBase.executeMany2(
        """
          |REPLACE INTO db_userportrait_test.t_dealer_article
          |(id, title, create_time, publish_time, start_time, end_time, relation_id, articletype_id, articletype, shopid, dealerid, province_code, city_code, province, city, is_del, `timestamp`,day,relation_products)
          |VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)
          |""".stripMargin, resultDF)
//     数据只有insert直接保存
      sqlBase.execute(s"update t_check set flag=1, message='$day:t_dealer_article 运行成功' where name='aliyun_t_dealer_article'")
    }catch {
      case e: Exception => {
        e.printStackTrace()
        try{
          val exec = new MySqlBaseDao()
          exec.execute(s"update t_check set flag=2, message='$day:t_dealer_article 运行失败' where name='aliyun_t_dealer_article'")
        }catch {
          case e:Throwable=>{
            e.printStackTrace()
          }
        }
      }
    }
  }

  /**
   * 业务数据同步，公布经销商发布文章数据 **数据实时计算，离线暂停
   *
   * @param spark   SparkSession
   * @param sqlBase mysql数据库操作实例
   * @param day     计算日期
   */
  def pullDealerBusinessOpportunity(spark: SparkSession, sqlBase: MySqlBaseDao, day: String): Unit = {
    val resultTable = "db_userportrait_test.t_gb_business_opportunity"
    val myKudu = new MyKudu(spark)
    myKudu.select("t_logdata_thba_streaming").select("data", "billtype", "timestamp", "operation_state", "env").where(
      s"""
         |day>='$day'
         |and env <> 'test'
         |""".stripMargin).createOrReplaceTempView("t_biz_data_temp")
    val resultDF = spark.sql(
      s"""
         |select c0 as sid,c1 as tel,c2 as manufacturer_name,c3 as business_status,c4 as is_enable,c5 as create_time,c6 as update_time,c7 as clue_id,if(c8='','0',c8) as clue_uid,c9 as clue_vid,ROW_NUMBER() over(partition by c0 order by timestamp desc) as rank  from
         |(select json_tuple(data,'sid','tel','manufacturerName','businessStatus','isEnable','createTime','updateTime','clueId','clueUid','clueVid'),`timestamp`,billType,operation_state
         |from t_biz_data_temp where billType ='gb_business_opportunity') as t
         |""".stripMargin).filter("rank=1").drop("rank")
//    resultDF.show(false)
    sqlBase.executeMany2(
      """
        |REPLACE INTO db_userportrait.t_gb_business_opportunity
        |(sid, tel, manufacturer_name, business_status, is_enable, create_time, update_time, clue_id, clue_uid, clue_vid)
        |VALUES(?,?,?,?,?,?,?,?,?,?)
        |""".stripMargin, resultDF.collect())
  }

  /**
   * sqlserver链接测试
   */
  def readFromSqlServer(): Unit ={
    val dbServer = "***********"
    val db = "master"
    val url = s"************************************************;"
    val ss=MySparkSession.conn()
    val dealerProductDF = ss.read
      .format("jdbc")
      .option("driver", "com.microsoft.sqlserver.jdbc.SQLServerDriver")
      .option("url", url)
      .option("user", "TruckhomeDB")
      .option("password", "truckhome#!#")
      .option("dbtable", "(select top 10 * from TruckDealer.dbo.Tab_Dealer_ProductPrice)t")
      .load().limit(10).show(false)
  }
  /**
   * 读取csv文件到hive
   */
  def csv2hive(): Unit ={
    val ss=MySparkSession.conn()
    val sch = StructType(Array(
      StructField("F_id", IntegerType),
      StructField("F_DealerId", IntegerType),
        StructField("SubdealerId", IntegerType),
          StructField("F_ProductId", IntegerType),
            StructField("F_IsEnable", IntegerType)
        )
    )
    val filePath="file:////D:\\download\\tab_dealer_price.csv"
    val resultDF=ss.read
      .format("csv")
      .schema(sch)
      .option("header","true").load(filePath)
    resultDF.write.mode("append")
      .format("hive")
      .saveAsTable("t_ods_dealer_product")
  }
}
