package com.che.hadoop.logclean.bo

import java.util.ResourceBundle

import com.che.hadoop.underlay.dao.{MySparkSession, MySqlBaseDao}
import com.che.hadoop.logclean.utils.{Common, DingRobot, UrlDecodeUtil}
import org.apache.spark.sql.{DataFrame, SparkSession}
import com.alibaba.fastjson._
import org.apache.spark.rdd.RDD
/**
 * 从日志拉取卡友圈数据
 * @ author dianwei
 * @ date 2020-07-06 09:59
 */
object PullKyqData {
    case class dataList(data:String,billType:String,timestamp:String,operationState:String,env:String)

    def main(args: Array[String]): Unit = {
        val d1: Long = System.currentTimeMillis()

        val day: String = Common.getDate(0)


        pullHelpDataHour(day)


        val d2: Long = System.currentTimeMillis()
        println(s"耗时:${(d2 - d1) / 1000}秒")
    }

    private val spark: SparkSession = MySparkSession.conn()

    /**
     * 卡友圈、图集
     *
     * @param day 昨天
     */
    def start(day: String): Unit = {
        try {
            val environment = "default."
            // val environment = "db_test."
            spark.udf.register("urlDecode", UrlDecodeUtil.urlDecode(_: String))
            spark.read.json("oss://360che-bigdata.cn-beijing.oss-dls.aliyuncs.com/pv-nginx-app/" + day + "")
              //            spark.read.json("/Users/<USER>/Downloads/a")
              .filter("status=200 and http_host='thba.360che.com'")
              .coalesce(1)
              .selectExpr("urlDecode(request_body) as request_body")
              .createOrReplaceTempView("temp_table")
            spark.sql("select json_tuple(request_body, 'data', 'billType','timestamp','operationState') from temp_table")
              .createOrReplaceTempView("t_biz_data_temp")
            spark.catalog.cacheTable("t_biz_data_temp")
            pullPicture(spark,day, environment)
            pullKyq(spark,day, environment)
            pullSection(spark,day, environment)
        } catch {
            case exception: Exception => {
                println(exception)
                val robot = new DingRobot(ResourceBundle.getBundle("robot").getString("ding.monitor"))
                robot.sendAlert(s"【监控--拉取卡友圈、图集数据】\n原因：${exception.toString}")

            }

        }
    }


    /**
     * 口碑
     *
     * @param day 当天时间
     */
    def mouthStart(day: String): Unit = {
        try {
            val environment = "default."
            // val environment = "db_test."
            spark.udf.register("urlDecode", UrlDecodeUtil.urlDecode(_: String))
            spark.read.json("oss://360che-bigdata.cn-beijing.oss-dls.aliyuncs.com/pv-nginx-app/" + day)
              .filter("status=200 and http_host='thba.360che.com'")
              .coalesce(1)
              .selectExpr("urlDecode(request_body) as request_body")
              .createOrReplaceTempView("temp_table")
            spark.sql("select json_tuple(request_body, 'data', 'billType','timestamp','operationState') from temp_table")
              .createOrReplaceTempView("t_biz_data_temp")
            spark.catalog.cacheTable("t_biz_data_temp")
            pullMouth(spark,day, environment)
            spark.stop()

        } catch {
            case exception: Exception => {
                println(exception)
                val robot = new DingRobot(ResourceBundle.getBundle("robot").getString("ding.monitor"))
                robot.sendAlert(s"【监控--拉取口碑数据】\n原因：${exception.toString}")

            }

        }
    }

    /**
     * 图集
     * @param day 日期
     */
    def pullPicture(spark:SparkSession,day: String, environment: String="default"): Unit = {
        val t_check_pre="aliyun_"
        try {
            //图集
            spark.sql("alter table t_article_picture drop if exists partition(day='" + day + "')")
            val pictureData: DataFrame = spark.sql("select json_tuple(data,'id','title','publishTime'" +
              "),`timestamp`, operation_state,from_unixtime(substr(`timestamp`,0,10), 'yyyyMMdd') from t_biz_data_temp where billtype='article_t_picture'")
              .toDF("id", "title", "publishTime", "timestamp", "operationState", "day")
            pictureData.write.mode("append").partitionBy("day").format("Hive").saveAsTable(environment + "t_article_picture")
            val exec = new MySqlBaseDao()
            exec.executeInsertOrUpdate(s"update t_check set flag=1 , message='$day:运行成功' where name='${t_check_pre}t_article_picture'",null)
        } catch {
            case e: Exception => {
                e.printStackTrace()
                try{
                    val exec = new MySqlBaseDao()

                    exec.executeInsertOrUpdate(s"update t_check set flag=2, message='$day:${t_check_pre}t_article_picture 运行失败' where name='${t_check_pre}t_article_picture'",null)
                }catch {
                    case e:Throwable=>{
                        e.printStackTrace()
                    }
                }
            }
        }

    }

    /**
     * 拉取卡友圈数据
     * @param day 日期
     */
    def pullKyq(spark:SparkSession,day: String, environment: String="default"): Unit = {
        val t_check_pre="aliyun_"
        try {
            spark.sql("alter table t_kyq drop if exists partition(day='" + day + "')")
            spark.sql("alter table t_kyq_comment drop if exists partition(day='" + day + "')")
            //获取卡友圈基本数据
            val kyqData: DataFrame = spark.sql("select json_tuple(data,'id', 'uid', 'tag', 'content', 'type', 'videoUrl', " +
              "'imgUrl', 'publishTime','address','province','city'),`timestamp`, operation_state,from_unixtime(substr(`timestamp`,0,10), 'yyyyMMdd') " +
              "from t_biz_data_temp where billtype='kyq_publish'")
              .toDF("id", "uid", "tag", "content", "type", "videoUrl", "imgUrl", "publishTime", "address", "province",
                  "city", "timestamp", "operationState", "day")
            kyqData.write.mode("append").partitionBy("day").format("Hive").saveAsTable(environment + "t_kyq")
            //获取卡友圈评论数据
            val kyqCommentData: DataFrame = spark.sql("select json_tuple(data,'id', 'uid', 'aid', 'replyId', 'content', 'publishTime' " +
              "),`timestamp`, operation_state,from_unixtime(substr(`timestamp`,0,10), 'yyyyMMdd') from t_biz_data_temp where billtype='kyq_comment'")
              .toDF("id", "uid", "aid", "replyId", "content", "publishTime", "timestamp", "operationState", "day")
            kyqCommentData.write.mode("append").partitionBy("day").format("Hive").saveAsTable(environment + "t_kyq_comment")
            val exec = new MySqlBaseDao()
            exec.executeInsertOrUpdate(s"update t_check set flag=1 , message='$day:运行成功' where name='${t_check_pre}t_kyq'",null)
        } catch {
            case e: Exception => {
                e.printStackTrace()
                try{
                    val exec = new MySqlBaseDao()

                    exec.executeInsertOrUpdate(s"update t_check set flag=2, message='$day:${t_check_pre}t_kyq 运行失败' where name='${t_check_pre}t_kyq'",null)
                }catch {
                    case e:Throwable=>{
                        e.printStackTrace()
                    }
                }
            }
        }

    }

    /**
     * 获取口碑数据
     * @param day 日期
     */
    def pullMouth(spark:SparkSession,day: String, environment: String="default"): Unit = {
        val t_check_pre="aliyun_"
        try {
            spark.sql("alter table t_product_comment drop if exists partition(day='" + day + "')")
            val mouthData: DataFrame = spark.sql("select json_tuple(data,'id','uid','cateId','subCategoryId','brandId','seriesId','productId'," +
              "'nakedPrice','liftTime','provinceId','cityId','traffic','kilomelre','fuelPetroleum','score','comprehensiveScore','status','essence'," +
              "'full','address','createTime'),`timestamp`, operation_state,from_unixtime(substr(`timestamp`,0,10), 'yyyyMMdd') from t_biz_data_temp where billtype='product_comment'")
              .toDF("id", "uid", "cateId", "subCategoryId", "brandId", "seriesId", "productId", "nakedPrice", "liftTime", "provinceId",
                  "cityId", "traffic", "kilomelre", "fuelPetroleum", "score", "comprehensiveScore", "status", "essence", "fulls", "address",
                  "createTime", "timestamp", "operationState", "day")
            mouthData.write.mode("append").partitionBy("day").format("Hive").saveAsTable(environment + "t_product_comment")
            val exec = new MySqlBaseDao()
            exec.executeInsertOrUpdate(s"update t_check set flag=1 , message='$day:运行成功' where name='${t_check_pre}t_product_comment'",null)
        } catch {
            case e: Exception => {
                e.printStackTrace()
                try{
                    val exec = new MySqlBaseDao()
                    exec.executeInsertOrUpdate(s"update t_check set flag=2, message='$day:${t_check_pre}t_product_comment 运行失败' where name='${t_check_pre}t_product_comment'",null)
                }catch {
                    case e:Throwable=>{
                        e.printStackTrace()
                    }
                }
            }
        }
    }

    /**
     * 口碑历史数据
     * @param day 日期
     */
    def pullHistoryMouth(day: String, environment: String): Unit = {
        val sqlBase = new MySqlBaseDao()

        sqlBase.getCacheTable("t_mouth_temp", "(select * from db_hitsstatistic_test.Tab_UserMouth ) as t",spark)
        spark.sql("alter table db_test.t_product_comment drop if exists partition(day='" + day + "')")

        val mouthData: DataFrame = spark.sql(
            s"""
               |select F_Id,F_Uid,F_CateId,F_SubCategoryId,F_BrandId,F_SeriesId,F_ProductId,F_NakedPrice,F_LiftTime,F_provinceid,
               | F_Cityid,F_Traffic,F_Kilomelre,F_FuelPetroleum,F_Score,F_ComprehensiveScore,F_Status,F_Essence,F_Full ,F_Address,
               | F_CreateDateTime, unix_timestamp(now()),'insert',20200813  from t_mouth_temp
             """.stripMargin)
          .toDF("id", "uid", "cateId", "subCategoryId", "brandId", "seriesId", "productId", "nakedPrice", "liftTime", "provinceId",
              "cityId", "traffic", "kilomelre", "fuelPetroleum", "score", "comprehensiveScore", "status", "essence", "fulls", "address",
              "createTime", "timestamp", "operationState", "day")
        mouthData.write.mode("append").partitionBy("day").format("Hive").saveAsTable(environment + "t_product_comment")
    }

    /**
     * 获取栏目数据
     * @param day 日期
     */
    def pullSection(spark:SparkSession,day: String, env: String ="default"): Unit = {
        val t_check_pre="aliyun_"
        try {
            val sectionData: DataFrame = spark.sql(
                s"""
                   |select json_tuple(data,'id','name','type','createTime'),`timestamp`, operation_state,from_unixtime(substr(`timestamp`,0,10), 'yyyyMMdd') from t_biz_data_temp where billtype = 'article_section'
             """.stripMargin)
              .toDF("id", "name", "type", "createTime", "timestamp", "operationState", "day")
            sectionData.repartition(1).write.mode("append").format("Hive").saveAsTable(env + "t_ods_section")
            val exec = new MySqlBaseDao()
            exec.executeInsertOrUpdate(s"update t_check set flag=1 , message='$day:运行成功' where name='${t_check_pre}t_ods_section'",null)
        } catch {
            case e: Exception => {
                e.printStackTrace()
                try{
                    val exec = new MySqlBaseDao()

                    exec.executeInsertOrUpdate(s"update t_check set flag=2, message='$day:${t_check_pre}t_ods_section 运行失败' where name='${t_check_pre}t_ods_section'",null)
                }catch {
                    case e:Throwable=>{
                        e.printStackTrace()
                    }
                }
            }
        }
    }

    /**
     * 数据大屏-问答小时数据
     * 已废弃20230616 李晓乾标注
     * @param day 日期
     */
    def pullHelpDataHour(day: String, env: String = "db_test."): Unit = {
        import spark.implicits._
        //val mysql = new MySqlBaseDaoOld()
        val mysql=new MySqlBaseDao("bigdata2")
        spark.udf.register("urlDecode", UrlDecodeUtil.urlDecode(_: String))
        spark.read.json("oss://360che-bigdata.cn-beijing.oss-dls.aliyuncs.com/pv-nginx-app/" + day+"/*.gz")
          .filter("status=200 and http_host='thba.360che.com'")
          .selectExpr("urlDecode(request_body) as request_body")
          .createOrReplaceTempView("temp_table")
        //        tempDF.rdd.map(line => {
        //            val requestBody = line.getAs[String]("request_body")
        //            val obj = JSON.parseObject(requestBody)
        //            val data = obj.getString("data")
        //            val billType = obj.getString("billType")
        //            val timestamp = obj.getString("timestamp")
        //            val operationState = obj.getString("operationState")
        //            val env = obj.getString("env")
        //            dataList(data, billType, timestamp, operationState, env)
        //        }).toDF("c0","c1","c2","c3","c4").createOrReplaceTempView("temp_table1")


        spark.sql("select json_tuple(request_body, 'data', 'billType','timestamp','operationState', 'env') from temp_table").createOrReplaceTempView("temp_table1")

        spark.catalog.cacheTable("temp_table1")

        //求助
        spark.sql("select json_tuple(c0, 'id','title','classifyId','classifyName','createDateTime','userId','address','content','rewordValue','referrerPage','referrerQuery','viewTerminal','coordinate','cityId','detailed','resultsNote','feedBack','feedBackUid','solveDate','isSolve'),c2, c3,from_unixtime(substr(c2,0,10), 'yyyyMMdd') from temp_table1 where c1='help_t_forhelp'")
          .toDF("id", "title", "classifyId", "classifyName", "createDateTime", "userId", "address", "content", "rewordValue", "referrerPage", "referrerQuery", "viewTerminal", "coordinate", "cityId", "detailed", "resultsNote", "feedBack", "feedBackUid", "solveDate", "isSolve", "timestamp", "operationState", "day")
          .createOrReplaceTempView("t_forhelp_temp")
        //        spark.sql("select * from t_forhelp_temp").show(false)
        //帮助回答/追问
        spark.sql("select json_tuple(c0, 'id','content','thankTitle','createDateTime','userId','forHelpId','isAdmin'),c2, c3,from_unixtime(substr(c2,0,10), 'yyyyMMdd') from temp_table1 where c1='help_t_help'")
          .toDF("id", "content", "thankTitle", "createDateTime", "userId", "forHelpId", "isAdmin", "timestamp", "operationState", "day")
          .createOrReplaceTempView("t_help_temp")

        //统计每日新增求助数
        val helpDf = spark.sql(s"select count(1) as value,$day as date,10 as type " +
          s"from t_forhelp_temp where day='$day' and operationstate='insert'")

        //统计每日新增被解决问题数
        val resolveDf = spark.sql(s"select count(1) as value,$day as date,20 as type " +
          s"from t_forhelp_temp where day='$day' and operationstate='update' and issolve='1'")

        //统计每日新增回答数
        val answerDf = spark.sql(s"select count(1) as value,$day as date,30 as type " +
          s"from t_help_temp where day='$day' and operationstate='insert'")
        mysql.execute("delete from t_monitor_data_screen where type in (10,20,30)")
        mysql.saveMysql(helpDf.union(resolveDf).union(answerDf),"t_monitor_data_screen")
        spark.stop()
    }


}
