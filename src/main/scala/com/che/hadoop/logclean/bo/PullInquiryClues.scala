package com.che.hadoop.logclean.bo

import java.text.SimpleDateFormat
import java.util.{ResourceBundle}
import com.alibaba.fastjson.JSON
import com.che.hadoop.logclean.utils.{Common, MD5Util}
import com.che.hadoop.underlay.dao.{MySparkSession, MySqlBaseDao}
import com.che.hadoop.underlay.tool.common.Common.base64
import com.che.hadoop.underlay.tool.http.MyHttpService
import org.apache.spark.sql.{SparkSession}
import scalaj.http.Http
import scala.collection.mutable.{ArrayBuffer, ListBuffer}


/**
 * 拉取线索数据
 * 接口对接：王鹏
 * gaojie
 */
object PullInquiryClues {

  def main(args: Array[String]): Unit = {

//    val t=Tuple3(1,2,"a,b,c")
//    println(t.toString())
    val spark=MySparkSession.conn()
//    test(spark)
    val sqlBase=new MySqlBaseDao()
    getDealerProvinceHotProduct(sqlBase,"20240305")
//    getTruckMarketOrders(spark,sqlBase,"20230419")
//    getFavoriteTruck(spark,sqlBase,"20230418")
//    getDealCycle(spark,sqlBase,"20230418")
//    getDealerVerify(sqlBase,"20230418")
  }

  /**
   * 拉取询价线索数据
   * @param beforeDay  指定拉取距离今天n天前的数据
   * @return
   */
  def getClues(sparkSession:SparkSession, beforeDay:Int): String ={
    val reader = ResourceBundle.getBundle("connection")
    val  sourceUrl =reader.getString("api.dealer.clues")
    val  sourceUrlSpm =reader.getString("api.dealer.cluesSpm")
    val appid = reader.getString("dealer.appid") //appid
    val key = reader.getString("dealer.key")
    val pullDate = Common.getShortDateTime(beforeDay)
    val day = Common.getDate(beforeDay)
    //val day = DateFormatTransfer(pullDate)
    println(pullDate)
    val table = "t_inquiry_clue"
    val timespan =  System.currentTimeMillis().toString.substring(0,10)
    val sign = MD5Util.MD5Encoder(key+timespan,"UTF-8")
    // 请求接口获取Json数据
    val response = Http(sourceUrl).param("pullDate", pullDate)
      .param("appid", appid).param("sign", sign).param("timespan", timespan).asString
    val resultJson = JSON.parseObject(response.body)
    println(resultJson)
    // 判断接口返回的状态，若失败直接return失败信息，程序不再往下进行
    if (resultJson.getInteger("status") == 0) {
      return resultJson.getString("msg")
    }

    // 请求接口获取Json数据，拉去spm
    val responseSpm = Http(sourceUrlSpm).param("pullDate", pullDate)
      .param("appid", appid).param("sign", sign).param("timespan", timespan).asString
    val resultJsonSpm = JSON.parseObject(responseSpm.body)
    println(resultJsonSpm)
    // 判断接口返回的状态，若失败直接return失败信息，程序不再往下进行
    if (resultJsonSpm.getInteger("status") == 0) {
      return resultJsonSpm.getString("msg")
    }

//    val sparkSession = MySparkSession.conn("LogClean_PullInquiryClues")
    import sparkSession.implicits._
    // 每小时执行一次 ，重新插入数据
    sparkSession.sql("alter table " + table + " drop if exists partition(day='" + day + "')")
    val dataList = new ListBuffer[(String,String, String,Int,Int,Int,Int,String, String,String,String,String,String,Int,String)]
    val dataListSpm = new ListBuffer[(String,String,String,String,String)]
    val jsonArraySpm = resultJsonSpm.getJSONArray("data")
    println(jsonArraySpm)
    val jsonArray = resultJson.getJSONArray("data")

    for(item <- 0 until  jsonArray.size()){
      val argObj = jsonArray.getJSONObject(item)
      //        println(argObj)
      val createDate = argObj.getString("createDate")
      val time = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(createDate)
      val hour = time.getHours
      dataList.append((
        argObj.getString("id"),
        argObj.getString("tel"),
        argObj.getString("realName"),
        argObj.getInteger("brandId"),
        argObj.getInteger("seriesId"),
        argObj.getInteger("truckId"),
        argObj.getInteger("clueResourceId"), argObj.getString("clueResourceName"),
        createDate,
        argObj.getString("provinceId"),
        argObj.getString("cityId"),
        argObj.getString("cityName"),
        argObj.getString("provinceName"),
        hour, day))
    }
    val dataDF = dataList.toDF("id","tel","real_name","brand_id","series_id","truck_id","clue_resource_id",
      "clue_resource_name","create_date","province_id","city_id","city_name","province_name","hour","day")

    //      dataDF.repartition(1).write.mode("append").format("Hive").partitionBy("day").saveAsTable(table)
    dataDF.createOrReplaceTempView("t_clues_log")


    for(item <- 0 until  jsonArraySpm.size()){
      val argObjSpm = jsonArraySpm.getJSONObject(item)
      // println(argObjSpm)
      dataListSpm.append(( argObjSpm.getString("cluesId"),
        argObjSpm.getString("spm"),
        argObjSpm.getString("udstatisticss"),
        argObjSpm.getString("udstatistics"),day
      ))
    }

    val dataDFSpm = dataListSpm.toDF("clues_id","spm","vid","gid","day")
    dataDFSpm.createOrReplaceTempView("t_cluesSpm_log")

//    val cluesDataDF = sparkSession.sql(
//      """
//        |select t2.id,t1.vid,t1.gid,t1.spm,base64(tel) tel,base64(real_name) real_name,brand_id,series_id,truck_id,clue_resource_id,
//        |        clue_resource_name,create_date,province_id,city_id,city_name,province_name,hour,t2.day
//        |from t_cluesSpm_log t1 right join t_clues_log t2 on t1.clues_id = t2.id and t1.day = t2.day
//        |where t1.clues_id is not null
//        |""".stripMargin)
    //修改同步线索数据保存全部数据（原来的数据排除了clue_resource_id in (9,10,11,100,201)） 20230426修改
    val cluesDataDF = sparkSession.sql(
      """
        |select t2.id,nvl(t1.vid,'') as vid,nvl(t1.gid,'') as gid,nvl(t1.spm,'') as spm,base64(tel) tel,base64(real_name) real_name,brand_id,series_id,truck_id,clue_resource_id,
        |        clue_resource_name,create_date,province_id,city_id,city_name,province_name,hour,t2.day
        |from t_cluesSpm_log t1 right join t_clues_log t2 on t1.clues_id = t2.id and t1.day = t2.day
        |-- where t1.clues_id is not null
        |""".stripMargin)
    //保存数据到数据库
    cluesDataDF.repartition(1).write.mode("append").format("Hive").partitionBy("day").saveAsTable(table)
    resultJson.getString("msg")
    resultJsonSpm.getString("msg")
  }

  /**
   * 拉取经销商认证数据,数据保存在mysql
   * @param sqlBase mysql链接工具类
   * @return
   */
  def getDealerVerify(sqlBase:MySqlBaseDao,day:String): Any ={
    try{
      val resultTable="db_userportrait.t_verify_dealer"
      val bussinessID="4"
      val pageSize=100
      val reader = ResourceBundle.getBundle("connection")
      val apiAll = reader.getString("api.dealer.verify.allData") //appid
      val apiCloseData = reader.getString("api.dealer.verify.closeData") //appid
      val key = reader.getString("dealer.verify.key")
      val sign = MD5Util.MD5Encoder(bussinessID+key,"UTF-8")
      // 1先请求全部数据（状态是未关闭）
      val response = Http(apiAll).param("BusinessID", bussinessID)
        .param("sign", sign)
        .param("pageSize",pageSize.toString)
        //第一次同步数据时候打开
//        .param("isNoShowClose","1")
        .param("currentPage","1").asString
      val resultJson = JSON.parseObject(response.body)
      // 判断接口返回的状态，若失败直接return失败信息，程序不再往下进行
      val status=resultJson.getInteger("status")
      if ( status== 0) {
        return resultJson.getString("msg")
      }else if(status==1){
        val totalPage=resultJson.getIntValue("totalPage")
        //循环请求数据
        for(i<-1 to totalPage){
          val response = Http(apiAll).param("BusinessID", bussinessID)
            .param("sign", sign)
            .param("pageSize",pageSize.toString)
            //第一次同步数据时候打开
//            .param("isNoShowClose","1")
            .param("currentPage",s"$i").asString
          val resultJson = JSON.parseObject(response.body)
          val dataList=resultJson.getJSONArray("data")
          val args=ArrayBuffer[List[Any]]()
          for(i <- 0 until dataList.size()){
            val data=JSON.parseObject(dataList.get(i).toString)
            val dealerId=data.getIntValue("dealerId")
            val dealerName=data.getString("dealerName")
            val dealerStatus=data.getIntValue("dealerStatus")
            val isCharge=data.getIntValue("isCharge")
            val brandList=data.getJSONArray("brands")
            val brandInCharge=ArrayBuffer[Int]()
            val brandNotCharge=ArrayBuffer[Int]()
            for(i <- 0 until  brandList.size()){
              val item=JSON.parseObject(brandList.get(i).toString)
              val brandId=item.getIntValue("brandId")
              val BrandInCharge=item.getIntValue("isCharge")
              if(BrandInCharge==1){
                brandInCharge.append(brandId)
              }else if(BrandInCharge==0){
                brandNotCharge.append(brandId)
              }
            }
            val telList=data.getJSONArray("tels")
            val tel1=ArrayBuffer[String]()
            val tel2=ArrayBuffer[String]()
            val tel3=ArrayBuffer[String]()
            for(i <- 0 until  telList.size()){
              val item=JSON.parseObject(telList.get(i).toString)
              val tel=base64(item.getString("tel"))
              val typeId=item.getIntValue("typeId")
              if(typeId==1){
                tel1.append(tel)
              }else if(typeId==2){
                tel2.append(tel)
              }else if(typeId==3){
                tel3.append(tel)
              }
            }
            args.append(List(dealerId,dealerName,dealerStatus,isCharge,tel1.mkString(","),tel2.mkString(","),tel3.mkString(","),brandInCharge.mkString(","),brandNotCharge.mkString(",")))
          }
          sqlBase.executeMany(s"REPLACE INTO $resultTable (dealer_id, dealer_name, dealer_status, is_charge, tel_secret, tel_brandsecret, tel_saler, brand_incharge, brand_notCharge) VALUES(?,?,?,?,?,?,?,?,?)",args)
        }
      }

      // 2先请求全部数据（状态是关闭）批量更新关闭状态的经销商
      val responseClose = Http(apiCloseData).param("BusinessID", bussinessID)
        .param("sign", sign).param("currentPage","1").asString
      val resultJson2 = JSON.parseObject(responseClose.body)
      // 判断接口返回的状态，若失败直接return失败信息，程序不再往下进行
      val status2=resultJson2.getInteger("status")
      if ( status2== 0) {
        return resultJson2.getString("msg")
      }else if(status2==1){
        val idList=resultJson2.getJSONArray("ids")
        val args=ArrayBuffer[List[Any]]()
        for (i <- 0 until idList.size()){
          args.append(List(idList.get(i)))
        }
        sqlBase.executeMany(s"update $resultTable set dealer_status=0 where dealer_id=?",args)
      }
      sqlBase.execute(s"update t_check set flag=1, message='$day:t_verify_dealer 运行成功' where name='aliyun_t_verify_dealer'")
    }catch {
    case e: Exception => {
      e.printStackTrace()
      try{
        val exec = new MySqlBaseDao()
        exec.execute(s"update t_check set flag=2, message='$day:t_verify_dealer 运行失败' where name='aliyun_t_verify_dealer'")
      }catch {
        case e:Throwable=>{
          e.printStackTrace()
        }
      }
    }
  }
//    //    测试update
//        val args=ArrayBuffer[Array[Any]]()
//        args.append(Array(3793))
//        sqlBase.executeMany("update db_hitsstatistic_test.t_verify_dealer set dealer_status=2 where dealer_id=?",args)
//    return

  }

  /**
   * 业务数据同步，大车市订单（一口价，抵扣券，库存车）
   * @param spark SparkSession
   * @param sqlBase mysql数据库操作实例
   * @param day 计算日期
   */
  def getTruckMarketOrders(spark:SparkSession,sqlBase:MySqlBaseDao,day:String): Unit ={
    try{
      val resultMysqlTable="db_userportrait.t_dealer_orders"
      val resultDF=spark.sql(
        s"""
           |select c0 as id,c1 as order_id,c2 as uid,base64(c3) as tel,c4 as brand_id,c5 as series_id,c6 as subcate_id,c7 as product_id,c8 as mall_product_id,c9 as type_id,c10 as create_time,day,ROW_NUMBER() OVER(partition by c0 order by c10 desc) as rank from
           |(select json_tuple(data, 'id', 'orderId','uid','tel', 'brand_id', 'series_id','subcate_id', 'truck_product_id', 'mall_product_id','type_id', 'createTime'),
           |  from_unixtime(cast(substr(`timestamp`,0,10) as int), 'yyyyMMdd') as day
           |from t_biz_data_temp where billType='truck_market_orders') as t
           |""".stripMargin).filter("rank=1").drop("rank")
      //数据保存到mysql
      val args=resultDF.collect().map(row=>List(row.getAs[Int]("id"),row.getAs[String]("order_id"),row.getAs[String]("mall_product_id"),row.getAs[Int]("uid"),row.getAs[String]("tel"),row.getAs[String]("brand_id"),row.getAs[String]("series_id"),row.getAs[String]("subcate_id"),row.getAs[String]("product_id"),row.getAs[String]("type_id"),row.getAs[String]("create_time"),row.getAs[String]("day"))).toList
      sqlBase.executeMany(
        s"""
           |REPLACE INTO $resultMysqlTable (id, order_id, mall_product_id, uid, tel, brand_id, series_id, subcate_id, product_id, type_id, create_time, `day`) VALUES(?,?,?,?,?,?,?,?,?,?,?,?)
           |""".stripMargin,args)
      sqlBase.execute(s"update t_check set flag=1, message='$day:t_dealer_orders 运行成功' where name='aliyun_t_dealer_orders'")
    }catch {
      case e: Exception => {
        e.printStackTrace()
        try{
          val exec = new MySqlBaseDao()
          exec.execute(s"update t_check set flag=2, message='$day:t_dealer_orders 运行失败' where name='aliyun_t_dealer_orders'")
        }catch {
          case e:Throwable=>{
            e.printStackTrace()
          }
        }
      }
    }
  }

  /**
   * 业务数据同步，用户购车周期
   * @param spark SparkSession
   * @param sqlBase mysql数据库操作实例
   * @param day 计算日期
   */
  def getDealCycle(spark:SparkSession,sqlBase:MySqlBaseDao,day:String): Unit ={
    try{
      val resultMysqlTable="db_userportrait.t_deal_cycle"
      val resultDF=spark.sql(
        s"""
           |select c0 as vid,if(c1='',null,c1) as uid,base64(c2) as tel,c3 as cycle,date_add(c4,cast(c3 as int)) as plan_date,c4 as create_time,day from
           |(select json_tuple(data, 'vid', 'uid', 'tel', 'cycle', 'createTime'),
           |  from_unixtime(cast(substr(`timestamp`,0,10) as int), 'yyyyMMdd') as day
           |from t_biz_data_temp where billType='deal_cycle') as t where CAST(c3 AS Int)>0
           |""".stripMargin)
      //数据只有insert直接保存
      sqlBase.execute(s"delete from $resultMysqlTable where day='$day'")
      sqlBase.saveMysql(resultDF,resultMysqlTable)
      sqlBase.execute(s"update t_check set flag=1, message='$day:t_deal_cycle 运行成功' where name='aliyun_t_deal_cycle'")
    }catch {
      case e: Exception => {
        e.printStackTrace()
        try{
          val exec = new MySqlBaseDao()
          exec.execute(s"update t_check set flag=2, message='$day:t_deal_cycle 运行失败' where name='aliyun_t_deal_cycle'")
        }catch {
          case e:Throwable=>{
            e.printStackTrace()
          }
        }
      }
    }

  }


  /**
   * 业务数据同步，车型，车系收藏数据
   * @param spark SparkSession
   * @param sqlBase mysql数据库操作实例
   * @param day 计算日期
   */
  def getFavoriteTruck(spark:SparkSession,sqlBase:MySqlBaseDao,day:String): Unit ={
    try{
      val resultMysqlTable="db_userportrait.t_favorite_truck"
      val resultDF=spark.sql(
        s"""
           |select c0 as id,c1 as vid,if(c2='0',null,c2) as uid,c3 as type_id,c4 as series_id,c5 as subcate_id,c6 as product_id,c7 as action,c8 as create_time,update_timestamp,ROW_NUMBER() OVER(partition by c0 order by `update_timestamp` desc) as rank from
           |(select json_tuple(data, 'id', 'vid', 'uid','type_id', 'seriesId','subcateId', 'productId','action', 'createTime'),`timestamp` as update_timestamp
           |from t_biz_data_temp where billType='favorite_truck') as t
           |""".stripMargin).filter("rank=1").drop("rank")
      //保存数据
      val args=resultDF.collect().map(row=>List(row.getAs[Int]("id"),row.getAs[String]("vid"),row.getAs[String]("uid"),row.getAs[String]("type_id"),row.getAs[String]("series_id"),row.getAs[String]("subcate_id"),row.getAs[String]("product_id"),row.getAs[String]("action"),row.getAs[String]("create_time"),row.getAs[String]("update_timestamp"))).toList
      sqlBase.executeMany(
        s"""
          |REPLACE INTO $resultMysqlTable (id, vid, uid, type_id, series_id, subcate_id, product_id, `action`, create_time,update_timestamp) VALUES(?,?,?,?,?,?,?,?,?,?)
          |""".stripMargin,args)
      sqlBase.execute(s"update t_check set flag=1, message='$day:t_favorite_truck 运行成功' where name='aliyun_t_favorite_truck'")
    }catch {
      case e: Exception => {
        e.printStackTrace()
        try{
          val exec = new MySqlBaseDao()
          exec.execute(s"update t_check set flag=2, message='$day:t_favorite_truck 运行失败' where name='aliyun_t_favorite_truck'")
        }catch {
          case e:Throwable=>{
            e.printStackTrace()
          }
        }
      }
    }
  }

  /**
   * 拉取付费经销商发布的热销车型数据,
   * 修改需求，近三个月发布的热销车型数量20240102
   *
   * @param sqlBase mysql链接工具类
   * @return
   */
  def getDealerProvinceHotProduct(sqlBase: MySqlBaseDao, day: String): Any = {
    try {
      val resultTable = "db_userportrait.t_dealer_province_hot_product"
      val bussinessID = "4"
      val pageSize = 10
      val totalPage=31/pageSize+1
      val reader = ResourceBundle.getBundle("connection")
      val apiAll = reader.getString("api.dealer.verify.hot.product") //appid
      println(apiAll)
      val key = reader.getString("dealer.verify.key")
      val sign = MD5Util.MD5Encoder(bussinessID + key, "UTF-8")
      //循环请求数据
      for (i <- 1 to totalPage) {
        val paramArg=s"?BusinessID=$bussinessID&sign=$sign&pageSize=$pageSize&currentPage=$i"
        val response=MyHttpService.getHttpRequest2(apiAll+paramArg)
        val resultJson = JSON.parseObject(response)
        val dataList = resultJson.getJSONArray("data")
        val args = ArrayBuffer[List[Any]]()
        for (i <- 0 until dataList.size()) {
          val data = JSON.parseObject(dataList.get(i).toString)
          val provinceSn = data.getString("provinceSn")
          val provinceName = data.getString("provinceName")
          val hotProductCount = data.getIntValue("hotProductCount")
          val hotProducts = data.getJSONArray("hotProducts")
          for (i <- 0 until hotProducts.size()) {
            val item = JSON.parseObject(hotProducts.get(i).toString)
            val productId = item.getIntValue("productId")
            val number = item.getIntValue("number")
            args.append(List(provinceSn, provinceName, hotProductCount, productId, number, day))
          }
        }
        sqlBase.executeMany(s"REPLACE INTO $resultTable (province_code,province_name,hot_product_count,product_id,number,update_date) VALUES(?,?,?,?,?,?)", args)
        sqlBase.execute(s"delete from $resultTable where update_date<'$day'")
      }
      sqlBase.execute(s"update t_check set flag=1, message='$day:t_dealer_province_hot_product 运行成功' where name='aliyun_t_dealer_province_hot_product'")
    } catch {
      case e: Exception => {
        e.printStackTrace()
        try {
          val exec = new MySqlBaseDao()
          exec.execute(s"update t_check set flag=2, message='$day:t_dealer_province_hot_product 运行失败' where name='aliyun_t_dealer_province_hot_product'")
        } catch {
          case e: Throwable => {
            e.printStackTrace()
          }
        }
      }
    }
  }

  /**
   * 更新数据
   */
  def test(spark:SparkSession): Unit ={
    val result=spark.sql(
      """
        |SELECT id,spm ,province_id ,city_id ,clue_resource_id ,clue_resource_name from t_inquiry_clue where day between'20230127' and '20230427'
        |""".stripMargin).collect().toList.map(row=>List(
      row.getAs[String]("spm"),
      row.getAs[String]("province_id"),
      row.getAs[String]("city_id"),
      row.getAs[Int]("clue_resource_id"),
      row.getAs[String]("clue_resource_name"),
      row.getAs[String]("id")))
    val sqlBase=new MySqlBaseDao()
    sqlBase.executeMany("update db_hitsstatistic.t_clues_realtime set spm=?,province_id =?,city_id =?,clue_resource_id =?,clue_resource_name =? where id=?",result)
  }

}
