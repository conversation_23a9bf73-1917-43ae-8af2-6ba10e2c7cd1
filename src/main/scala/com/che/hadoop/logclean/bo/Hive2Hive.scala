package com.che.hadoop.logclean.bo

import com.che.hadoop.underlay
import com.che.hadoop.underlay.dao.MySparkSession
import com.che.hadoop.underlay.tool.date.DateTranslate
import org.apache.spark.sql.SparkSession

import java.sql.{Connection, DriverManager, SQLException}
import java.util.Properties

/**
 * 将神策hive表中的数据同步到，oss_hdfs中的hive
 * */
object Hive2Hive {
  def main(args: Array[String]): Unit = {
//    TestConn()
    testConnection2()
  }


  def TestConn(): Unit = {
    val jdbcUrl = "***************************************************"
//    val jdbcUrl = "*************************************************************************************************************"
//    val user = "<EMAIL>" // 替换为您的用户名
//    val password = "your_password" // 替换为您的密码
    try {
      // 加载 Hive JDBC 驱动
      Class.forName("org.apache.hive.jdbc.HiveDriver")
      // 获取连接
      val connection: Connection = DriverManager.getConnection(jdbcUrl)
      println("Connected to Hive successfully!")
      // 关闭连接
      connection.close()
    } catch {
      case e: ClassNotFoundException =>
        e.printStackTrace()
      case e: SQLException =>
        e.printStackTrace()
      case e: Exception =>
        e.printStackTrace()
    }
  }

  def testConnection(): Unit = {
    val spark = MySparkSession.conn()
    //    JDBC 连接参数
    val jdbcUrl = "*************************************************************************************************************"
//    测试环境
//    val table_name = "events /*SA(default)*/"
//    正式环境
    val table_name = "events /*SA(production)*/"
    val properties: Properties = new Properties()
    properties.setProperty("driver", "org.apache.hive.jdbc.HiveDriver")
    //    读取 Hive 表
    spark.read.jdbc(url = jdbcUrl ,table = table_name, properties = properties)
      .createOrReplaceTempView("t_evens_view")
//    spark.sql("show create table t_evens_view").show(false)
    val df = spark.sql(
              s"""
                 |select * from t_evens_view
                 |where `date` between
                 |FROM_UNIXTIME(UNIX_TIMESTAMP('20240911', 'yyyyMMdd'))
                 |and
                 |FROM_UNIXTIME(UNIX_TIMESTAMP('20240912', 'yyyyMMdd'))
                 |""".stripMargin)
    df.write.mode("append")
      .format("hive")
      .partitionBy("date", "$lib")
      .saveAsTable("events")

    spark.stop()
  }

  def testConnection2(): Unit = {
    val spark = MySparkSession.conn()
    //    JDBC 连接参数
    val jdbcUrl = "*************************************************************************************************************"
    //    测试环境
    //    val table_name = "events /*SA(default)*/"
    //    正式环境
    val table_name = "events /*SA(production)*/"
    val properties: Properties = new Properties()
    properties.setProperty("driver", "org.apache.hive.jdbc.HiveDriver")
    //    读取 Hive 表
    spark.read.jdbc(url = jdbcUrl, table = table_name, properties = properties)
      .createOrReplaceTempView("t_evens_view")
    //    spark.sql("show create table t_evens_view").show(false)
    DateTranslate.getDatesBetween("20240801", "20240910").reverse.foreach(day => {
      println(s"计算日期：$day")
      val df = spark.sql(
        s"""
           |select * from t_evens_view
           |where `date`=FROM_UNIXTIME(UNIX_TIMESTAMP('$day', 'yyyyMMdd'))
           |""".stripMargin)
      df.write.mode("append")
        .format("hive")
        .partitionBy("date", "$lib")
        .saveAsTable("events")
    })

    spark.stop()
  }

}
