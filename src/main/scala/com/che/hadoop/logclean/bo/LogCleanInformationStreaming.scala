package com.che.hadoop.logclean.bo

import org.apache.spark.sql.functions._
import com.che.hadoop.underlay.dao.{MyKudu, MySparkSession, MySqlBaseDao}
import com.che.hadoop.underlay.tool.date.DateTranslate
import com.che.hadoop.underlay.tool.http.MyHttpService
import org.apache.spark.sql.SparkSession

import java.text.SimpleDateFormat
import java.util.{Calendar, Date, ResourceBundle}

/**
 * 从日志中清洗文章明细
 * 定时任务中
 */
object LogCleanInformationStreaming {

  def main(args: Array[String]): Unit = {
    val spark=MySparkSession.conn()
    val sqlBase=new MySqlBaseDao("che_test")
    val myKudu=new MyKudu(spark)
    DateTranslate.getDatesBetween("20240313","20240313").foreach(day=>{
      pushArticleExposure(spark,myKudu,sqlBase,day)
  })

//    dwdInformationLog(spark,sqlBase)
//    spark.stop()
  }

  /**
   * 从日志钟清洗文章明细数据
   */
  def dwdInformationLog(spark:SparkSession, sqlBase:MySqlBaseDao): Unit ={
    spark.sparkContext.setLogLevel("WARN")
    //1、从mysql中获取最大时间戳,读取kudu数据并创建临时表
    val maxTimestamp = sqlBase.executeQuery("select max(max_timestamp) as timestamp from t_max_timestamp where data_name='infomation_streaming'", null)
    maxTimestamp.next()
    val time = maxTimestamp.getString("timestamp")
    val day= DateTranslate.tranTimeStampToString(time.toLong,"yyyyMMdd")
    val myKudu = new MyKudu(spark)
    myKudu.select("t_logdata_streaming").where(s"day>=FROM_UNIXTIME($time/1000,'yyyyMMdd') and daytime>='$time' and ip <>'************'").createOrReplaceTempView("t_logdata_view")
    myKudu.select("t_logdata_appsc_streaming2").where(s"day>=FROM_UNIXTIME($time/1000,'yyyyMMdd') and recv_time>='$time'").createOrReplaceTempView("t_logdata_appsc_view")
    myKudu.select("t_logdata_mpsc_streaming").where(s"day>=FROM_UNIXTIME($time/1000,'yyyyMMdd') and recv_time>='$time'").createOrReplaceTempView("t_logdata_mpsc_view")
    /*01资讯相关数据*/
    //web端
//    spark.sql(
//      s"""
//         |select distinct id as `offset`,vid  ,day,'pageview' as eventid,uid,
//         |CASE
//         |					    WHEN lower(k) rlike 'articleid=([0-9]+)' THEN regexp_extract(LOWER(k) , 'articleid=([0-9]+)', 1)
//         |					    WHEN lower(k) rlike 'videoid=([0-9]+)' THEN regexp_extract(LOWER(k), 'videoid=([0-9]+)', 1)
//         |					    WHEN lower(k) rlike 'liveid=([0-9]+)' THEN regexp_extract(LOWER(k), 'liveid=([0-9]+)', 1)
//         |					    WHEN lower(k) rlike '(www|m).360che.com/(news|market|driver|tech|law)/[0-9]+/([0-9]+)' THEN regexp_extract(LOWER(k), '(www|m).360che.com/(news|market|driver|tech|law)/[0-9]+/([0-9]+)', 3)
//         |					    WHEN lower(k) rlike '(www|m).360che.com/v/v_([0-9]+)' THEN regexp_extract(LOWER(k), '(www|m).360che.com/v/v_([0-9]+)', 2)
//         |					    WHEN lower(k) rlike 'tu.360che.com/(news|m)/([0-9]+)' THEN regexp_extract(LOWER(k), 'tu.360che.com/(news|m)/([0-9]+)', 2)
//         |					    WHEN lower(k) rlike 'app.360che.com/share_picture/([0-9]+)' THEN regexp_extract(LOWER(k), 'app.360che.com/share_picture/([0-9]+)', 1)
//         |					    WHEN lower(k) rlike '(www|m).360che.com/v/v_([0-9]+)' THEN regexp_extract(LOWER(k), '(www|m).360che.com/v/v_([0-9]+)', 2)
//         |					    WHEN lower(k) rlike 'id=([0-9]+)' THEN regexp_extract(LOWER(k) , 'id=([0-9]+)', 1)
//         |ELSE -1 end as `label`,
//         |case when code in ('11102100110000','12103100110000','15107100110000','13102100110000','14102100110000')
//         |		then 2
//         |	when code in ('11102100210000','12103100210000','15107100210000')
//         |	    then 5
//         |	when code in ('11102100310000','12103100310000','15107100310000','13102100310000','14102100310000')
//         |		then 20
//         |  when code in ('11108100010000','12117100010000','15119100010000')
//         |     then 13
//         |    end as typeid,
//         |case when code rlike '^11' then 11
//         |	when code rlike '^12' then 12
//         |	when code rlike '^13' then 13
//         |	when code rlike '^14' then 14
//         |	when code rlike '^15' then 15
//         |	end as client,daytime as recv_time
//         |from t_logdata_view
//         |where code in ('11102100110000','11102100210000','11102100310000','12103100110000','12103100210000','12103100310000','15107100110000',
//         |'15107100210000','15107100310000','13102100110000','13102100310000','14102100110000','14102100310000','11108100010000','12117100010000','15119100010000')
//         |and eventid=''
//         |""".stripMargin).createOrReplaceTempView("t_websc_info")
    spark.sql(
      s"""
         |select distinct id as `offset`, vid ,day,'pageview' as eventid,daytime,ip,uid,sid,i,vtc,vtl,
         |CASE
         |					    WHEN lower(k) rlike 'articleid=([0-9]+)' THEN regexp_extract(LOWER(k) , 'articleid=([0-9]+)', 1)
         |					    WHEN lower(k) rlike 'videoid=([0-9]+)' THEN regexp_extract(LOWER(k), 'videoid=([0-9]+)', 1)
         |					    WHEN lower(k) rlike 'liveid=([0-9]+)' THEN regexp_extract(LOWER(k), 'liveid=([0-9]+)', 1)
         |					    WHEN lower(k) rlike '(www|m).360che.com/(news|market|driver|tech|law)/[0-9]+/([0-9]+)' THEN regexp_extract(LOWER(k), '(www|m).360che.com/(news|market|driver|tech|law)/[0-9]+/([0-9]+)', 3)
         |					    WHEN lower(k) rlike '(www|m).360che.com/v/v_([0-9]+)' THEN regexp_extract(LOWER(k), '(www|m).360che.com/v/v_([0-9]+)', 2)
         |					    WHEN lower(k) rlike 'tu.360che.com/(news|m)/([0-9]+)' THEN regexp_extract(LOWER(k), 'tu.360che.com/(news|m)/([0-9]+)', 2)
         |					    WHEN lower(k) rlike 'app.360che.com/share_picture/([0-9]+)' THEN regexp_extract(LOWER(k), 'app.360che.com/share_picture/([0-9]+)', 1)
         |					    WHEN lower(k) rlike '(www|m).360che.com/v/v_([0-9]+)' THEN regexp_extract(LOWER(k), '(www|m).360che.com/v/v_([0-9]+)', 2)
         |				      WHEN lower(k) rlike 'm.360che.com/wx-share/(article|video)' THEN regexp_extract(LOWER(k), 'm.360che.com/wx-share/(article|video).*html\\\\?id=([0-9]+)', 2)
         |              WHEN lower(k) rlike 'id=([0-9]+)' THEN regexp_extract(LOWER(k) , 'id=([0-9]+)', 1)
         |              ELSE -1
         |end as `label`,
         |case when  k rlike '(http|https)://((www.360che.com/(news|market|driver|tech|law)/[0-9]+/[0-9]+)|(m.360che.com/(news|market|driver|tech|law)/[0-9]+/[0-9]+).html|(m.360che.com/weixin/(article|SpeedArticle).aspx+)|(app.360che.com/app/ArticleNew/Article.aspx+)|(news-app.360che.com/article.html+)|(m.360che.com/wx-share/article))'
         |		  then 2
         |	   when k rlike '(http|https)://(((www.360che.com/v/v_[0-9]+)|(m.360che.com/v/v_[0-9]+)).html|(m.360che.com/appvideo/(VideoInfo|VideoShare|v/info|quickshare).aspx+)|(news-app.360che.com/video.html+)|(m.360che.com/wx-share/video))'
         |	    then 5
         |	   when k rlike '(http|https)://(((tu.360che.com/news/[0-9]+)|(tu.360che.com/m/[0-9]+)|(app.360che.com/share_picture/[0-9]+)).html|(api.360che.com/ImgChannel/app/ImageInfo.aspx+))'
         |		  then 20
         |     when k rlike 'https?://live-h5.360che.com'
         |      then 13
         |     when k rlike '(news-app.360che.com/new/experience/article.html|s.kcimg.cn/landing/experience/detail.html)\\\\?id=[0-9]+&articleId=[0-9]+'
         |      then 26
         |     when k rlike 's.kcimg.cn/landing/experience/detail.html\\\\?id=[0-9]+&videoId=[0-9]+'
         |      then 27
         |    end as typeid,
         |case when ua like '%Windows%' or ua like '%Macintosh%' then 11
         |       when ua like '%MicroMessenger%' then 15
         |       when ua not like '%MicroMessenger%' and ua not like '%360CHE%' and ua not like '%Windows%' and ua not like '%Macintosh%' then 12
         |       when ua like '%iPhone%' and ua like '%360CHE%'then 13
         |       when ua like '%Android%' and ua like '%360CHE%' then 14
         |	end as client,daytime as recv_time
         |from t_logdata_view
         |where eventid ='' and
         |( k rlike '(http|https)://((www.360che.com/(news|market|driver|tech|law)/[0-9]+/[0-9]+)|(m.360che.com/(news|market|driver|tech|law)/[0-9]+/[0-9]+).html|(m.360che.com/weixin/(article|SpeedArticle).aspx+)|(app.360che.com/app/ArticleNew/Article.aspx+)|(news-app.360che.com/article.html+))'
         |or k rlike '(http|https)://(((www.360che.com/v/v_[0-9]+)|(m.360che.com/v/v_[0-9]+)).html|(m.360che.com/appvideo/(VideoInfo|VideoShare|v/info|quickshare).aspx+)|(news-app.360che.com/video.html+))'
         |or k rlike '(http|https)://(((tu.360che.com/news/[0-9]+)|(tu.360che.com/m/[0-9]+)|(app.360che.com/share_picture/[0-9]+)).html|(api.360che.com/ImgChannel/app/ImageInfo.aspx+))'
         |or k rlike 'https?://live-h5.360che.com'
         |or k rlike 'https?://s.kcimg.cn/landing/experience/detail.html\\\\?id=[0-9]+&(articleId|videoId)=[0-9]+'
         |or k rlike 'https?://news-app.360che.com/new/experience/article.html\\\\?id=[0-9]+&articleId=[0-9]+'
         |or k rlike 'https://m.360che.com/wx-share/(article|video)'
         |)
         |""".stripMargin).createOrReplaceTempView("t_websc_info")
    //app端视频
    spark.sql(
      s"""
         |select concat("appsc#",`offset`) as `offset`,'pageview' as eventid,label,cid as vid,5 as typeid,day,
         |if(os='iOS',13,14) as client,recv_time
         |from t_logdata_appsc_view where event='AppViewScreen' and type_id=5 and title='视频详情页'
         |and label rlike '^[0-9]+$$'
         |union
         |select concat("appsc#",`offset`) as `offset`,'pageview' as eventid,regexp_extract(label,'[0-9]+\\\\|([0-9]+)',1) as label,cid as vid,27 as typeid,day,
         |if(os='iOS',13,14) as client,recv_time
         |from t_logdata_appsc_view where event='AppViewScreen' and type_id=27 and title='经验视频详情页'
         |and label rlike '^[0-9]+\\\\|[0-9]+'
         |""".stripMargin).createOrReplaceTempView("t_appsc_video")
    //计算直播的pv
    spark.sql(
      s"""
         |select concat("appsc#",`offset`) as `offset`,'pageview' as eventid,label,cid as vid,13 as typeid,day,
         |if(os='iOS',13,14) as client,recv_time
         |from t_logdata_appsc_view where title ="直播详情页" and event ="AppViewScreen"
         |and type_id ="13" and label rlike'^[0-9]*$$'
         |""".stripMargin).createOrReplaceTempView("t_appsc_zhibo")
    //微信小程序文章和视频数据
    spark.sql(
      s"""
         |select concat("mpsc#",`offset`) as `offset`,'pageview' as eventid,label,anonymous_id as vid, cast(type_id as int) as typeid,day,16 as client,recv_time
         |from t_logdata_mpsc_view where event='on_mpclick' and eventid='v_page' and type_id in ('5', '2') and label rlike '\\\\d+'
         |union all
         |select concat("mpsc#",`offset`) as `offset`,'pageview' as eventid,regexp_extract(LOWER(url_query) , '(?:articleid|videoid)=([1-9][0-9]*)', 1) AS label,anonymous_id as vid, if(LOWER(url_query) rlike 'articleid',2,5) as typeid,day,16 as client,recv_time
         |from t_logdata_mpsc_view where event='MPViewScreen'
         |and LOWER(url_path) rlike 'pages/experience/detail/(articledetail|videodetail)'
         |and LOWER(url_query) rlike '(articleid|videoid)=([1-9][0-9]*)'
         |""".stripMargin).createOrReplaceTempView("t_mpsc_info")
    /*02论坛相关数据*/
    val bbsUrlPattern=sqlBase.getDfTable("(select value from db_hitsstatistic.t_url_rule where id=6) as t",spark).first().getAs[String]("value").replace("\\","\\\\")
    //web端
    spark.sql(
      s"""
         |select id as `offset`,'pageview' as eventid,
         |  regexp_extract(regexp_extract(k, 'thread-(\\\\d+)|tid=(\\\\d+)', 0),'(\\\\d+)',0)  AS label,
         |  vid,9 as typeid,day,
         | case when code rlike '^11' then 11
         |	when code rlike '^12' then 12
         |	when code rlike '^13' then 13
         |	when code rlike '^14' then 14
         |	when code rlike '^15' then 15
         |	end as client ,daytime as recv_time
         |from t_logdata_view where eventid =''
         |and  k rlike '$bbsUrlPattern'
         |""".stripMargin).createOrReplaceTempView("t_websc_bbs")
    //app原生
    spark.sql(
      s"""
         |select concat("appsc#",`offset`) as `offset`,'pageview' as eventid,
         |label,
         |cid as vid,cast(type_id as int) as typeid,day,
         |if(os='iOS',13,14) as client,recv_time
         |from t_logdata_appsc_view where event ="AppViewScreen"
         |and type_id in('3','4') and label rlike'^[0-9]*$$'
         |""".stripMargin).createOrReplaceTempView("t_appsc_bbs")
    spark.sql(
      s"""
         |select concat("mpsc#",`offset`) as `offset`,'pageview' as eventid,regexp_extract(regexp_extract(url_query, 'id=(\\\\d+)', 0),'(\\\\d+)',0) AS label,anonymous_id as vid, 9 as typeid,16 as client,day,recv_time
         |from t_logdata_mpsc_view where event='MPViewScreen' and url_path regexp 'pages/Bbs/ThreadDetail'
         |""".stripMargin).createOrReplaceTempView("t_mpsc_bbs")
    /*分享相关数据*/
    spark.sql(
      s"""select concat("appsc#",`offset`) as `offset`,'ck_share' as eventid,
         |label,cid as vid,cast(type_id as int) as typeid,day,
         |if(os='iOS',13,14) as client,recv_time
         |from t_logdata_appsc_view where event ="on_appclick" and eventid='ck_share'
         |and type_id in('2','5','3','4','9','13') and label rlike'^[0-9]*$$'
         |union
         |select concat("mpsc#",`offset`) as `offset`,'ck_share' as eventid,label,anonymous_id as vid, cast(type_id as int) as typeid,day,16 as client,recv_time
         |from t_logdata_mpsc_view where event='on_mpclick' and eventid ='ck_share' and type_id in ('2','5','3','4','9','13') and label rlike'^[0-9]*$$'
         |""".stripMargin).createOrReplaceTempView("t_share")
    val resultDF =spark.sql(
      """
        |-- websc pc,m,wx的文章和视频、直播，app的文章
        |select eventid,cast(label as int) as label,vid,typeid,client,day,recv_time,`offset` from t_websc_info
        |union all
        |-- appsc的视频
        |select eventid,cast(label as int) as label,vid,typeid,client,day,recv_time,`offset` from t_appsc_video
        |union all
        |-- appsc的直播
        |select eventid,cast(label as int) as label,vid,typeid,client,day,recv_time,`offset` from t_appsc_zhibo
        |union all
        |-- 微信小程序资讯
        |select eventid,cast(label as int) as label,vid,typeid,client,day,recv_time,`offset` from t_mpsc_info
        |--websc bbs
        |union all
        |select eventid,cast(label as int) as label,vid,typeid,client,day,recv_time,`offset` from t_websc_bbs
        |--appsc bbs
        |union all
        |select eventid,cast(label as int) as label,vid,typeid,client,day,recv_time,`offset` from t_appsc_bbs
        |--mpsc bbs
        |union all
        |select eventid,cast(label as int) as label,vid,typeid,client,day,recv_time,`offset` from t_mpsc_bbs
        |--分享相关数据(app和小程序端)
        |union all
        |select eventid,cast(label as int) as label,vid,typeid,client,day,recv_time,`offset` from t_share
        |""".stripMargin).cache()
    resultDF.cache()
    //3、upsert方式写进kudu表中（t_information_log_streaming）
    myKudu.upsert(resultDF, "t_information_log_streaming")
    pushtoEdit(spark,myKudu,sqlBase,day)
    pushArticleExposure(spark, myKudu, sqlBase, day)
    //4、获取更新数据的最大时间戳，为下次计算做准备(后退10分钟)
    val newTime = (resultDF.agg(max("recv_time")).head().getAs[String](0).toLong - 10*60000).toString
    sqlBase.execute(s"update t_max_timestamp set max_timestamp=$newTime,day=FROM_UNIXTIME($newTime/1000,'%Y%m%d') where data_name='infomation_streaming'")
    resultDF.unpersist() // 清除缓存
  }

  def pushtoEdit(spark: SparkSession,myKudu:MyKudu,sqlBase:MySqlBaseDao,day:String): Unit = {
//    kudu表缓存创建临时视图
    myKudu.select("t_information_log_streaming").createOrReplaceTempView("t_information_log_streaming_view2")
    myKudu.select("t_logdata_streaming").createOrReplaceTempView("t_logdata_view2")
    myKudu.select("t_logdata_appsc_streaming2").createOrReplaceTempView("t_logdata_appsc_view2")
    val dayBefore3=DateTranslate.getSpecifyDate(day,"yyyyMMdd",2,Calendar.DATE)
    val lableValue = "^[1-9][0-9]*$" //正则验证
    // 计算视频的完播web端
    spark.sql(
      s"""
         |select vid,lable,day from t_logdata_view2
         |where day='$day' and eventid ="v_video_end" and lable rlike '$lableValue'
         |""".stripMargin).createOrReplaceTempView("t_video_end")
    //计算视频的完播app端
    spark.sql(
      s"""
         |select cid,label,day FROM t_logdata_appsc_view2
         |WHERE day='$day' and event='on_appclick' and eventid='v_video_end'
         |and label rlike '$lableValue'
         |""".stripMargin).createOrReplaceTempView("t_video_end_appsc")
    //    数据源直接读取dwdInformationLog
    spark.sql(
      s"""

         |select articleid as id,typeid as `type`,day
         |,sum(case when billtype=11 then pv  else 0 end) as  pc_pv
         |,sum(case when billtype=12 then pv  else 0 end) as  m_pv
         |,0 as  ios_pv
         |,0 as  and_pv
         |,sum(case when billtype=13 then pv  else 0 end ) as  iosches_pv
         |,sum(case when billtype=14 then pv  else 0 end ) as  andches_pv
         |,sum(case when billtype=15 then pv  else 0 end ) as  wx_pv
         |,sum(case when billtype=16 then pv  else 0 end ) as  mp_pv
         |,sum(case when billtype=15 then uv  else 0 end ) as  wx_uv
         |,0 as  wx_ip
         |,0 as  ios_uv
         |,0 as  and_uv
         |,sum(case when billtype=11 then uv  else 0 end ) as  pc_uv
         |,sum(case when billtype=12 then uv  else 0 end ) as  m_uv
         |,sum(case when billtype=13 then uv  else 0 end ) as  iosches_uv
         |,sum(case when billtype=14 then uv  else 0 end ) as  andches_uv
         |,sum(case when billtype=16 then uv  else 0 end ) as  mp_uv
         |,sum(case when billtype in (11,12,13,14,15,16) then pv else 0 end) as all_pv
         |,sum(case when billtype in (11,12,13,14,15,16) then uv else 0 end) as all_uv
         |,sum(case when billtype=19 then pv  else 0 end ) as  video_end
         |from (
         |select label as articleid,`day`,count(1) as pv,count(distinct vid) as uv,client as billtype,case typeid when '2' then '1' when '5' then '2' when '26' then '5' when '27' then '6' end as typeid
         |from t_information_log_streaming_view2 where day='$day' and  eventid='pageview' and label rlike '$lableValue'
         |and typeid in ('2','5','26','27')
         |group by label,day,client,typeid
         |union all
         |select lable as articleid,day,count(1) as pv,count(distinct vid) as uv,19 as billtype,2 as typeid
         |from t_video_end
         |group by day, lable
         |union all
         |SELECT label as articleid,`day`,count(1) as pv,count(distinct cid) as uv,19 as billtype,2 as typeid
         |FROM t_video_end_appsc
         |group by label,day
         |) as a group by articleid,typeid,day """.stripMargin).createOrReplaceTempView("t_PVUV")

    //从mysql中把文章视频发布的信息取出来
    val articlepublish =
      s"""
         |select distinct  CAST(articleid  as char) as articleid from t_ba_article tba
         |where  publishtime BETWEEN DATE_SUB("$day", INTERVAL 2 DAY)
         |and DATE_ADD("$day", INTERVAL 1 DAY) and billType in ( "article_t_article","article_t_video")
         |""".stripMargin
    sqlBase.getCacheTable("t_articlepublish_tmp", s"($articlepublish) as t", spark)

    //从数据库中把三天内的文章视频数据查询出来
    spark.sql(
      s"""
         |select cid , uid,event,nvl(screen_name,'') as screen_name,title,type_id,label,action,os,time,event_duration,day
         |from t_logdata_appsc_view2 where day >='$dayBefore3' and
         |type_id in ("2","5") and event in ('CHES_00000001','AppClick','CHES_00000002') and  label!=''  and label rlike '^[0-9]*$$'
         |union
         |select cid , uid,event,nvl(screen_name,'') as screen_name,title,type_id,regexp_extract(label,'[0-9]+\\\\|([0-9]+)',1) as label,action,os,time,event_duration,day
         |from t_logdata_appsc_view2 where day >='$dayBefore3' and
         |type_id in ("26","27") and event in ('CHES_00000001','AppClick','CHES_00000002') and  label!=''  and label rlike '^[0-9]+\\\\|[0-9]+$$'
         |""".stripMargin).createOrReplaceTempView("t_article_tmp")
    // 得到近三天发布文章视频的情况
    spark.sql(
      s"""
         |select cid , uid,event,screen_name,title,type_id,label,action,os,time,event_duration,day
         |from t_article_tmp t1 inner join t_articlepublish_tmp t2 on t1.label = t2.articleid
         |""".stripMargin).createOrReplaceTempView("t_article_tmp2")
    spark.catalog.cacheTable("t_article_tmp2")
    //计算近三天发布文章的单篇的CTR
    spark.sql(
      s"""
         |select
         |	sum(if(event='CHES_00000001',num,0)) as f_puv,
         |	sum(if(event='AppClick',num,0)) as f_euv,
         |	nvl((sum(if(event='AppClick',num,0))/sum(if(event='CHES_00000001',num,0))),0)  as ctr,
         | a.label,
         | a.type
         |from (
         |	select
         |		count(distinct cid,label) as num ,event,label,`day`,
         |    (case when type_id = "2" then 1
         |         when type_id = "5" then 2
         |         when type_id = "26" then 5
         |         when type_id = "27" then 6
         |         end ) as type
         |	from  t_article_tmp2
         |	where  type_id in ("2","5","26","27")
         |		and event in('CHES_00000001','AppClick')
         |	group by event,os,`day`,label,type_id) a
         |group by a.label,a.type
         |""".stripMargin).createOrReplaceTempView("t_realTimeCTR")
    spark.catalog.cacheTable("t_realTimeCTR")
    //计算进三天发布文章的单篇停留时长
    spark.sql(
      s"""
         |select label, cid ,`day`,
         |	if(event_duration > 900,180,event_duration) as event_duration,
         | (case when type_id = "2" then 1
         |     when type_id = "5" then 2
         |     end ) as type
         |from  t_article_tmp2
         |where  event ="CHES_00000002"
         |and type_id in ("2","5")
         |and label!=''  and label rlike '^[0-9]*$$'
         |union
         |select regexp_extract(label,'[0-9]+\\\\|([0-9]+)',1) as label, cid ,`day`,
         |	if(event_duration > 900,180,event_duration) as event_duration,
         | (case when type_id = "26" then 5
         |     when type_id = "27" then 6
         |     end ) as type
         |from  t_article_tmp2
         |where  event ="CHES_00000002"
         |and type_id in ("26","27")
         |and label!=''  and label rlike '^[0-9]+\\\\|[0-9]+$$'
         |""".stripMargin).createOrReplaceTempView("t_tmp")
    spark.catalog.cacheTable("t_tmp")
    spark.sql(
      s"""
         |select
         |	a.label ,
         |	COUNT(DISTINCT cid)  as f_uv,
         |	sum(event_duration) as f_total_time,
         |	nvl(sum(event_duration)/count(DISTINCT cid ),0) as f_avg_time
         | ,type
         |from t_tmp a
         |group by a.label ,a.type
         |""".stripMargin).createOrReplaceTempView("t_realAvgTime")
    spark.catalog.cacheTable("t_realAvgTime")
    spark.sql(
      """
        |select t1.label,
        |if(f_puv is null ,0 ,f_puv) as f_puv,
        |if(f_euv is null ,0 ,f_euv) as f_euv,
        |if(ctr is null ,0 ,ctr) as ctr,
        |if(f_uv is null ,0 ,f_uv) as f_uv,
        |if(f_total_time is  null ,0 ,f_total_time) f_total_time,
        |if(f_avg_time is null ,0 ,f_avg_time) as f_avg_time,t1.type
        |from t_realTimeCTR t1  left join t_realAvgTime t2 on t1.label = t2.label  and t1.type= t2.type
        |""".stripMargin).createOrReplaceTempView("t_realTimeCtr")
    spark.catalog.cacheTable("t_realTimeCtr")
    //        println("得到停留时长和ctr表")
    //结果数据
    val df = spark.sql(
      """
        |select
        |t1.id ,t1.day ,
        |if(pc_pv is null ,0 ,pc_pv) as pc_pv,
        |if(m_pv is null ,0 ,m_pv) as m_pv,
        |if(ios_pv is null ,0 ,ios_pv) as ios_pv,
        |if(and_pv is null ,0 ,and_pv) as and_pv,
        |if(iosches_pv is null ,0 ,iosches_pv) as iosches_pv,
        |if(andches_pv is null ,0 ,andches_pv) as andches_pv,
        |if(wx_pv is null ,0 ,wx_pv) as wx_pv,
        |if(mp_pv is null ,0 ,mp_pv) as mp_pv,
        |if(mp_uv is null ,0 ,mp_uv) as mp_uv,
        |if(wx_uv is null ,0 ,wx_uv) as wx_uv,
        |if(wx_ip is null ,0 ,wx_ip) as wx_ip,
        |if(pc_uv is null ,0 ,pc_uv) as pc_uv,
        |if(m_uv is null ,0 ,m_uv) as m_uv,
        |if(ios_uv is null ,0 ,ios_uv) as ios_uv,
        |if(and_uv is null ,0 ,and_uv) as and_uv,
        |if(iosches_uv is null ,0 ,iosches_uv) as iosches_uv,
        |if(andches_uv is null ,0 ,andches_uv) as andches_uv,
        |if(video_end is null ,0 ,video_end) as video_end,
        |if(all_pv is null ,0 ,all_pv) as all_pv,
        |if(all_uv is null ,0 ,all_uv) as all_uv,
        |if(f_puv is null ,0 ,f_puv) as f_puv,
        |if(f_euv is null ,0 ,f_euv) as f_euv,
        |if(ctr is null ,0 ,ctr) as ctr,
        |if(f_uv is null ,0 ,f_uv) as  f_uv,
        |if(f_total_time is  null ,0 ,f_total_time) as f_total_time,
        |if(f_avg_time is null ,0 ,f_avg_time) as f_avg_time
        |,t1.type
        |from t_PVUV t1   left  join t_realTimeCtr t2  on t1.id = t2.label and t1.type= t2.type having id <>"" and id is not null
        |""".stripMargin)
    val text = "{\"status\":\"1\",\"msg\":\"success\",\"data\":" + df.toJSON.collectAsList().toString.trim + "}"
    try {
      println(new SimpleDateFormat("yyyyMMdd hh:mm:ss").format(new Date)+"接口开始推送")
      val reader=ResourceBundle.getBundle("connection_underlay")
      val url=reader.getString("pushNewsEditRealTime")
      val response=MyHttpService.GetHttpPost2(url, text.replace(", ", ","))
      println(new SimpleDateFormat("yyyyMMdd hh:mm:ss").format(new Date)+s"接口推送成功--$response")
    }
    catch {
      case e: Exception =>
        e.printStackTrace()
    }
    df.unpersist()
  }


  /***
   * 推送文章曝光量
   */
  def pushArticleExposure(spark: SparkSession,myKudu:MyKudu,sqlBase:MySqlBaseDao,day:String): Unit = {
//    val dayBefore3=DateTranslate.getSpecifyDate(day,"yyyyMMdd",2,Calendar.DATE)
//    myKudu.select("t_logdata_appsc_streaming2").createOrReplaceTempView("t_logdata_appsc_view2")
//    //从mysql中把文章视频发布的信息取出来
//    val articlepublish =
//      s"""
//         |select distinct  CAST(articleid  as char) as articleid from t_ba_article tba
//         |where  publishtime BETWEEN DATE_SUB("$day", INTERVAL 2 DAY)
//         |and DATE_ADD("$day", INTERVAL 1 DAY) and billType in ( "article_t_article","article_t_video")
//         |""".stripMargin
//    sqlBase.getCacheTable("t_articlepublish_tmp", s"($articlepublish) as t", spark)
//    //从数据库中把三天内的文章视频数据查询出来
//    spark.sql(
//      s"""
//         |select cid , uid,event,nvl(screen_name,'') as screen_name,title,type_id,label,action,os,time,event_duration,day
//         |from t_logdata_appsc_view2 where day >='$dayBefore3' and
//         |type_id in ("2","5") and event in ('CHES_00000001','AppClick','CHES_00000002') and  label!=''  and label rlike '^[0-9]*$$'
//         |""".stripMargin).createOrReplaceTempView("t_article_tmp")
//    // 得到近三天发布文章视频的情况
//    spark.sql(
//      s"""
//         |select cid , uid,event,screen_name,title,type_id,label,action,os,time,event_duration,day
//         |from t_article_tmp t1 inner join t_articlepublish_tmp t2 on t1.label = t2.articleid
//         |""".stripMargin).createOrReplaceTempView("t_article_tmp2")
    val resultStr=spark.sql(
      """
        |select cast(label as int) as id,count(1) as `count` from t_article_tmp2 where type_id='2' and event='CHES_00000001' group by label
        |""".stripMargin).toJSON.collect().mkString("[",",","]")
    println(resultStr)
    val reader = ResourceBundle.getBundle("connection_underlay")
    val url = reader.getString("pushArticleExposure")
    println(url)
    val res=MyHttpService.GetHttpPost2(url,resultStr)
    println(res)
  }
}
