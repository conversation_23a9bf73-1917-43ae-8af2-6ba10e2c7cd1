package com.che.hadoop.logclean.bo.discard

import java.util.Calendar

import com.che.hadoop.logclean.dao.mysql_conn
import com.che.hadoop.underlay.dao.MySparkSession
import org.apache.spark.sql.types.{StringType, StructField, StructType}
import org.apache.spark.sql.{DataFrame, Row}

/**
 *从sqlserver备份库拉取询价线索数据 数据写入hive表t_clues_extend
 *未部署定时任务
 */
object FetchCluesData {

  def main(args: Array[String]): Unit = {
//    fetchData()
    pullData()
  }
  def fetchData(): Unit ={
val spark = MySparkSession.conn("LogClean_FetchCluesData")
    val structType = StructType(Array(
      StructField("id", StringType, true),
      StructField("CluesId", StringType, true),
      StructField("DealerId", StringType, true),
      Struct<PERSON>ield("SubdealerId", StringType, true),
      Struct<PERSON>ield("State", StringType, true),
      <PERSON>ruct<PERSON>ield("BrandId", StringType, true),
      StructField("Relname", StringType, true),
      StructField("Tel", StringType, true),
      StructField("SeriesId", StringType, true),
      StructField("CategoryId", StringType, true),
      StructField("TruckId", StringType, true),
      StructField("TruckName", StringType, true),
      StructField("Createdate", StringType, true),
      StructField("DealtDate", StringType, true),
      StructField("DealtTruckId", StringType, true),
      StructField("DealtTruck", StringType, true)
    ))
    var list = Array[String]()
    val cal: Calendar = Calendar.getInstance()
    val year = cal.get(Calendar.YEAR)
    for (i <- year-1 to year){
      list  = list :+ i.toString
    }
    for ( x <- list ) {
      val sql = "(SELECT cast(id as varchar) as id,cast(CluesId as varchar) as CluesId," +
        "cast(DealerId as varchar) as DealerId,cast(SubdealerId as varchar) as SubdealerId," +
        "cast(State as varchar) as State,cast(BrandId as varchar) as BrandId," +
        "Relname,Tel,cast(SeriesId as varchar) as SeriesId,cast(CategoryId as varchar) as CategoryId," +
        "cast(TruckId as varchar) TruckId,TruckName, CONVERT(varchar,Createdate, 23) as Createdate," +
        "CONVERT(varchar,DealtDate, 23) as DealtDate, cast(DealtTruckId as varchar) as DealtTruckId," +
        " DealtTruck from CluesExtend  where SUBSTRING(CONVERT(varchar,Createdate, 23), 0, 5)='"+x+"' ) as t"
      val LogRdd = getData(mysql_conn.sqlSeverUrl,sql).repartition(5)
      val rowRDD = LogRdd.rdd.map(line => {
        Row(line.getString(0),
          line.getAs[String](1),
          line.getAs[String](2),
          line.getAs[String](3),
          line.getAs[String](4),
          line.getAs[String](5),
          line.getAs[String](6),
          line.getAs[String](7),
          line.getAs[String](8),
          line.getAs[String](9),
          line.getAs[String](10),
          line.getAs[String](11),line.getAs[String](12),
          line.getAs[String](13),
          line.getAs[String](14),
          line.getAs[String](15)
        )

      })
//      MySparkSession.conn.sql(" set mapred.reduce.tasks=4")
      spark.sqlContext.createDataFrame(rowRDD,structType).createOrReplaceTempView("t_clues")
      val insertSql = "INSERT OVERWRITE TABLE t_clues_extend PARTITION (day='"+x+"') select * from t_clues"
      spark.sql(insertSql)
    }

    spark.close()
    //    sqlSever


  }
  def getData(mysqlUrl:String,table:String): DataFrame = {
    val rdd=MySparkSession.conn().read.format("jdbc").option("url",mysqlUrl)
      .option("dbtable",table)
      .option("user",mysql_conn.sqlSeverUser)
      .option("password",mysql_conn.sqlSeverPassword)
      .option("driver","com.microsoft.sqlserver.jdbc.SQLServerDriver")
      .load()
    return rdd
  }

  def pullData(): Unit ={
    val sql = "(SELECT cast(id as varchar) as id,cast(CluesId as varchar) as CluesId," +
      "cast(DealerId as varchar) as DealerId,cast(SubdealerId as varchar) as SubdealerId," +
      "cast(State as varchar) as State,cast(BrandId as varchar) as BrandId," +
      "Relname,Tel,cast(SeriesId as varchar) as SeriesId,cast(CategoryId as varchar) as CategoryId," +
      "cast(TruckId as varchar) TruckId,REPLACE(TruckName, CHAR(10), '') as TruckName, CONVERT(varchar,Createdate, 23) as Createdate," +
      "CONVERT(varchar,DealtDate, 23) as DealtDate, cast(DealtTruckId as varchar) as DealtTruckId," +
      " DealtTruck from CluesExtend  where  Createdate>'2019-01-01' and Createdate<'2019-10-17') as t"
    println(sql)
    val LogRdd = getData(mysql_conn.sqlSeverUrl,sql).repartition(5)
    val rowRDD = LogRdd.rdd.map(line => {
      Row(line.getString(0),
        line.getAs[String](1),
        line.getAs[String](2),
        line.getAs[String](3),
        line.getAs[String](4),
        line.getAs[String](5),
        line.getAs[String](6),
        line.getAs[String](7),
        line.getAs[String](8),
        line.getAs[String](9),
        line.getAs[String](10),
        line.getAs[String](11),line.getAs[String](12),
        line.getAs[String](13),
        line.getAs[String](14),
        line.getAs[String](15)
      )

    })
    val structType = StructType(Array(
      StructField("id", StringType, true),
      StructField("CluesId", StringType, true),
      StructField("DealerId", StringType, true),
      StructField("SubdealerId", StringType, true),
      StructField("State", StringType, true),
      StructField("BrandId", StringType, true),
      StructField("Relname", StringType, true),
      StructField("Tel", StringType, true),
      StructField("SeriesId", StringType, true),
      StructField("CategoryId", StringType, true),
      StructField("TruckId", StringType, true),
      StructField("TruckName", StringType, true),
      StructField("Createdate", StringType, true),
      StructField("DealtDate", StringType, true),
      StructField("DealtTruckId", StringType, true),
      StructField("DealtTruck", StringType, true)
    ))
    MySparkSession.conn().sqlContext.createDataFrame(rowRDD,structType).createOrReplaceTempView("t_clues")
    val insertSql = "INSERT OVERWRITE TABLE t_clues_extend PARTITION (day='2019') select * from t_clues"
    MySparkSession.conn().sql(insertSql)
    MySparkSession.conn().close()
  }



}
