package com.che.hadoop.logclean.bo.discard

import java.util.ResourceBundle

import com.che.hadoop.logclean.utils.DingRobot
import org.apache.spark.sql.SparkSession

/**
 * 孵化失败，t_driver_info数据已经不更新
 * 定时任务解除
 */
object PullDriverInfo {


    def main(args: Array[String]): Unit = {
//        saveDriverToDwd("20200616")
    }

    /**
      * driver数据清洗到dwd层
      *
      * @param day 日期
      */
    def saveDriverToDwd(spark:SparkSession,day: String): Unit = {
      try {
//            val spark: SparkSession = MySparkSession.conn("Logclean_PullBusinessData")
            spark.sql("set spark.sql.shuffle.partitions=1")
            spark.sql(
                s"""
                   |insert overwrite table t_dwd_driver_info
                   |select nvl(a.id, b.id)                   as id,
                   |       nvl(a.userid, b.userid)           as userid,
                   |       nvl(a.userphone, b.userphone)     as userphone,
                   |       nvl(a.username, b.username)       as username,
                   |       nvl(a.usersex, b.usersex)         as usersex,
                   |       nvl(a.userage, b.userage)         as userage,
                   |       nvl(a.drivetype, b.drivetype)     as drivetype,
                   |       nvl(a.driveage, b.driveage)       as driveage,
                   |       nvl(a.createtime, b.createtime)   as createtime,
                   |       nvl(a.`timestamp`, b.`timestamp`) as `timestamp`,
                   |       nvl(a.isdel, b.isdel)             as isdel,
                   |       nvl(a.day, b.day)                 as day
                   |from t_dwd_driver_info a
                   |         full outer join (
                   |    select id,`timestamp`,userid,userphone,username,usersex,
                   |           userage,drivetype,driveage,createtime,isdel,day
                   |    from (SELECT id,`timestamp`,
                   |                 row_number() over (partition by id order by `timestamp` desc) rank,userid,
                   |                 userphone,username, usersex,userage,drivetype,driveage,createtime,
                   |                 case when operationstate = 'delete' then 1 else 0 end as   isdel,day
                   |          from t_driver_info where day = '$day') t
                   |    where t.rank = 1
                   |) b
                   |on a.id = b.id
            """.stripMargin)
        } catch {
            case exception: Exception => {
                println(exception)
                val robot = new DingRobot(ResourceBundle.getBundle("robot").getString("ding.monitor"))
                robot.sendAlert(s"【监控--司机招聘dwd】\n原因：${exception.toString}")

            }

        }
    }


}
