package com.che.hadoop.logclean.utils

/**
  * Desc:利用钉钉机器人发送警报,类初始化传钉钉机器人的地址，调用sendAlert方法发送警报
  *Auther:Lixiaoqian
  */
import scalaj.http.Http

class DingRobot(webhook:String) {
  val webhooks=webhook

  /**
    * 利用钉钉机器人发送警报
    *
    * @param  ccontent 需要发送的消息内容，必填
    *        mobiles 需要@人的手机号（多个以逗号分隔）,默认为空
    *        isAtAll 是否@所有人,默认为false
    */
  def sendAlert(content: String,mobiles:String ="",isAtAll:Boolean=false): Unit = {
    try {
      Http(webhooks)
        .header("Content-Type", "application/json; charset=utf-8")
        .postData(
          s"""
             |{
             |    "msgtype": "text",
             |    "text": {
             |        "content": "$content"
             |    },
             |    "at": {
             |        "atMobiles": [$mobiles
             |        ],
             |        "isAtAll": $isAtAll
             |    }
             |}
           """.stripMargin)
        .asString
    } catch {
      case e: Exception =>
        println(e)
    }
  }
}
object TestDingRobot{
  def main(args: Array[String]): Unit = {
    val myDingRobot=new DingRobot("https://oapi.dingtalk.com/robot/send?access_token=ce480db9940fafdc4cb1ec5253fa1fd9ff5a3f30746faf13adba6c3337eb03ad")
    myDingRobot.sendAlert("hello world")
  }
}
