package com.che.hadoop.logclean.bo

import com.che.hadoop.underlay.dao.{MyKudu, MySparkSession, MySqlBaseDao}
import com.che.hadoop.underlay.tool.date.DateTranslate
import org.apache.spark.sql.SparkSession
import org.apache.spark.sql.functions.col

/**
  * cookies用户唯一标识映射
* */
object UserMap {
    def main(args: Array[String]): Unit = {
//      val day="20230312"
//      val expDay=DateTranslate.getDate(day,365*3,"yyyyMMdd")
//      println(expDay)
      val spark=MySparkSession.conn()
      val sqlBase=new MySqlBaseDao()
      test(spark,sqlBase)
    }


  /**
    * cookies映射userid 切换为神策数据，t_vid表不再维护
    **/
  def cookieUser(spark:SparkSession,exec:MySqlBaseDao,day:String): Unit = {
    val resultTable="t_vid_uid"
    //过期时间
//    val expDay=DateTranslate.getDate(day,365*3,"yyyyMMdd")
    val day90=DateTranslate.getDate(day,90,"yyyyMMdd")
    try {
      spark.conf.set("spark.sql.shuffle.partitions","9")
      exec.getCacheTable("t_recruit_publish_view",
        s"""
          |(select vid,uid,date_format(create_time,"%Y%m%d") as updatetime,cast(unix_timestamp(create_time) as SIGNED) as ts,'Business' as cs
          |from db_userportrait.t_recruit_publish where uid is not null and uid <>''
          |AND date_format(create_time,'%Y%m%d')='$day'
          |)as t
          |""".stripMargin,spark)
      //询价用户关联
      spark.sql(
        s"""
          |select if(vid='',tel,vid) as vid ,b.uid,date_format(create_date,"yyyyMMdd") as updatetime,unix_timestamp(create_date , 'yyyy-MM-dd HH:mm:ss')as ts,'Clues' as cs
          |from
          |(SELECT * from t_inquiry_clue where day ='$day') as a
          |INNER JOIN t_user as b on a.tel=b.sjyz
          |""".stripMargin).createOrReplaceTempView("t_clues_view")
      //用户注册数据关联
      val sqlBase=new MySqlBaseDao()
      sqlBase.getCacheTable("t_tao_clues_view",
        s"""
          |(
          |select vid,TO_BASE64(mobile) as tel from db_userportrait.t_tao_clues where day >='$day90' and vid <> '' group by vid,mobile
          |)t
          |""".stripMargin,spark)
      val resultDF=spark.sql(
        s"""
          |select b.vid ,a.uid,nvl(date_format(from_unixtime(cast(regdate as Int)),"yyyyMMdd"),'19700101') as updatetime,if(regdate is null or regdate='null','0',regdate) as ts,'Register' as cs
          |from
          |(SELECT sjyz,uid,regdate from t_user where cast(if(regdate='null',0,regdate) as Long)>=UNIX_TIMESTAMP('$day', 'yyyyMMdd') and sjyz is not null) as a
          |INNER JOIN (SELECT vid,tel from t_inquiry_clue where day >='$day90' and vid <> '' group by vid,tel
          |            union
          |            SELECT sjyz AS vid,sjyz as tel from t_user where cast(regdate as Long)>=UNIX_TIMESTAMP('$day', 'yyyyMMdd') and sjyz is not null  and sjyz<>''
          |            union
          |            SELECT vid,tel from t_tao_clues_view
          |            ) as b on a.sjyz=b.tel
          |-- 用户注册时后的unionid信息作为vid
          |union
          |select unionid as vid ,uid,nvl(date_format(from_unixtime(cast(regdate as Int)),"yyyyMMdd"),'19700101') as updatetime,if(regdate is null or regdate='null','0',regdate) as ts,'Register' as cs
          |from t_user where unionid <> ''
          |""".stripMargin)
      resultDF.createOrReplaceTempView("t_register_view")
      val myKudu=new MyKudu(spark)
      myKudu.upsert(resultDF.withColumn("uid", col("uid").cast("string")),"t_vid_uid_streaming")
      spark.sql(
        s"""
           |select vid,uid,cs,max(updatetime) as updatetime,max(ts) as ts from (
           |-- 神策web
           |select vid,uid,day as updatetime, vtc as ts,'bbs' as cs
           |from t_logdata_websc where day='$day' and uid rlike '^[1-9][0-9]*$$'
           |and vid rlike '^[0-9]*$$'
           |-- 神策app
           |union all
           |SELECT cid as vid,uid,day as updatetime ,substr(`time`,0,10) as ts,if(os='iOS','iOS','android') as cs from t_ods_appsc_log where day='$day' and uid rlike '^[1-9][0-9]*$$' and cid <>''
           |-- 小程序
           |union all
           |SELECT anonymous_id as vid,uid,day as updatetime,substr(`time`,0,10) as ts,'MiniProgram' as cs from t_ods_miniprogram_log where day='$day' and uid rlike '^[1-9][0-9]*$$' and anonymous_id <>''
           |-- 司机招聘业务数据
           |union all
           |select vid,uid,updatetime,ts,cs from t_recruit_publish_view
           |--询价数据
           |union all
           |select vid,uid,updatetime,ts,cs from t_clues_view
           |--用户注册数据
           |union all
           |select vid,uid,updatetime,ts,cs from t_register_view
           |-- t_vid_uid历史数据
           |union all
           |select vid,uid,updatetime,if(ts='null',0,ts) as ts,cs from $resultTable
           |) as a
           |group by vid,uid,cs
           |""".stripMargin).createOrReplaceTempView("t_vid_uid_tmp")
      spark.sql(
        s"""
           |insert overwrite table $resultTable PARTITION(cs)
           |select vid,uid,updatetime,ts,cs from t_vid_uid_tmp where uid <>''
           |""".stripMargin)
      exec.executeInsertOrUpdate(s"update t_check set flag=1, message='$day:运行成功' where name='aliyun_t_vid_uid'",null)
    } catch {
      case e: Exception => {
        e.printStackTrace()
        try{
          exec.executeInsertOrUpdate(s"update t_check set flag=2, message='$day:运行失败' where name='aliyun_t_vid_uid'",null)
        }catch {
          case e:Throwable=>{
            e.printStackTrace()
          }
        }
      }
    }
  }


  def test(spark:SparkSession,exec:MySqlBaseDao): Unit ={
    val resultTable="t_vid_uid"
    val resultTable2="t_vid_uid"
    val sqlBase=new MySqlBaseDao()
    sqlBase.getCacheTable("t_tao_clues_view",
      s"""
         |(
         |select vid,TO_BASE64(mobile) as tel from db_userportrait.t_tao_clues where vid <> '' group by vid,mobile
         |)t
         |""".stripMargin, spark)
    //用户注册数据关联
    val resultDF=spark.sql(
      s"""
         |select b.vid ,a.uid,nvl(date_format(from_unixtime(cast(regdate as Int)),"yyyyMMdd"),'19700101') as updatetime,if(regdate='null','0',regdate) as ts,'Register' as cs
         |from
         |(SELECT sjyz,uid,regdate from t_user where sjyz is not null) as a
         |INNER JOIN (SELECT vid,tel from t_inquiry_clue where day >='20230901' and vid <> '' group by vid,tel
         |            union
         |            SELECT sjyz AS vid,sjyz as tel from t_user where sjyz is not null and sjyz<>''
         |            union
         |            SELECT vid,tel from t_tao_clues_view
         |            ) as b on a.sjyz=b.tel
         |""".stripMargin)
//      .show(false)
    resultDF.createOrReplaceTempView("t_register_view")
    val myKudu = new MyKudu(spark)
    myKudu.upsert(resultDF.withColumn("uid", col("uid").cast("string")), "t_vid_uid_streaming")
    spark.sql(
      s"""
         |select vid,uid,cs,max(updatetime) as updatetime,max(ts) as ts from (
         |--用户注册数据
         |select vid,uid,updatetime,ts,cs from t_register_view
         |-- t_vid_uid历史数据
         |union all
         |select vid,uid,updatetime,ts,cs from $resultTable
         |) as a
         |group by vid,uid,cs
         |""".stripMargin).createOrReplaceTempView("t_vid_uid_tmp")
    spark.sql(
      s"""
         |insert overwrite table $resultTable2 PARTITION(cs)
         |select vid,uid,updatetime,ts,cs from t_vid_uid_tmp where uid <>''
         |""".stripMargin)
  }

  def test2(): Unit = {
    val spark=MySparkSession.conn()
    val myKudu=new MyKudu(spark)
    myKudu.select("t_vid_uid_streaming").createOrReplaceTempView("t_vid_uid_view")
    spark.sql(
      s"""
        |insert overwrite table t_vid_uid PARTITION(cs)
        |select vid,uid,updatetime,ts,cs from t_vid_uid_view
        |""".stripMargin)
  }
}
