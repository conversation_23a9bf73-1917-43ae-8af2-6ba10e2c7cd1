package com.che.hadoop.logclean.bo.discard

import com.che.hadoop.underlay.dao.{MySparkSession, MyKudu,MySqlBaseDao}
import org.apache.spark.Partitioner
import org.apache.spark.sql.DataFrame
/**
 * Dmid任务废弃，已不再调用
 */
object DmIdMap {
  val spark = MySparkSession.conn()
  val sqlBase = new MySqlBaseDao()

  val dmIdTable = "t_dwd_dmid"
  val dmIdChangeTable = "t_dwd_dmid_change"
  val appLogTable = "db_test.t_logdata_app"
  val dmIdUidTmpTable = "db_test.t_dwd_dmid_uid"
  val dmIdVidTmpTable = "db_test.t_dwd_dmid_vid"

  def main(args: Array[String]): Unit = {
    cleanLog("20200701")
  }

  def cleanLog(day: String): Unit = {
    spark.conf.set("spark.rpc.askTimeout", "700s")
    val logTable = "t_ods_miniprogram_log"
    val myKudu=new MyKudu(spark)
    val d = myKudu.select("t_logdata_mp_streaming")
      .where("day='20210331' ").drop("id")
     d.repartition(4).write.mode("append").format("Hive")
       .partitionBy("day").saveAsTable(logTable)

  }

  def getDmId(data: DataFrame): Unit = {
    //          .foreach(row => {
    //            row._2
    //              .foreach(f => {
    //                //                println(f)
    //                val cs = f._1.cs
    //                val uid = f._2._3
    //                val vid = f._2._1
    //                val did = f._2._2
    //                var dmId = ""
    //                val time = Common.tranTimestamp(f._2._5)
    //                var dmIdByVid = ""
    //                var vidTimestamp = 0L
    //                val timestamp = f._1.timestamp
    //                //首先查询VID对应的关系
    //                var dmIdByDid = ""
    //                var didTimeStamp = 0L
    //                var whereSql = ""
    //                var uidByVid = ""
    //                var uidByDid = ""
    //                //如果有设备ID，查询设备ID的映射关系
    //                if (did != null && did != "0" && did != "") {
    //                  whereSql = s" or f_vid='$did'"
    //                }
    //                val sql = s"""SELECT v.f_dm_id,v.f_timestamp, ifnull(u.f_uid,'') as f_uid,f_vid_type
    //                             |from t_dmid_vid_temp v left join t_dmid_uid_temp u on u.f_dm_id=v.f_dm_id
    //                             |where (f_vid='$vid' $whereSql  ) and  v.f_cs='$cs' """.stripMargin
    //                val dataVid = conn.prepareStatement(sql).executeQuery()
    //                while (dataVid.next()) {
    //                  val vidType = dataVid.getInt("f_vid_type")
    //                  if (vidType == 0) {
    //                    dmIdByVid = dataVid.getString("f_dm_id")
    //                    vidTimestamp = dataVid.getLong("f_timestamp")
    //                    uidByVid = dataVid.getString("f_uid")
    //                  } else {
    //                    didTimeStamp = dataVid.getLong("f_timestamp")
    //                    dmIdByDid = dataVid.getString("f_dm_id")
    //                    uidByDid = dataVid.getString("f_uid")
    //                  }
    //                }
    //                //1 首先处理有uid的数据，登录用户
    //                if (uid != "") {
    //                  //1.1 更新uid的映射，判断该uid是否有对应的天眼id
    //                  var dmIdByUid = ""
    //                  val dataUid = conn.prepareStatement(
    //                    s"""select f_dm_id,f_uid,f_create_time
    //                       |from $dmIdUidTmpTable where f_uid='$uid'""".stripMargin).executeQuery()
    //                  if (dataUid.next()) {
    //                    // 1.1.1 有映射关系
    //                    dmIdByUid = dataUid.getString("f_dm_id")
    //                    //更新或者插入数据
    //                    if (dmIdByVid != "") { //更新
    //                      conn.prepareStatement(updateVidData(dmIdByUid, vid, time, timestamp)).execute()
    //                    } else { //插入
    //                      conn.prepareStatement(insertVidData(dmIdByUid, vid, time, cs, timestamp, 0)).execute()
    //                    }
    //                    if (dmIdByDid != "") {
    //                      conn.prepareStatement(updateVidData(dmIdByUid, did, time, timestamp)).execute()
    //                    } else if (whereSql != "") {
    //                      conn.prepareStatement(insertVidData(dmIdByUid, did, time, cs, timestamp, 1)).execute()
    //                    }
    //                  } else { //1.2 如果没有关联关系 则开始根据vid去找之前是否有映射
    //                    //1.2.1 如果vid有映射，则开始判断是否有uid
    //                    if (dmIdByVid != "") {
    //                      if (uidByVid != "" && uidByDid != uid) {
    //                        //如果cid对应的有uid并且不是当前登录的UID，则给当前uid生成天眼id并且重新给cid赋值天眼ID
    //                        dmId = Common.getUUID()
    //                        if(vidTimestamp < timestamp){
    //                          conn.prepareStatement(updateVidData(dmId, vid, time, timestamp)).execute()
    //                          if (dmIdByDid != "") {
    //                            conn.prepareStatement(updateVidData(dmId, did, time, timestamp)).execute()
    //                          } else if (whereSql != "") {
    //                            conn.prepareStatement(insertVidData(dmId, did, time, cs, timestamp, 1)).execute()
    //                          }
    //                        }
    //                      } else if (uidByVid == "") {
    //                        dmId = dmIdByVid
    //                        if (whereSql != "" && dmIdByDid == "") {
    //                          conn.prepareStatement(insertVidData(dmId, did, time, cs, timestamp, 1)).execute()
    //                        }
    //                      }
    //                    } else {
    //                      //1.2.2 如果没有映射，则证明是全新的用户，生成天眼ID插入数据
    //                      dmId = Common.getUUID()
    //                      conn.prepareStatement(insertVidData(dmId, vid, time, cs, timestamp, 0)).execute()
    //                      if (whereSql != "") {
    //                        conn.prepareStatement(insertVidData(dmId, did, time, cs, timestamp, 1)).execute()
    //                      }
    //                    }
    //                    conn.prepareStatement(insertUidData(dmId, uid, time)).execute()
    //                  }
    //                } else {
    //                  //2、如果是未登录用户
    //                  //2.1 并且没有映射
    //                  if (dmIdByVid == "") {
    //                    dmId = Common.getUUID()
    //                    conn.prepareStatement(insertVidData(dmId, vid, time, cs, timestamp, 0)).execute()
    //                    if (whereSql != "" && didTimeStamp < timestamp) {
    //                      conn.prepareStatement(insertVidData(dmId, did, time, cs, timestamp, 1)).execute()
    //                    }
    //                  } else if (whereSql != "" && dmIdByDid == "" && didTimeStamp < timestamp) {
    //                    conn.prepareStatement(insertVidData(dmIdByVid, did, time, cs, timestamp, 1)).execute()
    //                  }
    //                }
    //              })
    //          })


  }


}

class ClientPartitioner(partitions: Int) extends Partitioner {
  require(partitions >= 0, s"Number of partitions ($partitions) cannot be negative.")

  override def numPartitions: Int = partitions

  override def getPartition(key: Any): Int = {
    val k = key.asInstanceOf[ClientKey]
    Math.abs(k.cs.hashCode()) % numPartitions
  }
}

//创建key类，key组合键为 cs,timestamp
case class ClientKey(cs: String, timestamp: Long)

object ClientKey {
  implicit def orderingByTimestamp[A <: ClientKey]: Ordering[A] = {
    Ordering.by(fk => (fk.cs, fk.timestamp))
  }
}