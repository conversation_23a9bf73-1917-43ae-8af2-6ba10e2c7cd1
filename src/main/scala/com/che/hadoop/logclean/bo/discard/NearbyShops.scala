package com.che.hadoop.logclean.bo.discard

import com.alibaba.fastjson.JSON
import com.che.hadoop.underlay.dao
import scalaj.http.Http

import scala.collection.mutable.ListBuffer

/**
  * 从接口获取数据（仅适用于附近商家和挂车用户信息采集接口）
  * 数据已经统一迁移到FetchWebData中
  * 本程序废弃
  */
object NearbyShops {

  /**
    * 请求接口获取Json数据，解析后写入到hive
    * @param sourceUrl  接口地址
    * @param tableName  hive表名称
    * @param dataFields 需要从接口返回的json中解析出来的字段名称，与hive表字段一致
    * @return 接口请求状态消息
    */
  def getDataAndSave(sourceUrl: String, tableName: String, dataFields: String*): String = {

    // 请求接口获取Json数据
    val response = Http(sourceUrl).asString
    val resultJson = JSON.parseObject(response.body)
    // 判断接口返回的状态，若失败直接return失败信息，程序不再往下进行
    if (resultJson.getInteger("status") == 1) {
      return resultJson.getString("msg")
    }
    val spark = dao.MySparkSession.conn()
    import spark.implicits._
    // 解析Json数据构造DataFrame
    val dataList = new ListBuffer[(Long, Long, String, String, Int)]
    val jsonArray = resultJson.getJSONArray("data").iterator()

    while (jsonArray.hasNext) {
      val t = JSON.parseObject(jsonArray.next().toString)
      dataList.append((
        t.getLong(dataFields(0)),
        t.getLong(dataFields(1)),
        t.getString(dataFields(2)),
        t.getString(dataFields(3)),
        t.getInteger(dataFields(4))))
    }
    val data = dataList.toDF(dataFields: _*)
    // 写入hive
    data.repartition(1).write.mode("append").format("Hive").saveAsTable(tableName)
    spark.close()
    resultJson.getString("msg")
  }

}
