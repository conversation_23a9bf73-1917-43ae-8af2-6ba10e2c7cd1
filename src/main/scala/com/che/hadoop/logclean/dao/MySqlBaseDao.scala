package com.che.hadoop.logclean.dao

import java.sql.{Connection, PreparedStatement, ResultSet, Statement}
import java.util.Properties

import com.che.hadoop.underlay.dao.MySparkSession
import org.apache.spark.sql.{DataFrame, SparkSession}


class MySqlBaseDao(connectionName:String="che")  {
  private val dbConnectionPool=new DBConnectionPool(connectionName)
  private val connection: Connection = dbConnectionPool.getConn()
  private var preparedStatement: PreparedStatement = _
  private var resultSet: ResultSet = _


  /**
    * 执行指定的SQL
    * @param sql 需要执行的sql
    */
  def execute(sql: String): Unit = {
    try {
      this.preparedStatement = this.connection.prepareStatement(sql)
      this.preparedStatement.execute()
    } catch {
      case e: Exception => e.printStackTrace()
    } finally {
      dbConnectionPool.releaseCon(this.connection)
      this.connection == null
    }
  }

  /**
   * 添加记录并返回主键
   * @param sql
   * @param objects
   * @return
   */
  def insertForGeneratedKeys(sql: String, objects: Array[Any]): ResultSet = {
    try {
      this.preparedStatement = this.connection.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)
      if(objects != null)
        this.setPreparedStatement(objects)

      this.preparedStatement.executeUpdate()
      this.resultSet = preparedStatement.getGeneratedKeys
    } catch {
      case e: Exception => e.printStackTrace()
    } finally {
      dbConnectionPool.releaseCon(this.connection)
      this.connection == null
    }

    this.resultSet
  }

  /**
   * 执行查询
   * @param sql 需要执行的sql
   * @param objects 参数
   * @return
   */
  def executeQuery(sql: String, objects: Array[Any]): ResultSet = {
    try {
      this.preparedStatement = this.connection.prepareStatement(sql)
      if(objects != null)
        this.setPreparedStatement(objects)
      this.resultSet = this.preparedStatement.executeQuery()
    } catch {
      case e: Exception => e.printStackTrace()
    } finally {
      dbConnectionPool.releaseCon(this.connection)
      this.connection == null
    }

    this.resultSet
  }


  /**
    * 执行插入/更新操作
    * @param sql
    * @param objects
    * @return 更新的行数，如果-1则出现异常
    */
  def executeInsertOrUpdate(sql: String, objects: Array[Any]): Int = {
    try {
      this.preparedStatement = this.connection.prepareStatement(sql)
      if(objects != null)
        this.setPreparedStatement(objects)
      this.preparedStatement.executeUpdate()
    } catch {
      case e: Exception => e.printStackTrace()
    } finally {
      dbConnectionPool.releaseCon(this.connection)
      this.connection == null
    }
    -1
  }

  /**
   * 对于已经初始化完毕的preparedStatement进行参数赋值
   * @param objects
   */
  private def setPreparedStatement(objects: Array[Any]): Unit = {
    var i = 1
    for(obj <- objects) {
      this.preparedStatement.setObject(i, obj)
      i += 1
    }
  }

  /**
   * 存在则更新，不存在则插入
   * @param sqls 0,1,2 查询，更新，插入
   * @param args 0,1,2 查询，更新，插入
   */
  def existsUpdateElseInsert(sqls: List[String], args: List[Array[Any]]): Unit = {
    try {
      if(sqls.length != 3 || args.length != 3)
        return
      // 1. 查询
      val rs = this.executeQuery(sqls.head, args.head)
      if(rs.next()) {
        // 2.1 更新
        if(sqls(1) != null && args(1) != null)
          this.executeInsertOrUpdate(sqls(1), args(1))
      } else {
        // 2.2 插入
        if(sqls(2) != null && args(2) != null)
          this.executeInsertOrUpdate(sqls(2), args(2))
      }
    } catch {
      case e: Exception => e.printStackTrace()
    }
  }

  /*w数据保存到mysql 通过spark实现连接*/
  def saveMysql(saveDf:DataFrame,tablename:String,saveMode:String="append"):Unit=
  {
    //truncate仅仅是删除数据，并不删除结构。
    if(saveMode=="overwrite"){
      execute(s"truncate table $tablename")
    }
    val prop=new Properties()
    prop.put("user",dbConnectionPool.username)
    prop.put("password",dbConnectionPool.password)
    prop.put("driver","com.mysql.cj.jdbc.Driver")
    saveDf.write.mode("append").jdbc(dbConnectionPool.url,tablename, prop)
  }



  /***
    *根据类型得到得到url_code转换code码
    */
  //  def GetType_M(ss:SparkSession,strWhere:String,filed:String): Unit =
  //  {
  //    ss.read.format("jdbc").option("url",dao.mysql_conn.mysqlurl)
  //      .option("dbtable","(select " +
  //        " a.f_id as id,a.F_parentid as parentid,a.f_name as name,a.f_code as code "+filed+" from " +
  //        " t_type as a  where a.f_isdelete=0 "+strWhere+") as t")
  //      .option("user","root")
  //      .option("password","4271bf88302e7108")
  //      .load().createOrReplaceTempView("t_type")
  //  }


  /**
  得到拉取文章接口数据
    */
  //  def getArticle(day:String):Unit={
  //    dao.MySparkSession.conn.read.format("jdbc").option("url",dao.mysql_conn.mysqlurl)
  //      .option("dbtable","(SELECT *,unix_timestamp(f_starttime)*1000 as starttime,unix_timestamp(f_endtime)*1000 as endtime," +
  //        "CONCAT('(id=',f_articleid,')|(Id=',f_articleid,')|(/',f_articleid,'.)|(v_',f_articleid,')|(articleid=',f_articleid,')') as id FROM `t_article` where  "+day+" " +
  //        "between DATE_FORMAT(f_starttime,'%Y%m%d') and  DATE_FORMAT(f_endtime,'%Y%m%d')) as t")  // "+day+" between DATE_FORMAT(f_starttime,'%Y%m%d') and  DATE_FORMAT(f_endtime,'%Y%m%d'
  //      .option("user",dao.mysql_conn.user)
  //      .option("password",dao.mysql_conn.password)
  //      .load().createOrReplaceTempView("t_article")
  //  }

  def getCacheTable(tableName:String,sql:String, spark:SparkSession = MySparkSession.conn()):Unit=
  {
    spark.read.format("jdbc").option("url",new DBConnectionPool(connectionName).url)
      .option("dbtable",sql)
      .option("user",dbConnectionPool.username)
      .option("password",dbConnectionPool.password)
      .load().createOrReplaceTempView(tableName)

  }
  //读取mysql数据
  def getDfTable(sql:String, spark:SparkSession = MySparkSession.conn()):DataFrame=
  {
    spark.read.format("jdbc").option("url",new DBConnectionPool(connectionName).url)
      .option("dbtable",sql)
      .option("user",dbConnectionPool.username)
      .option("password",dbConnectionPool.password)
      .load()

  }
}
