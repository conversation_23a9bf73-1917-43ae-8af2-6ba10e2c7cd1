package com.che.hadoop.logclean.bo.discard

import com.che.hadoop.underlay.dao.MySparkSession

/**
  * 百度Eqid解析,任务废弃，已不再调用
  */
object BaiduEqidOld {
  def main(args: Array[String]): Unit = {
    for (day<-Array("20190623","20190624","20190627","20190628","20190629","20190630")){
      println(day)
      EqidPage(day)
    }
//    EqidPage(day)
//    pvuvZiXun()

//    println(MySparkSession.conn.sql("SELECT distinct uid FROM `default`.t_logdata where day=20190319 and code=15108100010000 " +
//      "and k like '%action=appoint%'").count())
//
//    val httpClient : CloseableHttpClient = HttpClients.createDefault()
  }
  /**
    * 计算eqid访问页面
    */
  def EqidPage(day: String): Unit = {
    val spark=MySparkSession.conn()
    val sql = " SELECT vid,uid,code,k as page,SUBSTRING(i,locate('eqid',i)+5,32) eqid,from_unixtime(cast(substring(daytime,0,10) as int),'yyyy-MM-dd HH:mm:ss') as v_datetime,'" + day + "' as day FROM `default`.t_logdata where day=" + day + " and eventid='' and i like '%eqid%' and length(SUBSTRING(i,locate('eqid',i)+5,32))>=32"
    spark.sql(sql).write.mode("append").format("Hive").partitionBy("day")saveAsTable("default.t_eqid_page")

    //解析eqid
//    val eqidRdd = MySparkSession.conn.sql("select distinct(eqid) as eqid from t_eqid_page where day=" + day + " ").rdd
//    var rowRDD = eqidRdd.foreach(f = line => {
//      val eqid = line.getAs[String]("eqid").toString
//      var word = ""
//      try {
//        //通过接口解析百度eqid关键词
//        var strData = MyHttpService.GetHttpRequest2("http://180.76.232.68/index.php?eqid=" + eqid + "")
//        if (strData != "") {
//          val json = new JsonQuery(strData)
//          if (json.ms("status").toBoolean) {
//            word = StrEcode.unicodeStr2String(json.ms("wd"))
//          }
//        }
//      }
//      catch {
//        case ex: Exception => println(ex)
//      }
//      println(word)
//      Row(eqid, word, day)
//      //println(eqid+":"+ word)
//    })
//    val structType = StructType(Array(
//      StructField("eqid", StringType, true),
//      StructField("word", StringType, true),
//      StructField("day", StringType, true)
//    ))
//    val eqidDf = dao.MySparkSession.conn.sqlContext.createDataFrame(rowRDD, structType).createOrReplaceTempView("t_eqid_temp")
//    MySparkSession.conn.sql("insert overwrite table t_eqid select eqid,word,day from t_eqid_temp where word!=''")

//    //写入ga mysql数据库
//    val dataDf = MySparkSession.conn.sql("select day,eqid,word from t_eqid where day=" + day + "").toDF()
//    dataDf.foreachPartition(partitionOfRecords => {
//      val exec = new MySqlBaseDao_GA()
//      val list = new ListBuffer[exec.EqidData]
//      partitionOfRecords.foreach(info => {
//        val day = info.getAs[String]("day")
//        val eqid = info.getAs[String]("eqid")
//        val word = info.getAs[String]("word")
//        list.append(exec.EqidData(day, eqid, word))
//      })
//      exec.InsertEqid("insert into t_eqid(day, eqid, word)values(?,?,?)", list)
//    })
  }
  def pvuvZiXun():Unit={
    val spark=MySparkSession.conn()
    val sql="SELECT day,count(1) pv,count(DISTINCT vid) uv from t_eqid_page where day in('20190412','20190413','20190414','20190415','20190416','20190417','20190418') and code in ('12103100010000','12107100010000','11102100010000','11106100010000','11108100010000','15102100010000','13102100010000','13103100010000','14102100010000','14103100010000','15107100010000') GROUP by day"
//    val sql="SELECT day,count(1) pv,count(DISTINCT vid) uv from t_eqid_page where day='20190412'GROUP by day"

    spark.sql(sql).show()
  }

}
