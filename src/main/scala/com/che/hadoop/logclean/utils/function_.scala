package com.che.hadoop.logclean.utils

import java.security.MessageDigest
import java.text.SimpleDateFormat
import java.util.{Calendar, Date, Properties}

import com.che.hadoop.underlay.dao
import org.apache.spark.sql.{DataFrame, SparkSession}

import scala.util.matching.Regex
import java.util.regex.Pattern

import com.che.hadoop.logclean.dao.mysql_conn
import com.che.hadoop.underlay.dao.{MySparkSession, MySqlBaseDao}
import scalaj.http.{Http, HttpResponse}

object function_ {
  def main(args: Array[String]): Unit = {
    println(getWeekNum(0))
  }
  /*得到当前日期前一天*/
  def getDate(day: Int): String = {
    val dateFormat: SimpleDateFormat = new SimpleDateFormat("yyyyMMdd")
    var cal: Calendar = Calendar.getInstance()
    cal.add(Calendar.DATE, -day)
    val date = dateFormat.format(cal.getTime())
    return date

  }
  //val mysqlurl="****************************************************************************"
  /*w数据保存到mysql*/
  def saveMysql(saveDf:DataFrame,tablename:String):Unit=
  {
    val prop=new Properties()
    prop.put("user",mysql_conn.user)
    prop.put("password",mysql_conn.password)
    prop.put("driver","com.mysql.cj.jdbc.Driver")
    saveDf.write.mode("append").jdbc(mysql_conn.mysqlurl,tablename, prop)
  }

  /*正则判断匹配*/
  def GetRegular(value:String,regular:String):Boolean=
  {
    var k=value.toString
    val pattern = new Regex(regular)
    if((pattern findAllIn value.toString()).mkString(",")!="")
      return true
    else
      return false
  }

  /*得到当前日期前一天yyyy-MM-dd格式*/
  def getShortDateTime(day: Int): String = {
    var  dateFormat:SimpleDateFormat = new SimpleDateFormat("yyyy-MM-dd")
    var cal:Calendar=Calendar.getInstance()
    cal.add(Calendar.DATE,-day)
    var date=dateFormat.format(cal.getTime())
    return date
  }
  /*得到当前日期前几天星期几*/
  def getWeekNum(day: Int): Int = {
    var  dateFormat:SimpleDateFormat = new SimpleDateFormat("yyyy-MM-dd")
    var cal:Calendar=Calendar.getInstance()
    cal.add(Calendar.DATE,-day)
    var date = cal.get(Calendar.DAY_OF_WEEK)
    return date
  }

  def getNowDate(): String = {
    val now: Date = new Date()
    val dateFormat: SimpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
    val date = dateFormat.format(now)
    return date
  }
  /**
   *请求url公共方法
   * @param url 请求url
   * @param max_try 最大重试次数
   * @return HttpResponse
   */
  def request_data(url:String,max_try:Int =3):HttpResponse[String] = {
    var loop = 0
    var response:HttpResponse[String] = null
    while(loop < max_try){
      try{
        response = Http(url).timeout(100000,100000).asString
        if(response.code !=200){
          throw new Exception(s"response异常:{code:${response.code},url:$url}")
        }
        loop = max_try
      }catch{
        case e:Exception => {
          loop = loop + 1
          if(loop < max_try){
            println("请求失败" + loop.toString + "次,再次请求.url:" + url+"  "+getNowDate())
            e.printStackTrace()
          }else{
            println("请求失败" + loop.toString + "次,已到最高请求次数.url:" + url+"  "+getNowDate())
            e.printStackTrace()
          }
        }
      }

    }
    response
  }

  def MD5(input: String): String = {
    var md5: MessageDigest = null
    try {
      md5 = MessageDigest.getInstance("MD5")
    }
    catch {
      case e: Exception => {
        e.printStackTrace
        println(e.getMessage)
      }
    }
    val byteArray: Array[Byte] = input.getBytes
    val md5Bytes: Array[Byte] = md5.digest(byteArray)
    var hexValue: String = ""
    var i: Integer = 0
    for ( i <- 0 to md5Bytes.length-1) {
      val str: Int = (md5Bytes(i).toInt) & 0xff
      if (str < 16) {
        hexValue=hexValue+"0"
      }
      hexValue=hexValue+(Integer.toHexString(str))
    }
    return hexValue.toString
  }

  def getCarPropertyData(ss: SparkSession, strSql: String): DataFrame = {
    val sqlBase=new MySqlBaseDao()
    sqlBase.getDfTable(strSql,ss)
  }
  //根据时间获取时间戳
  def getTimestamp(tm: String): String = {
    val dateFormat: SimpleDateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssX")
    val dt = dateFormat.parse(tm)
    //val aa = dateFormat.format(dt)
    val tim: Long = dt.getTime()
    // println(tim)
    tim.toString.substring(0,10)
  }
}
