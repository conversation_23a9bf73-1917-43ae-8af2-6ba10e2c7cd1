<?xml version="1.0" encoding="UTF-8"?>
<module org.jetbrains.idea.maven.project.MavenProjectsManager.isMavenModule="true" type="JAVA_MODULE" version="4">
  <component name="NewModuleRootManager" LANGUAGE_LEVEL="JDK_1_5">
    <output url="file://$MODULE_DIR$/target/classes" />
    <output-test url="file://$MODULE_DIR$/target/test-classes" />
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/src/main/resources" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/main/scala" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/main/java" isTestSource="false" />
      <excludeFolder url="file://$MODULE_DIR$/target" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" name="scala-sdk-2.11.8" level="application" />
    <orderEntry type="library" name="sqljdbc42" level="project" />
    <orderEntry type="library" name="Maven: org.apache.commons:commons-compress:1.4.1" level="project" />
    <orderEntry type="library" name="Maven: org.tukaani:xz:1.0" level="project" />
    <orderEntry type="library" name="Maven: xml-apis:xml-apis:1.3.04" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-core:2.6.7" level="project" />
    <orderEntry type="library" name="Maven: org.apache.spark:spark-core_2.11:2.3.3" level="project" />
    <orderEntry type="library" name="Maven: org.apache.avro:avro:1.7.7" level="project" />
    <orderEntry type="library" name="Maven: org.codehaus.jackson:jackson-core-asl:1.9.13" level="project" />
    <orderEntry type="library" name="Maven: com.thoughtworks.paranamer:paranamer:2.3" level="project" />
    <orderEntry type="library" name="Maven: org.apache.commons:commons-compress:1.4.1" level="project" />
    <orderEntry type="library" name="Maven: org.tukaani:xz:1.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.avro:avro-mapred:hadoop2:1.7.7" level="project" />
    <orderEntry type="library" name="Maven: org.apache.avro:avro-ipc:1.7.7" level="project" />
    <orderEntry type="library" name="Maven: org.apache.avro:avro-ipc:tests:1.7.7" level="project" />
    <orderEntry type="library" name="Maven: com.twitter:chill_2.11:0.8.4" level="project" />
    <orderEntry type="library" name="Maven: com.esotericsoftware:kryo-shaded:3.0.3" level="project" />
    <orderEntry type="library" name="Maven: com.esotericsoftware:minlog:1.3.0" level="project" />
    <orderEntry type="library" name="Maven: org.objenesis:objenesis:2.1" level="project" />
    <orderEntry type="library" name="Maven: com.twitter:chill-java:0.8.4" level="project" />
    <orderEntry type="library" name="Maven: org.apache.xbean:xbean-asm5-shaded:4.4" level="project" />
    <orderEntry type="library" name="Maven: org.apache.hadoop:hadoop-client:2.6.5" level="project" />
    <orderEntry type="library" name="Maven: org.apache.hadoop:hadoop-common:2.6.5" level="project" />
    <orderEntry type="library" name="Maven: xmlenc:xmlenc:0.52" level="project" />
    <orderEntry type="library" name="Maven: commons-collections:commons-collections:3.2.2" level="project" />
    <orderEntry type="library" name="Maven: commons-configuration:commons-configuration:1.6" level="project" />
    <orderEntry type="library" name="Maven: commons-digester:commons-digester:1.8" level="project" />
    <orderEntry type="library" name="Maven: commons-beanutils:commons-beanutils:1.7.0" level="project" />
    <orderEntry type="library" name="Maven: commons-beanutils:commons-beanutils-core:1.8.0" level="project" />
    <orderEntry type="library" name="Maven: com.google.code.gson:gson:2.2.4" level="project" />
    <orderEntry type="library" name="Maven: org.apache.hadoop:hadoop-auth:2.6.5" level="project" />
    <orderEntry type="library" name="Maven: org.apache.directory.server:apacheds-kerberos-codec:2.0.0-M15" level="project" />
    <orderEntry type="library" name="Maven: org.apache.directory.server:apacheds-i18n:2.0.0-M15" level="project" />
    <orderEntry type="library" name="Maven: org.apache.directory.api:api-asn1-api:1.0.0-M20" level="project" />
    <orderEntry type="library" name="Maven: org.apache.directory.api:api-util:1.0.0-M20" level="project" />
    <orderEntry type="library" name="Maven: org.apache.curator:curator-client:2.6.0" level="project" />
    <orderEntry type="library" name="Maven: org.htrace:htrace-core:3.0.4" level="project" />
    <orderEntry type="library" name="Maven: org.apache.hadoop:hadoop-hdfs:2.6.5" level="project" />
    <orderEntry type="library" name="Maven: org.mortbay.jetty:jetty-util:6.1.26" level="project" />
    <orderEntry type="library" name="Maven: xerces:xercesImpl:2.9.1" level="project" />
    <orderEntry type="library" name="Maven: xml-apis:xml-apis:1.3.04" level="project" />
    <orderEntry type="library" name="Maven: org.apache.hadoop:hadoop-mapreduce-client-app:2.6.5" level="project" />
    <orderEntry type="library" name="Maven: org.apache.hadoop:hadoop-mapreduce-client-common:2.6.5" level="project" />
    <orderEntry type="library" name="Maven: org.apache.hadoop:hadoop-yarn-client:2.6.5" level="project" />
    <orderEntry type="library" name="Maven: org.apache.hadoop:hadoop-yarn-server-common:2.6.5" level="project" />
    <orderEntry type="library" name="Maven: org.apache.hadoop:hadoop-mapreduce-client-shuffle:2.6.5" level="project" />
    <orderEntry type="library" name="Maven: org.apache.hadoop:hadoop-yarn-api:2.6.5" level="project" />
    <orderEntry type="library" name="Maven: org.apache.hadoop:hadoop-mapreduce-client-core:2.6.5" level="project" />
    <orderEntry type="library" name="Maven: org.apache.hadoop:hadoop-yarn-common:2.6.5" level="project" />
    <orderEntry type="library" name="Maven: javax.xml.bind:jaxb-api:2.2.2" level="project" />
    <orderEntry type="library" name="Maven: javax.xml.stream:stax-api:1.0-2" level="project" />
    <orderEntry type="library" name="Maven: org.codehaus.jackson:jackson-jaxrs:1.9.13" level="project" />
    <orderEntry type="library" name="Maven: org.codehaus.jackson:jackson-xc:1.9.13" level="project" />
    <orderEntry type="library" name="Maven: org.apache.hadoop:hadoop-mapreduce-client-jobclient:2.6.5" level="project" />
    <orderEntry type="library" name="Maven: org.apache.hadoop:hadoop-annotations:2.6.5" level="project" />
    <orderEntry type="library" name="Maven: org.apache.spark:spark-launcher_2.11:2.3.3" level="project" />
    <orderEntry type="library" name="Maven: org.apache.spark:spark-kvstore_2.11:2.3.3" level="project" />
    <orderEntry type="library" name="Maven: org.fusesource.leveldbjni:leveldbjni-all:1.8" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-core:2.6.7" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-annotations:2.6.7" level="project" />
    <orderEntry type="library" name="Maven: org.apache.spark:spark-network-common_2.11:2.3.3" level="project" />
    <orderEntry type="library" name="Maven: org.apache.spark:spark-network-shuffle_2.11:2.3.3" level="project" />
    <orderEntry type="library" name="Maven: org.apache.spark:spark-unsafe_2.11:2.3.3" level="project" />
    <orderEntry type="library" name="Maven: net.java.dev.jets3t:jets3t:0.9.4" level="project" />
    <orderEntry type="library" name="Maven: javax.activation:activation:1.1.1" level="project" />
    <orderEntry type="library" name="Maven: org.bouncycastle:bcprov-jdk15on:1.52" level="project" />
    <orderEntry type="library" name="Maven: com.jamesmurty.utils:java-xmlbuilder:1.1" level="project" />
    <orderEntry type="library" name="Maven: net.iharder:base64:2.3.8" level="project" />
    <orderEntry type="library" name="Maven: org.apache.curator:curator-recipes:2.6.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.curator:curator-framework:2.6.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.zookeeper:zookeeper:3.4.6" level="project" />
    <orderEntry type="library" name="Maven: com.google.guava:guava:16.0.1" level="project" />
    <orderEntry type="library" name="Maven: javax.servlet:javax.servlet-api:3.1.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.commons:commons-lang3:3.5" level="project" />
    <orderEntry type="library" name="Maven: org.apache.commons:commons-math3:3.4.1" level="project" />
    <orderEntry type="library" name="Maven: com.google.code.findbugs:jsr305:1.3.9" level="project" />
    <orderEntry type="library" name="Maven: org.slf4j:slf4j-api:1.7.16" level="project" />
    <orderEntry type="library" name="Maven: org.slf4j:jul-to-slf4j:1.7.16" level="project" />
    <orderEntry type="library" name="Maven: org.slf4j:jcl-over-slf4j:1.7.16" level="project" />
    <orderEntry type="library" name="Maven: log4j:log4j:1.2.17" level="project" />
    <orderEntry type="library" name="Maven: org.slf4j:slf4j-log4j12:1.7.16" level="project" />
    <orderEntry type="library" name="Maven: com.ning:compress-lzf:1.0.3" level="project" />
    <orderEntry type="library" name="Maven: org.xerial.snappy:snappy-java:1.1.2.6" level="project" />
    <orderEntry type="library" name="Maven: org.lz4:lz4-java:1.4.0" level="project" />
    <orderEntry type="library" name="Maven: com.github.luben:zstd-jni:1.3.2-2" level="project" />
    <orderEntry type="library" name="Maven: org.roaringbitmap:RoaringBitmap:0.5.11" level="project" />
    <orderEntry type="library" name="Maven: commons-net:commons-net:2.2" level="project" />
    <orderEntry type="library" name="Maven: org.scala-lang:scala-library:2.11.8" level="project" />
    <orderEntry type="library" name="Maven: org.json4s:json4s-jackson_2.11:3.2.11" level="project" />
    <orderEntry type="library" name="Maven: org.json4s:json4s-core_2.11:3.2.11" level="project" />
    <orderEntry type="library" name="Maven: org.json4s:json4s-ast_2.11:3.2.11" level="project" />
    <orderEntry type="library" name="Maven: org.scala-lang:scalap:2.11.0" level="project" />
    <orderEntry type="library" name="Maven: org.scala-lang:scala-compiler:2.11.0" level="project" />
    <orderEntry type="library" name="Maven: org.glassfish.jersey.core:jersey-client:2.22.2" level="project" />
    <orderEntry type="library" name="Maven: javax.ws.rs:javax.ws.rs-api:2.0.1" level="project" />
    <orderEntry type="library" name="Maven: org.glassfish.hk2:hk2-api:2.4.0-b34" level="project" />
    <orderEntry type="library" name="Maven: org.glassfish.hk2:hk2-utils:2.4.0-b34" level="project" />
    <orderEntry type="library" name="Maven: org.glassfish.hk2.external:aopalliance-repackaged:2.4.0-b34" level="project" />
    <orderEntry type="library" name="Maven: org.glassfish.hk2.external:javax.inject:2.4.0-b34" level="project" />
    <orderEntry type="library" name="Maven: org.glassfish.hk2:hk2-locator:2.4.0-b34" level="project" />
    <orderEntry type="library" name="Maven: org.javassist:javassist:3.18.1-GA" level="project" />
    <orderEntry type="library" name="Maven: org.glassfish.jersey.core:jersey-common:2.22.2" level="project" />
    <orderEntry type="library" name="Maven: javax.annotation:javax.annotation-api:1.2" level="project" />
    <orderEntry type="library" name="Maven: org.glassfish.jersey.bundles.repackaged:jersey-guava:2.22.2" level="project" />
    <orderEntry type="library" name="Maven: org.glassfish.hk2:osgi-resource-locator:1.0.1" level="project" />
    <orderEntry type="library" name="Maven: org.glassfish.jersey.core:jersey-server:2.22.2" level="project" />
    <orderEntry type="library" name="Maven: org.glassfish.jersey.media:jersey-media-jaxb:2.22.2" level="project" />
    <orderEntry type="library" name="Maven: javax.validation:validation-api:1.1.0.Final" level="project" />
    <orderEntry type="library" name="Maven: org.glassfish.jersey.containers:jersey-container-servlet:2.22.2" level="project" />
    <orderEntry type="library" name="Maven: org.glassfish.jersey.containers:jersey-container-servlet-core:2.22.2" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-all:4.1.17.Final" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty:3.9.9.Final" level="project" />
    <orderEntry type="library" name="Maven: com.clearspring.analytics:stream:2.7.0" level="project" />
    <orderEntry type="library" name="Maven: io.dropwizard.metrics:metrics-core:3.1.5" level="project" />
    <orderEntry type="library" name="Maven: io.dropwizard.metrics:metrics-jvm:3.1.5" level="project" />
    <orderEntry type="library" name="Maven: io.dropwizard.metrics:metrics-json:3.1.5" level="project" />
    <orderEntry type="library" name="Maven: io.dropwizard.metrics:metrics-graphite:3.1.5" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-databind:2.6.7.1" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.module:jackson-module-scala_2.11:2.6.7.1" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.module:jackson-module-paranamer:2.7.9" level="project" />
    <orderEntry type="library" name="Maven: org.apache.ivy:ivy:2.4.0" level="project" />
    <orderEntry type="library" name="Maven: oro:oro:2.0.8" level="project" />
    <orderEntry type="library" name="Maven: net.razorvine:pyrolite:4.13" level="project" />
    <orderEntry type="library" name="Maven: net.sf.py4j:py4j:0.10.7" level="project" />
    <orderEntry type="library" name="Maven: org.apache.spark:spark-tags_2.11:2.3.3" level="project" />
    <orderEntry type="library" name="Maven: org.apache.commons:commons-crypto:1.0.0" level="project" />
    <orderEntry type="library" name="Maven: org.spark-project.spark:unused:1.0.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.spark:spark-sql_2.11:2.3.3" level="project" />
    <orderEntry type="library" name="Maven: com.univocity:univocity-parsers:2.5.9" level="project" />
    <orderEntry type="library" name="Maven: org.apache.spark:spark-sketch_2.11:2.3.3" level="project" />
    <orderEntry type="library" name="Maven: org.apache.spark:spark-catalyst_2.11:2.3.3" level="project" />
    <orderEntry type="library" name="Maven: org.scala-lang.modules:scala-parser-combinators_2.11:1.0.4" level="project" />
    <orderEntry type="library" name="Maven: org.codehaus.janino:janino:3.0.8" level="project" />
    <orderEntry type="library" name="Maven: org.codehaus.janino:commons-compiler:3.0.8" level="project" />
    <orderEntry type="library" name="Maven: org.antlr:antlr4-runtime:4.7" level="project" />
    <orderEntry type="library" name="Maven: org.apache.orc:orc-core:nohive:1.4.4" level="project" />
    <orderEntry type="library" name="Maven: com.google.protobuf:protobuf-java:2.5.0" level="project" />
    <orderEntry type="library" name="Maven: commons-lang:commons-lang:2.6" level="project" />
    <orderEntry type="library" name="Maven: io.airlift:aircompressor:0.8" level="project" />
    <orderEntry type="library" name="Maven: org.apache.orc:orc-mapreduce:nohive:1.4.4" level="project" />
    <orderEntry type="library" name="Maven: org.apache.parquet:parquet-column:1.8.3" level="project" />
    <orderEntry type="library" name="Maven: org.apache.parquet:parquet-common:1.8.3" level="project" />
    <orderEntry type="library" name="Maven: org.apache.parquet:parquet-encoding:1.8.3" level="project" />
    <orderEntry type="library" name="Maven: org.apache.parquet:parquet-hadoop:1.8.3" level="project" />
    <orderEntry type="library" name="Maven: org.apache.parquet:parquet-format:2.3.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.parquet:parquet-jackson:1.8.3" level="project" />
    <orderEntry type="library" name="Maven: org.apache.arrow:arrow-vector:0.8.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.arrow:arrow-format:0.8.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.arrow:arrow-memory:0.8.0" level="project" />
    <orderEntry type="library" name="Maven: com.carrotsearch:hppc:0.7.2" level="project" />
    <orderEntry type="library" name="Maven: com.vlkan:flatbuffers:1.2.0-3f79e055" level="project" />
    <orderEntry type="library" scope="PROVIDED" name="Maven: org.apache.spark:spark-streaming_2.11:2.3.3" level="project" />
    <orderEntry type="library" name="Maven: com.github.stephenc.findbugs:findbugs-annotations:1.3.9-1" level="project" />
    <orderEntry type="library" name="Maven: junit:junit:4.11" level="project" />
    <orderEntry type="library" name="Maven: org.hamcrest:hamcrest-core:1.3" level="project" />
    <orderEntry type="library" name="Maven: org.apache.spark:spark-hive_2.11:2.3.3" level="project" />
    <orderEntry type="library" name="Maven: com.twitter:parquet-hadoop-bundle:1.6.0" level="project" />
    <orderEntry type="library" name="Maven: org.spark-project.hive:hive-exec:1.2.1.spark2" level="project" />
    <orderEntry type="library" name="Maven: commons-io:commons-io:2.4" level="project" />
    <orderEntry type="library" name="Maven: javolution:javolution:5.5.1" level="project" />
    <orderEntry type="library" name="Maven: log4j:apache-log4j-extras:1.2.17" level="project" />
    <orderEntry type="library" name="Maven: org.antlr:antlr-runtime:3.4" level="project" />
    <orderEntry type="library" name="Maven: org.antlr:stringtemplate:3.2.1" level="project" />
    <orderEntry type="library" name="Maven: antlr:antlr:2.7.7" level="project" />
    <orderEntry type="library" name="Maven: org.antlr:ST4:4.0.4" level="project" />
    <orderEntry type="library" name="Maven: com.googlecode.javaewah:JavaEWAH:0.3.2" level="project" />
    <orderEntry type="library" name="Maven: org.iq80.snappy:snappy:0.2" level="project" />
    <orderEntry type="library" name="Maven: stax:stax-api:1.0.1" level="project" />
    <orderEntry type="library" name="Maven: net.sf.opencsv:opencsv:2.3" level="project" />
    <orderEntry type="library" name="Maven: org.spark-project.hive:hive-metastore:1.2.1.spark2" level="project" />
    <orderEntry type="library" name="Maven: com.jolbox:bonecp:0.8.0.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: commons-cli:commons-cli:1.2" level="project" />
    <orderEntry type="library" name="Maven: org.datanucleus:datanucleus-api-jdo:3.2.6" level="project" />
    <orderEntry type="library" name="Maven: org.datanucleus:datanucleus-rdbms:3.2.9" level="project" />
    <orderEntry type="library" name="Maven: commons-pool:commons-pool:1.5.4" level="project" />
    <orderEntry type="library" name="Maven: commons-dbcp:commons-dbcp:1.4" level="project" />
    <orderEntry type="library" name="Maven: javax.jdo:jdo-api:3.0.1" level="project" />
    <orderEntry type="library" name="Maven: javax.transaction:jta:1.1" level="project" />
    <orderEntry type="library" name="Maven: commons-httpclient:commons-httpclient:3.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.calcite:calcite-avatica:1.2.0-incubating" level="project" />
    <orderEntry type="library" name="Maven: org.apache.calcite:calcite-core:1.2.0-incubating" level="project" />
    <orderEntry type="library" name="Maven: org.apache.calcite:calcite-linq4j:1.2.0-incubating" level="project" />
    <orderEntry type="library" name="Maven: net.hydromatic:eigenbase-properties:1.1.5" level="project" />
    <orderEntry type="library" name="Maven: org.codehaus.jackson:jackson-mapper-asl:1.9.13" level="project" />
    <orderEntry type="library" name="Maven: commons-codec:commons-codec:1.10" level="project" />
    <orderEntry type="library" name="Maven: joda-time:joda-time:2.9.3" level="project" />
    <orderEntry type="library" name="Maven: org.jodd:jodd-core:3.5.2" level="project" />
    <orderEntry type="library" name="Maven: org.datanucleus:datanucleus-core:3.2.10" level="project" />
    <orderEntry type="library" name="Maven: org.apache.thrift:libthrift:0.9.3" level="project" />
    <orderEntry type="library" name="Maven: org.apache.thrift:libfb303:0.9.3" level="project" />
    <orderEntry type="library" name="Maven: org.apache.derby:derby:10.12.1.1" level="project" />
    <orderEntry type="library" name="Maven: mysql:mysql-connector-java:6.0.6" level="project" />
    <orderEntry type="library" name="Maven: org.scalaj:scalaj-http_2.11:2.3.0" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba:fastjson:1.2.44" level="project" />
    <orderEntry type="library" name="Maven: org.apache.httpcomponents:httpclient:4.5.2" level="project" />
    <orderEntry type="library" name="Maven: org.apache.httpcomponents:httpcore:4.4.4" level="project" />
    <orderEntry type="library" name="Maven: commons-logging:commons-logging:1.2" level="project" />
    <orderEntry type="library" name="Maven: org.scalactic:scalactic_2.11:3.0.5" level="project" />
    <orderEntry type="library" name="Maven: org.scala-lang:scala-reflect:2.11.12" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.scalatest:scalatest_2.11:3.0.5" level="project" />
    <orderEntry type="library" name="Maven: org.scala-lang.modules:scala-xml_2.11:1.0.6" level="project" />
  </component>
</module>