#LogClean_PullBusinessDataJob.job
type=command
command=spark-submit --master yarn --deploy-mode cluster --conf spark.driver.extraJavaOptions="-Dlog4j.configuration=file:log4j.properties -verbose:class" --verbose --name Log<PERSON>lean_PullBusinessDataJob --driver-memory 5g --num-executors 5 --executor-cores 3 --executor-memory 10G --conf "spark.driver.userClassPathFirst=true" --conf "spark.executor.userClassPathFirst=true" --jars 360che.hadoop.underlay-2.0.jar --class com.che.hadoop.logclean.activity.PullBusinessDataJob 360che.hadoop.logclean-1.0.jar default.