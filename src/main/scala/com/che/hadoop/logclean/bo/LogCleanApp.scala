package com.che.hadoop.logclean.bo

import java.text.SimpleDateFormat
import java.util.{Base64, Calendar, Date, ResourceBundle}

import com.alibaba.fastjson.JSON
import org.apache.spark.sql.types._
import com.che.hadoop.underlay.dao.{MyKudu, MySparkSession, MySqlBaseDao}
import com.che.hadoop.logclean.utils.{Common, DingRobot, GZIPUtils, UrlDecodeUtil}
import com.che.hadoop.underlay.tool.common.HDFSFileRenamer
import com.che.hadoop.underlay.tool.date.DateTranslate
import org.apache.kudu.Schema
import org.apache.kudu.client.{AlterTableOptions, KuduClient}
import org.apache.kudu.spark.kudu.KuduContext
import org.apache.spark.sql.{DataFrame, Row, SparkSession}

/**
 * app埋点数据清洗到hive
 */
object LogCleanApp {

  val pattern = "^[0-9]+\\.[0-9]+\\.[0-9]+$".r

  case class LogData(cid: String, uid: String, event: String, screen_name: String, title: String,
                     referrer: String, url: String, element_id: String, element_content: String,
                     type_id: String, eventid: String, label: String, action: String,
                     element_type: String, element_position: String, device_id: String,
                     manufacturer: String, os: String, model: String, os_version: String,
                     app_id: String, screen_width: String, screen_height: String, app_version: String, ads: String,
                     latitude: String, longitude: String, wifi: String, network_type: String, time: String,
                     resume_from_background: String, event_duration: String, cl: String, day: String,user_id:String,province:String,city:String,country:String,referrer_title:String,recvTime:String)

  def main(args: Array[String]): Unit = {
//    test()
        val spark=MySparkSession.conn()
    Common.getBetweenDates("20241110","20241110").foreach(day=>{
      cleanAppScData(spark,day,"db_test.")
//      cleanAppData(spark,day)
//      dmIdSupplement(day)
    })
    //    dmIdSupplement("20220927")
    //    val a="110000000"
    //    var b=""
    //    if(!a.contains(".")){
    //      b=(a.toInt*1.0/1000000).toString
    //    }
    //    println(b)
    //    dmIdSupplement("20220112")
    //    val kuduMasters = ResourceBundle.getBundle("connection").getString("kudu.master")
    //    //获取kudu客户端对象
    //    val kuduClient: KuduClient = new KuduClient.KuduClientBuilder(kuduMasters).build()
    //    val tableName="t_logdata_appsc_streaming2"
    ////    val tableName="t_ods_appsc_streaming2"
    //    val schema=KuduSchema.getKuduSchema(tableName)
    //    val options=new AlterTableOptions()
    //    val lower=schema.newPartialRow()
    //    lower.addString("month","202112")
    //    val upper=schema.newPartialRow()
    //    upper.addString("month","202201")
    //    options.addRangePartition(lower,upper)
    ////    options.addNullableColumn("user_id",Type.STRING)
    //    kuduClient.alterTable(tableName,options)

  }

  /**
   * hadoop埋点数据清洗
   *
   * @param dayValue
   */
  def cleanAppData(spark:SparkSession,dayValue: String,dbEnv:String="default."): Unit = {
    val table=s"${dbEnv}t_logdata_app"
    val mysqlDB="db_hitsstatistic."
    //重命名tmp文件（flume采集异常情况下会有tmp文件）
    val URI=ResourceBundle.getBundle("connection_underlay").getString("oss-hdfs.uri")
    val path=s"/pv-nginx-app-new/$dayValue"
    val today=DateTranslate.getDate(0)
    //当天文件不自动重命名，因为正在采集过程中会有tmp文件
    if(today == dayValue){
      println("当天采集数据不能重命名tmp文件")
    }else{
      HDFSFileRenamer.renameTmpFile(URI,path)
    }
    val dirPath = s"$URI$path/*.gz"
    spark.sql(s"alter table $table drop if exists partition(day='$dayValue')")
    println(s"alter table $table drop if exists partition(day='$dayValue')")
    val sqlBase = new MySqlBaseDao()
    try {
      val LogRdd = spark.read.json(dirPath).filter(t => t.getAs[String]("status") == "200"
        && t.getAs[String]("request_body") != "-"
        && t.getAs[String]("http_host") == "thappa.360che.com") //过滤业务日志，参数为"-"
      LogRdd.cache()
      //转换日志格式
      val rowRDD = LogRdd.rdd.map(line => {
        var args=""
        try{
          args = UrlDecodeUtil.urlDecode(line.getAs[String]("request_body"))
        }catch {
          case ex: Exception => {
            println(ex)
          }
        }
        var list: List[Row] = Nil
        if (args != "") {
          try {
            val argJson = JSON.parseArray(args)
            for (a <- 0 until argJson.size()) {
              try {
                val argObj = argJson.getJSONObject(a)
                val av = argObj.getString("av")
                val isOnline = pattern findFirstIn av
                // 正则根据版本判断是否是测试数据，测试版本号都是4位，线上版本号都是3位
                if (isOnline.isDefined || av == "********") {
                  //              if (true) {
                  val cid = argObj.getString("cid")
                  val t = argObj.getString("t")
                  val time = argObj.getString("time")
                  val m = argObj.getString("m")
                  val sr = argObj.getString("sr")
                  val p = getP(argObj.getString("p"))
                  val cs = argObj.getString("cs")
                  val eventid = argObj.getString("eventid")
                  val action = argObj.getString("action")
                  var lable = argObj.getString("lable")
                  if(eventid=="CHES_00000088"){
                    val labelArr = lable.split("\\|")
                    if(labelArr.length > 2){
                      labelArr(0) = new String(Base64.getEncoder.encode(labelArr(0).getBytes("UTF-8")))
                      labelArr(1) = new String(Base64.getEncoder.encode(labelArr(1).getBytes("UTF-8")))
                    }
                    lable=labelArr.mkString("|")
                  }
                  val uid = argObj.getString("uid")
                  var visit_day = tranTimeToString(time)
                  if (visit_day > dayValue) {
                    visit_day = dayValue
                  }
                  var appid: String = null
                  if (argObj.containsKey("appid")) {
                    appid = argObj.getString("appid")
                  }
                  var page: String = null
                  if (argObj.containsKey("page")) {
                    page = argObj.getString("page")

                  }
                  var cl: String = null
                  if (argObj.containsKey("cl")) {
                    cl = argObj.getString("cl")
                  }
                  var deviceid: String = null
                  if (argObj.containsKey("deviceid")) {
                    deviceid = argObj.getString("deviceid")
                  }
                  var lib: String = null
                  if (argObj.containsKey("lib")) {
                    lib = argObj.getString("lib")
                  }
                  list = list :+ Row(av, cid, t, time, m, sr, p, cs, eventid, action, lable, uid, appid, visit_day,
                    dayValue, page, cl, deviceid, lib)
                }

              } catch {
                case ex: ClassCastException => {
                  println(ex)
                }
                case ex: NullPointerException => {
                  println(ex)
                }
              }
            }
          } catch {
            case ex: Exception => {
              println(ex)
            }
          }
        }
        list

      }).flatMap(x => x)
      val structType = StructType(Array(
        StructField("av", StringType, true),
        StructField("cid", StringType, true),
        StructField("t", StringType, true),
        StructField("time", StringType, true),
        StructField("m", StringType, true),
        StructField("sr", StringType, true),
        StructField("p", StringType, true),
        StructField("cs", StringType, true),
        StructField("eventid", StringType, true),
        StructField("action", StringType, true),
        StructField("lable", StringType, true),
        StructField("uid", StringType, true),
        StructField("appid", StringType, true),
        StructField("visit_day", StringType, true),
        StructField("day", StringType, true),
        StructField("page", StringType, true),
        StructField("cl", StringType, true),
        StructField("deviceid", StringType, true),
        StructField("lib", StringType, true)
      ))
      spark.sqlContext.createDataFrame(rowRDD, structType).createOrReplaceTempView("t_app_log")
      //匹配天眼ID,
      spark.sql(s"select uid,vid,dm_id,is_del,cs from t_dwd_dmid where day='$dayValue' " +
        "and cs in('android_s','android_c','iOS_c','iOS_s')" +
        " and vid_type=0 and is_del=0  ").createOrReplaceTempView("t_dmid_tmp")
      spark.catalog.cacheTable("t_dmid_tmp")
      spark.sql("select distinct uid, dm_id from t_dmid_tmp where uid!=''").createOrReplaceTempView("t_uid_dmid")
      spark.sql("select distinct vid, dm_id,cs from t_dmid_tmp where is_del=0").createOrReplaceTempView("t_vid_dmid")
      spark.sql(
        """
          |select distinct l.*,nvl(d.dm_id,d1.dm_id) as dm_id from t_app_log  l left join t_uid_dmid d on l.uid=d.uid and l.uid!=''
          |left join t_vid_dmid d1 on l.cid=d1.vid and d1.cs=(case  when (lib in ('com.truckhome.bbs','com.360che.truckhome') or av='7.4.0') and appid='ches' then concat(l.cs,"_c")
          |when appid='ches' then concat(l.cs,"_s") else  concat(l.cs,'_c')  end)
          |""".stripMargin).repartition(4).write.mode("append").format("Hive")
        .partitionBy("day").saveAsTable(table)
      sqlBase.executeInsertOrUpdate(s"update ${mysqlDB}t_check set flag=1, message='$dayValue:aliyun_t_logdata_app 运行成功' where name='aliyun_t_logdata_app'", null)
      spark.catalog.clearCache()
    }catch {
      case e: Exception => {
        e.printStackTrace()
        try {
          sqlBase.executeInsertOrUpdate(s"update ${mysqlDB}t_check set flag=2, message='$dayValue:aliyun_t_logdata_app 运行失败' where name='aliyun_t_logdata_app'", null)
        } catch {
          case e: Throwable => {
            e.printStackTrace()
          }
        }
      }
    }
  }

  /**
   * 神策埋点数据清洗
   *
   * @param day 日期
   */
  def cleanAppScData(spark:SparkSession,day: String,dbEnv:String="default."): Unit = {
    val logTable = s"${dbEnv}t_ods_appsc_log"
    val sqlBase=new MySqlBaseDao()
    var mysqlDB="db_hitsstatistic."
    var kuduTable="t_logdata_appsc_streaming2"
    var localAppscDF=cleanLocalAppScData(spark,sqlBase,day)
    if (dbEnv != "default.") {
      kuduTable = "t_logdata_appsc_streaming2_test"
      mysqlDB = "db_hitsstatistic_test."
      localAppscDF=None
    }
    try {
      spark.sql(s"alter table $logTable drop if exists partition(day='$day')")
      println(s"alter table $logTable drop if exists partition(day='$day')")
      //查询kudu  数据
      val myKudu=new MyKudu(spark)
      myKudu.select(kuduTable).createOrReplaceTempView("t_appsc_tmp")
      val kuduAppscDF = spark.sql(
        s"""
           |select DISTINCT  cid ,uid ,event ,screen_name ,title ,referrer ,url ,element_id ,element_content ,
           |       type_id ,eventid,label ,action ,element_type ,element_position ,device_id ,manufacturer ,os ,model ,os_version ,
           |       app_id ,screen_width ,screen_height , app_version , ads , latitude,longitude,wifi , network_type , time ,
           |       resume_from_background ,event_duration ,cl,day,user_id,nvl(province,'') as province,nvl(city,'') as city,nvl(country,'') as country,nvl(referrer_title,'') as referrer_title,recv_time
           |from t_appsc_tmp where day='$day'
           |""".stripMargin)

          localAppscDF match{
            case Some(value)=>{
              value.union(kuduAppscDF)
                .createOrReplaceTempView("t_log_tmp")
            }
            case None=>{
              kuduAppscDF.createOrReplaceTempView("t_log_tmp")
            }
          }
      //匹配天眼ID,
      spark.sql(s"select uid,vid,dm_id,is_del,cs from t_dwd_dmid where day='$day' " +
        "and cs in('android_s','android_c','iOS_c','iOS_s')" +
        " and vid_type=0 and is_del=0 ").createOrReplaceTempView("t_dmid_tmp")
      spark.catalog.cacheTable("t_dmid_tmp")
      spark.sql("select distinct uid, dm_id from t_dmid_tmp where uid!=''").createOrReplaceTempView("t_uid_dmid")
      spark.sql("select distinct vid, dm_id,cs from t_dmid_tmp where is_del=0").createOrReplaceTempView("t_vid_dmid")
      spark.sql(
        """
          |select distinct l.*,nvl(d.dm_id,d1.dm_id) as dm_id from t_log_tmp  l
          |left join t_uid_dmid d on l.uid=d.uid and l.uid!=''
          |left join t_vid_dmid d1 on l.cid=d1.vid
          |-- and d1.cs=
          |-- if(l.app_id in ('com.che.truckhome.speed','com.360che.truckHomeRapidly'),concat(if(l.os='Android','android','iOS'),"_s"),concat(if(l.os='Android','android','iOS'),'_c'))
          |""".stripMargin)
        .repartition(4).write.mode("append").format("Hive")
        .partitionBy("day").saveAsTable(logTable)
      sqlBase.executeInsertOrUpdate(s"update ${mysqlDB}t_check set flag=1, message='$day:运行成功' where name='aliyun_t_ods_appsc_log'",null)
    }catch {
      case e: Exception => {
        e.printStackTrace()
        try{
          sqlBase.executeInsertOrUpdate(s"update ${mysqlDB}t_check set flag=2, message='$day:运行失败' where name='aliyun_t_ods_appsc_log'",null)
        }catch {
          case e:Throwable=>{
            e.printStackTrace()
          }
        }
      }
    }

  }

  /**
   * 清洗上报到本地的神策appsc日志
   */
  def cleanLocalAppScData(spark:SparkSession,sqlBase:MySqlBaseDao,day: String):Option[DataFrame] ={
    val mysqlDB="db_hitsstatistic."
    import spark.implicits._
    try {
      //重命名tmp文件（flume采集异常情况下会有tmp文件）
      val URI=ResourceBundle.getBundle("connection_underlay").getString("oss-hdfs.uri")
      val path=s"/pv-nginx-appsc-new/$day"
      val today=DateTranslate.getDate(0)
      //当天文件不自动重命名，因为正在采集过程中会有tmp文件
      if(today == day){
        println("当天采集数据不能重命名tmp文件")
      }else{
        HDFSFileRenamer.renameTmpFile(URI,path)
      }
      val dirPath = s"$URI$path/*.gz"
      val LogRdd = spark.read
        .json(dirPath).filter(t => t.getAs[String]("status") == "200" &&
        t.getAs[String]("request_body") != "-"
        && t.getAs[String]("http_host") == "thappsc.360che.com") //过滤业务日志，参数为"-"
      val redultDF=LogRdd.map(line => {
        var list: List[LogData] = Nil
        val requestBody = line.getAs[String]("request_body")
        var sendTime = line.getAs[String]("@timestamp");
        if (!DateTranslate.timeIsFormat(sendTime)) {
          sendTime = DateTranslate.transFormat(day,"yyyyMMdd","yyyy-MM-dd'T'HH:mm:ssXXX")
        }
        val recvTime = DateTranslate.tranTimeStringToLong(sendTime,"yyyy-MM-dd'T'HH:mm:ssXXX").toString()
        //对发送过来的埋点数据先url解码，再进行base64解码，最后进行解压
        val dataTmp = Common.getPValueByName(requestBody, "data_list")
        try {
          val decoder = Base64.getDecoder
          val dataList = GZIPUtils.uncompressToString(decoder.decode(UrlDecodeUtil.urlDecode(dataTmp)))
          val dataJson = JSON.parseArray(dataList)
          if (dataJson != null) {
            for (a <- 0 until dataJson.size()) {
              try {
                val argObj = dataJson.getJSONObject(a)
                val sc_type=argObj.getString("type")
                if(sc_type=="track"){
                  val properties = argObj.getJSONObject("properties")
                  val appVersion = properties.getString("$app_version")
                  //判断版本号  大于7.13.3 是1，等于7.13.3是0，小于7.13.3 是-1 ，
                  // 如果是0 和1  cid,type_id,label,action,ads,DownloadChannel就去掉$符号 否则就不去掉
                  if(versionCompare(appVersion,"7.14") == -1){
                    val isOnline = pattern findFirstIn appVersion
                    // 正则根据版本判断是否是测试数据，测试版本号都是4位，线上版本号都是3位
                    if (isOnline.isDefined) {
                      val time = argObj.getString("time")
                      val event = argObj.getString("event").replace("$", "")
                      val uid = argObj.getOrDefault("login_id", "").toString
                      val cid = properties.getOrDefault("$cid", "").toString
                      val screenName = properties.getString("$screen_name")
                      val title = properties.getOrDefault("$title", "").toString
                      val referrer = properties.getOrDefault("$referrer", "").toString
                      val url = properties.getOrDefault("$url", "").toString
                      val elementId = properties.getOrDefault("$element_id", "").toString
                      val elementContent = properties.getOrDefault("$element_content", "").toString
                      val typeId = properties.getOrDefault("$type_id", "").toString
                      val eventid = properties.getOrDefault("eventid", "").toString
                      //电话和姓名加密
                      var label = properties.getOrDefault("$label", "").toString
                      if(event=="CHES_00000004"){
                        val labelArr = label.split("\\|")
                        if(labelArr.length > 2){
                          labelArr(0) = new String(Base64.getEncoder.encode(labelArr(0).getBytes("UTF-8")))
                          labelArr(1) = new String(Base64.getEncoder.encode(labelArr(1).getBytes("UTF-8")))
                        }
                        label=labelArr.mkString("|")
                      }
                      val user_id =  properties.getOrDefault("user_id", "").toString
                      val action = properties.getOrDefault("$action", "").toString
                      val elementType = properties.getOrDefault("$element_type", "").toString
                      val elementPosition = properties.getOrDefault("$element_position", "").toString
                      val deviceId = properties.getString("$device_id")
                      val manufacturer = properties.getString("$manufacturer")
                      val os = properties.getString("$os")
                      val model = properties.getString("$model")
                      val osVersion = properties.getString("$os_version")
                      val appId = properties.getString("$app_id")
                      val screenWidth = properties.getString("$screen_width")
                      val screenHeight = properties.getString("$screen_height")
                      val ads = properties.getOrDefault("$ads", "").toString
                      var latitude = properties.getOrDefault("$latitude", "").toString
                      //神策数据中经纬度*E6,清洗过程中还原
                      if(!latitude.contains(".") && latitude!="" ) {
                        latitude  = (latitude.toInt*1.0 / 1000000).toString
                      }else if(latitude.contains("E")){
                        latitude= (BigDecimal.valueOf(latitude.toDouble).toInt *1.0 / 1000000).toString
                      }
                      var longitude = properties.getOrDefault("$longitude", "").toString
                      if(!longitude.contains(".") && longitude!="" ){
                        longitude  = (longitude.toInt*1.0 / 1000000).toString
                      }else if(longitude.contains("E")){
                        longitude=(BigDecimal.valueOf(longitude.toDouble).toInt *1.0 / 1000000).toString
                      }
                      val wifi = properties.getString("$wifi")
                      val networkType = properties.getString("$network_type")
                      val resumeFromBackground = properties.getOrDefault("$resume_from_background", "").toString
                      val eventDuration = properties.getOrDefault("event_duration", "").toString
                      val cl = properties.getOrDefault("$DownloadChannel", "").toString
                      val province = properties.getOrDefault("$province", "").toString
                      val city = properties.getOrDefault("$city", "").toString
                      val country = properties.getOrDefault("$country", "").toString
                      val referrer_title = properties.getOrDefault("$referrer_title", "").toString
                      list = list :+ LogData(cid, uid, event, screenName, title, referrer, url, elementId, elementContent, typeId,eventid, label, action,
                        elementType, elementPosition, deviceId, manufacturer, os, model, osVersion, appId, screenWidth, screenHeight, appVersion,
                        ads, latitude, longitude, wifi, networkType, time, resumeFromBackground, eventDuration, cl, day,user_id,province,city,country,referrer_title,recvTime)
                    }
                  }else {
                    val isOnline = pattern findFirstIn appVersion
                    // 正则根据版本判断是否是测试数据，测试版本号都是4位，线上版本号都是3位
                    if (isOnline.isDefined) {
                      val time = argObj.getString("time")
                      val event = argObj.getString("event").replace("$", "")
                      val uid = argObj.getOrDefault("login_id", "").toString
                      val cid = properties.getOrDefault("cid", "").toString
                      val screenName = properties.getString("$screen_name")
                      val title = properties.getOrDefault("$title", "").toString
                      val referrer = properties.getOrDefault("$referrer", "").toString
                      val url = properties.getOrDefault("$url", "").toString
                      val elementId = properties.getOrDefault("$element_id", "").toString
                      val elementContent = properties.getOrDefault("$element_content", "").toString
                      val typeId = properties.getOrDefault("type_id", "").toString
                      val eventid = properties.getOrDefault("eventid", "").toString
                      var label = properties.getOrDefault("label", "").toString
                      if(event=="CHES_00000004"){
                        val labelArr = label.split("\\|")
                        if(labelArr.length > 2){
                          labelArr(0) = new String(Base64.getEncoder.encode(labelArr(0).getBytes("UTF-8")))
                          labelArr(1) = new String(Base64.getEncoder.encode(labelArr(1).getBytes("UTF-8")))
                        }
                        label=labelArr.mkString("|")
                      }
                      val action = properties.getOrDefault("action", "").toString
                      val user_id =  properties.getOrDefault("user_id", "").toString
                      val elementType = properties.getOrDefault("$element_type", "").toString
                      val elementPosition = properties.getOrDefault("$element_position", "").toString
                      val deviceId = properties.getString("$device_id")
                      val manufacturer = properties.getString("$manufacturer")
                      val os = properties.getString("$os")
                      val model = properties.getString("$model")
                      val osVersion = properties.getString("$os_version")
                      val appId = properties.getString("$app_id")
                      val screenWidth = properties.getString("$screen_width")
                      val screenHeight = properties.getString("$screen_height")
                      val ads = properties.getOrDefault("ads", "").toString
                      var latitude = properties.getOrDefault("$latitude", "").toString
                      //神策数据中经纬度*E6,清洗过程中还原
                      if(!latitude.contains(".") && latitude!="" ) {
                        latitude  = (latitude.toInt*1.0 / 1000000).toString
                      }else if(latitude.contains("E")){
                        latitude= (BigDecimal.valueOf(latitude.toDouble).toInt *1.0 / 1000000).toString
                      }
                      var longitude = properties.getOrDefault("$longitude", "").toString
                      if(!longitude.contains(".") && longitude!="" ){
                        longitude  = (longitude.toInt*1.0 / 1000000).toString
                      }else if(longitude.contains("E")){
                        longitude=(BigDecimal.valueOf(longitude.toDouble).toInt *1.0 / 1000000).toString
                      }
                      val wifi = properties.getString("$wifi")
                      val networkType = properties.getString("$network_type")
                      val resumeFromBackground = properties.getOrDefault("$resume_from_background", "").toString
                      val eventDuration = properties.getOrDefault("event_duration", "").toString
                      val cl = properties.getOrDefault("DownloadChannel", "").toString
                      val province = properties.getOrDefault("$province", "").toString
                      val city = properties.getOrDefault("$city", "").toString
                      val country = properties.getOrDefault("$country", "").toString
                      val referrer_title = properties.getOrDefault("$referrer_title", "").toString
                      list = list :+ LogData(cid, uid, event, screenName, title, referrer, url, elementId, elementContent, typeId, eventid,label, action,
                        elementType, elementPosition, deviceId, manufacturer, os, model, osVersion, appId, screenWidth, screenHeight, appVersion,
                        ads, latitude, longitude, wifi, networkType, time, resumeFromBackground, eventDuration, cl, day,user_id,province,city,country,referrer_title,recvTime)
                    }
                  }
                }
              } catch {
                case ex: ClassCastException => {
                  ex.printStackTrace()
                }
                case ex: NullPointerException => {
                  ex.printStackTrace()
                }
              }
            }
          }

        } catch {
          case ex: Exception => {
            ex.printStackTrace()
          }
        }
        list
      }).flatMap(x => x).toDF("cid", "uid", "event", "screen_name", "title", "referrer", "url", "element_id", "element_content",
        "type_id", "eventid" ,"label", "action", "element_type", "element_position", "device_id", "manufacturer", "os", "model", "os_version",
        "app_id", "screen_width", "screen_height", "app_version", "ads", "latitude", "longitude", "wifi", "network_type", "time",
        "resume_from_background", "event_duration", "cl", "day","user_id","province","city","country","referrer_title","recv_time")
      Some(redultDF)
    }catch {
      case e: Exception => {
        e.printStackTrace()
        None
        try{
          sqlBase.executeInsertOrUpdate(s"update ${mysqlDB}t_check set flag=2, message='$day:运行失败' where name='aliyun_t_ods_appsc_log'",null)
          None
        }catch {
          case e:Throwable=>{
            e.printStackTrace()
            None
          }
        }
      }
    }
  }

  /**
   * 维护kudu表中的数据，定时清洗
   *
   * @param day 日期
   */
  def kuduTableMaintain(day: String): Unit = {
    val spark = MySparkSession.conn()
    val myKudu=new MyKudu(spark)
    val appLogKuduTable = "t_logdata_app_streaming"
    val appLogKuduTestTable = "t_logdata_app_streaming_test"
    val appscLogKuduTable = "t_logdata_appsc_streaming"
    val appscLogKuduTestTable = "t_logdata_appsc_streaming_test"
    val newsWebLogTable="t_ods_info_webdata_streaming"
    val newsAppLogTable="t_ods_info_appdata_streaming"
    val newsAppscLogTable="t_ods_info_appscdata_streaming"
    val kuduMasters = ResourceBundle.getBundle("connection").getString("kudu.master")
    spark.conf.set("spark.rpc.askTimeout", "600s")
    // 删除两天前的实时数据
    val before2Day = Common.getSpecifyDate(day, 2)
    val before3Day = Common.getSpecifyDate(day, 3)
    myKudu.select(appLogKuduTable).createOrReplaceTempView("t_app_tmp")
    val appAndroidOldData = spark.sql(s"select id from t_app_tmp where day<='$before2Day' and cs='android'")
    val appIosOldData = spark.sql(s"select id from t_app_tmp where day<='$before2Day' and cs='iOS'")
    myKudu.select(appscLogKuduTable).createOrReplaceTempView("t_appsc_tmp")
    val appAndroidScOldData = spark.sql(s"select id from t_appsc_tmp where  day<='$before2Day' and os='Android'")
    val appIosScOldData = spark.sql(s"select id from t_appsc_tmp where  day<='$before2Day'")
    //删除测试环境两天前的测试数据
    val appLogKuduTestData=myKudu.select(appLogKuduTestTable).select("id").where(s"day<='$before2Day'")
    myKudu.delete(appLogKuduTestData,appLogKuduTestTable)
    val appscLogKuduTestData=myKudu.select(appscLogKuduTestTable).select("id").where(s"day<='$before2Day'")
    myKudu.delete(appscLogKuduTestData,appscLogKuduTestTable)

    val kuduContext = new KuduContext(kuduMasters, spark.sparkContext)
    kuduContext.deleteRows(appAndroidOldData, appLogKuduTable)
    kuduContext.deleteRows(appIosOldData, appLogKuduTable)
    kuduContext.deleteRows(appAndroidScOldData, appscLogKuduTable)
    kuduContext.deleteRows(appIosScOldData, appscLogKuduTable)
    //处理web日志
    myKudu.select("t_logdata_streaming").createOrReplaceTempView("t_web_tmp")
    for (h <- 0 to 23) {
      val webOldData = spark.sql(s"select id from t_web_tmp where  day<='$before2Day' and hours=$h")
      kuduContext.deleteRows(webOldData, "t_logdata_streaming")
    }

    //    删除资讯清洗数据
    val newsWebLogData=myKudu.select(newsWebLogTable).select("id").where(s"day<='$before3Day'")
    myKudu.delete(newsWebLogData,newsWebLogTable)
    val newsAppscLogData=myKudu.select(newsAppscLogTable).select("id").where(s"day<='$before3Day'")
    myKudu.delete(newsAppscLogData,newsAppscLogTable)
    val newsAppLogData=myKudu.select(newsAppLogTable).select("id").where(s"day<='$before3Day'")
    myKudu.delete(newsAppLogData,newsAppLogTable)
    spark.stop()

  }

  def getP(p: String): String = {
    var str: String = ""
    if (p != "null|null|null") {
      str = p
    }
    str
  }


  /**
   * 时间戳转换为日期类型
   * @param tm 时间戳字符串
   * @return
   */
  def tranTimeToString(tm: String): String = {
    val fm = new SimpleDateFormat("yyyyMMdd")
    var newTm = tm
    if (tm.length == 10) {
      newTm = tm + "000"
    }
    val tim = fm.format(new Date(newTm.toLong))
    tim
  }

  /**
   * app版本比较
   * @param v1 版本号1
   * @param v2 版本号2
   * @return v1>v2返回1，v1<v2返回-1，v1=v2返回0
   */
  private def versionCompare(v1:String,v2:String):Int = {
    val versionTuple = v1.split("\\.").zip(v2.split("\\."))
    versionTuple.foreach(i => {
      if (i._1.toInt.compare(i._2.toInt) != 0) {
        return i._1.toInt.compare(i._2.toInt)
      }
    })
    0
  }

  /**
   * 管理kudu分区，动态创建range分区，动态删除过期分区
   * 增加分区为一年后，删除分区为三个月前
   * 举例：当前日期为20220603，删除分区范围为(202202,202203)，创建范围分区为
   * 每月1号创建新的分区，删除过期分区
   * @param day 当前日期 yyyyMMdd
   * @param tableName 需要维护的表名
   * @param schema 表的schema信息
   */
  def kuduPartitionManage(day:String,tableName:String,schema:Schema): Unit ={
    try{
      val kuduMasters = ResourceBundle.getBundle("connection").getString("kudu.master")
      //获取kudu客户端对象
      val kuduClient: KuduClient = new KuduClient.KuduClientBuilder(kuduMasters).build()
      //获取当前日期，计算需要添加分区的月份范围；计算需要删除的月份范围
      val sdf1 = new SimpleDateFormat("yyyyMMdd")
      val sdf2 = new SimpleDateFormat("yyyyMM")
      val calendar = Calendar.getInstance()
      val date1 = sdf1.parse(day)
      calendar.setTime(date1)
      calendar.set(Calendar.DATE,1)
      calendar.add(Calendar.MONTH,12)
      val addDate1=sdf2.format(calendar.getTime())
      calendar.add(Calendar.MONTH,1)
      val addDate2=sdf2.format(calendar.getTime())
      println(addDate1,addDate2)
      calendar.setTime(date1)
      calendar.set(Calendar.DATE,1)
      calendar.add(Calendar.MONTH,-4)
      val delDate1=sdf2.format(calendar.getTime())
      calendar.add(Calendar.MONTH,1)
      val delDate2=sdf2.format(calendar.getTime())
      println(delDate1,delDate2)
      val options=new AlterTableOptions()
      val addLower=schema.newPartialRow()
      addLower.addString("month",addDate1)
      val addUpper=schema.newPartialRow()
      addUpper.addString("month",addDate2)
      //新建分区
      options.addRangePartition(addLower,addUpper)
      val delLower=schema.newPartialRow()
      delLower.addString("month",delDate1)
      val delUpper=schema.newPartialRow()
      delUpper.addString("month",delDate2)
      //删除过期分区
      options.dropRangePartition(delLower,delUpper)
      //执行操作
      kuduClient.alterTable(tableName,options)
    }catch {
      case exception: Exception=>{
        //维护分区操作异常，发送钉钉预警
        exception.printStackTrace()
        val robot = new DingRobot(ResourceBundle.getBundle("robot").getString("ding.monitor"))
        robot.sendAlert(s"【监控--appsc日志清洗】\nkudu表维护失败\n原因:${exception.getMessage.replace('"',' ')}")
      }
    }
  }
  /**
   * 由于t_ods_appsc_log表中有不能映射到dm_id的数据，
   * 把这部分数据的cid单独作为dm_id保存,数据保存在mysql
   */
  def dmIdSupplement(day:String,dbEnv:String="default."): Unit ={
    val spark = MySparkSession.conn()
    var sqlBase=new MySqlBaseDao()
    val table=s"${dbEnv}t_ods_appsc_log"
    var mysqlDB="db_hitsstatistic."
    if (dbEnv !="default."){
      sqlBase=new MySqlBaseDao("che_test")
      mysqlDB="db_hitsstatistic_test."
    }
    val resultDF=spark.sql(
      s"""
         |select distinct cid as dm_id,day from $table where day='$day'
         |and dm_id is null and cid <>""
         |""".stripMargin)
    //    历史数据处理
    //    val resultDF2=spark.sql(
    //      s"""
    //         |select cid as dm_id,day from t_ods_appsc_log where dm_id is null and cid is not null group by day,cid
    //         |""".stripMargin)
    sqlBase.execute(s"delete from ${mysqlDB}t_dmid_supplement where day='$day'")
    sqlBase.saveMysql(resultDF,s"${mysqlDB}t_dmid_supplement")
  }

  def test(): Unit ={
    val av = "********"
    val isOnline = pattern findFirstIn av
    println(isOnline.isDefined || av == "********")
//    spark.sql(
//      s"""
//         |select cid ,uid ,event ,screen_name ,title ,referrer ,url ,element_id ,element_content ,
//         |       type_id ,eventid,label ,action ,element_type ,element_position ,device_id ,manufacturer ,os ,model ,os_version ,
//         |       app_id ,screen_width ,screen_height , app_version , ads , latitude,longitude,wifi , network_type , time ,
//         |       resume_from_background ,event_duration ,cl,day,user_id from t_ods_appsc_log where day='$day'
//         |""".stripMargin).createOrReplaceTempView("t_log_tmp")
//    //匹配天眼ID,
//    spark.sql(s"select uid,vid,dm_id,is_del,cs from t_dwd_dmid where day='$day' " +
//      "and cs in('android_s','android_c','iOS_c','iOS_s')" +
//      " and vid_type=0 and is_del=0 ").createOrReplaceTempView("t_dmid_tmp")
//    spark.catalog.cacheTable("t_dmid_tmp")
//    spark.sql("select distinct uid, dm_id from t_dmid_tmp where uid!=''").createOrReplaceTempView("t_uid_dmid")
//    spark.sql("select distinct vid, dm_id,cs from t_dmid_tmp where is_del=0").createOrReplaceTempView("t_vid_dmid")
//    spark.sql(
//      """
//        |select distinct l.*,nvl(d.dm_id,d1.dm_id) as dm_id from t_log_tmp  l left join t_uid_dmid d on l.uid=d.uid and l.uid!=''
//        |left join t_vid_dmid d1 on l.cid=d1.vid
//        |-- and d1.cs=
//        |-- if(l.app_id in ('com.che.truckhome.speed','com.360che.truckHomeRapidly'),concat(if(l.os='Android','android','iOS'),"_s"),concat(if(l.os='Android','android','iOS'),'_c'))
//        |""".stripMargin)
//      .repartition(4).write.mode("append").format("Hive")
//      .partitionBy("day").saveAsTable("db_test.t_ods_appsc_log_20221031")
  }
}
