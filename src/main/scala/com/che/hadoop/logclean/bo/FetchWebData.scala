package com.che.hadoop.logclean.bo

import com.che.hadoop.underlay.dao

import scala.collection.mutable.{ListBuffer, Set}
import scala.collection.JavaConversions._
import com.che.hadoop.logclean.utils.JsonQuery

import java.sql.ResultSet
import java.util.ResourceBundle
import com.alibaba.fastjson.JSON
import com.che.hadoop.underlay.dao.{MySparkSession, MySqlBaseDao}
import com.che.hadoop.underlay.tool.http.MyHttpService
import org.apache.spark.sql.SparkSession
import scalaj.http.Http
/*
* @author:张松伟
* 获取web数据导入到hive表中
* 从接口中读取数据
*/
object FetchWebData {
  val t_check_pre="aliyun_"
  val dataType_dealer = "dealer"
  val dataType_secondHandTruck = "secondHandTruck"
  val dataType_secondHandPostTruck = "secondHandPostTruck"
  val dataType_attach = "aliyun_attach"
  val lastDay = com.che.hadoop.logclean.utils.function_.getWeekNum(1)
  val nowDay = com.che.hadoop.logclean.utils.function_.getWeekNum(0)
  def main(args: Array[String]): Unit = {
    val day="20240223"
    val spark=MySparkSession.conn()
    val sqlBase=new MySqlBaseDao()
//    //*todo nearbyShops没有按天分区，重复执行数据会重复
    getKeyWordAlias(spark,sqlBase,day)
//    FetchWebData.secondHandTruck(spark,"default.",day)
//      val hiveDB = "db_test."
//    val hiveDB = "default."
//    dealer(hiveDB)
//    secondHandTruck(hiveDB)
//    secondHandPostTruck(hiveDB)
  }
 /**
  *经销商数据
  *不再通过 t_max_timestamp表更新数据
  */
  def dealer(spark:SparkSession,hiveDB:String,day:String):Unit = {
    try{
      import spark.implicits._
      import spark.sql
      val exec = new dao.MySqlBaseDao()
//      var rs:ResultSet = exec.executeQuery("select max(max_timestamp) from t_max_timestamp where data_name=? ", Array(dataType_dealer))
//      rs.next()
//      val maxTimestamp = rs.getLong(1)
      val maxTimestamp = sql("SELECT max(unix_timestamp(usercreatetime, 'yyyy-MM-dd HH:mm:ss')) from t_dealer").first().getLong(0)
      val sign = com.che.hadoop.logclean.utils.function_.MD5(4 +"df45se4r34#45v")
      val response = com.che.hadoop.logclean.utils.function_.request_data(ResourceBundle.getBundle("connection").getString("api.dealer_user") + maxTimestamp.toString + "&businessID=4&sign=" + sign)
      if(response == null){
        throw new Throwable("request failed:no data")
      }
      val j = new JsonQuery(response.body)
//      exec.executeInsertOrUpdate("insert t_max_timestamp (data_name, day, last_result) values(?,?,?) on duplicate key update last_result=?", Array(dataType_dealer, lastDay, response.body, response.body))
      if (j.mi("status") == 0) {
        throw new Throwable("request failed:" + j.ms("msg"))
      }
      val dataList = new ListBuffer[(Long, String, Int, String, String, Long, String, Int, String, String, String, String, Int, Int)]
      for(i <- j.m("data")){
        val t = new JsonQuery(i)
        dataList.append((t.ms("uid").toLong, t.ms("userName"), t.mi("userIsDelete"), t.ms("userCreateTime"), t.ms("userPhone").stripMargin, t.ms("dealerId").toLong, t.ms("dealerName"), t.mi("dealerIsDelete"), t.ms("dealerCreateTime"), t.ms("dealerPhone"),t.ms("provinceName"), t.ms("cityName"), t.mi("provinceSn"), t.mi("citySn")))
      }
      val dataColArray: Array[String] = Array("uid", "userName", "userIsDelete", "userCreateTime", "userPhone", "dealerId", "dealerName", "dealerIsDelete", "dealerCreateTime", "dealerPhone", "provinceName","cityName" ,"provinceSn", "citySn")
      dataList.toDF(dataColArray:_*).createOrReplaceTempView("data_new_dealer")
      sql("set spark.sql.shuffle.partitions=1")
      sql("insert overwrite table " + hiveDB + " t_dealer select coalesce(b.uid,a.uid)as uid,coalesce(b.userName,a.userName)as userName,coalesce(b.userIsDelete,a.userIsDelete)as userIsDelete,coalesce(b.userCreateTime,a.userCreateTime)as userCreateTime,coalesce(b.userPhone,a.userPhone)as userPhone,coalesce(b.dealerId,a.dealerId)as dealerId,coalesce(b.dealerName,a.dealerName)as dealerName,coalesce(b.dealerIsDelete,a.dealerIsDelete)as dealerIsDelete,coalesce(b.dealerCreateTime,a.dealerCreateTime)as dealerCreateTime, coalesce(b.dealerPhone,a.dealerPhone)as dealerPhone, coalesce(b.provinceName,a.provinceName)as provinceName, coalesce(b.cityName,a.cityName)as cityName, coalesce(b.provinceSn,a.provinceSn)as provinceSn, coalesce(b.citySn,a.citySn)as citySn from t_dealer a full join data_new_dealer b on (a.uid = b.uid)")
//      exec.executeInsertOrUpdate("insert ignore t_max_timestamp (data_name, day, max_timestamp) values(?,?,?) on duplicate key update max_timestamp=?", Array(dataType_dealer, nowDay, j.ms("maxTimestamp"), j.ms("maxTimestamp")))
      exec.executeInsertOrUpdate(s"update t_check set flag = 1,message='$day:运行成功' where name=?",Array(s"${t_check_pre}t_" + dataType_dealer))
    }catch {
      case e: Throwable => {
        e.printStackTrace()
        try{
          val exec = new dao.MySqlBaseDao()

          exec.executeInsertOrUpdate(s"update t_check set flag=2, message='$day:${t_check_pre}t_$dataType_dealer 运行失败' where name=?",Array(s"${t_check_pre}t_" + dataType_dealer))
        }catch {
          case e:Throwable=>{
            e.printStackTrace()
          }
        }
      }
    }
  }
  /**
    *二手车数据
   */
  def secondHandTruck(spark:SparkSession,hiveDB:String,day:String):Unit = {
    try{
      import spark.implicits._
      import spark.sql
      val exec = new MySqlBaseDao()
      val maxTimestamp = sql("SELECT max(unix_timestamp(usercreatetime, 'yyyy-MM-dd HH:mm:ss')) from t_secondhandtruck").first().getLong(0)
      val url = ResourceBundle.getBundle("connection").getString("api.second_truck_user") + maxTimestamp.toString
      var response = com.che.hadoop.logclean.utils.function_.request_data(url)
      if(response == null){
        exec.executeInsertOrUpdate(s"update t_check set flag = 2, message='$day:t_$dataType_secondHandTruck response=null' where name=?",Array("t_" + dataType_secondHandTruck))
      }else{
        val j = JSON.parseObject(response.body)
        exec.executeInsertOrUpdate("insert t_max_timestamp (data_name, day, last_result) values(?,?,?) on duplicate key update last_result=?", Array(dataType_secondHandTruck, lastDay, response.body, response.body))
        if (j.getIntValue("status") == 0) {
          exec.executeInsertOrUpdate(s"update t_check set flag = 2, message='$day:t_$dataType_secondHandTruck status=0' where name=?",Array("t_" + dataType_secondHandTruck))
        }else{
          if(j.getIntValue("totalNum") != 0){
            val dataList = new ListBuffer[(Long, Int, Int, String, Long, String, String,Int,Int,String,String,String,Int)]
            for(i <- j.getJSONArray("data")){
              val t = JSON.parseObject(i.toString)
              dataList.append((t.getIntValue("uid").toLong, t.getIntValue("userIsDelete"), t.getIntValue("isVIP"), t.getString("userCreateTime"), t.getString("companyId").toLong, t.getString("companyName"), t.getString("companyCreateTime"),t.getIntValue("onMain"),t.getIntValue("onSold"),t.getString("licence"),t.getString("storeStatus"),t.getString("status"),t.getIntValue("isOpen")))
            }
            dataList.toDF("uid", "userIsDelete", "isVIP", "userCreateTime", "companyId", "companyName", "companyCreateTime","onMain","onSold","licence","storeStatus","licenceStatus","isOpen").createOrReplaceTempView("data_new_secondHandTruck")
            sql("set spark.sql.shuffle.partitions=1")
            sql(
              s"""
                 |insert overwrite table ${hiveDB}t_secondHandTruck
                 | select coalesce(b.uid,a.uid)as uid,
                 | coalesce(b.userIsDelete,a.userIsDelete)as userIsDelete,
                 | coalesce(b.userCreateTime,a.userCreateTime)as userCreateTime,
                 | coalesce(b.companyId,a.companyId)as companyId,
                 | coalesce(b.companyName,a.companyName)as companyName,
                 | coalesce(b.companyCreateTime,a.companyCreateTime)as companyCreateTime,
                 | coalesce(b.isVIP,a.isVIP)as isVIP,
                 | coalesce(b.onMain,a.onMain)as onMain,
                 | coalesce(b.onSold,a.onSold)as onSold,
                 | coalesce(b.licence,a.licence)as licence,
                 | coalesce(b.storeStatus,a.storeStatus)as storeStatus,
                 | coalesce(b.licenceStatus,a.licenceStatus)as licenceStatus,
                 | coalesce(b.isOpen,a.isOpen)as isOpen from ${hiveDB}t_secondHandTruck a
                 |full join data_new_secondHandTruck b on (a.uid = b.uid)
                 |""".stripMargin)
            exec.executeInsertOrUpdate("insert ignore t_max_timestamp (data_name, day, max_timestamp) values(?,?,?) on duplicate key update max_timestamp=?", Array(dataType_secondHandTruck, nowDay, j.getString("maxTimestamp"), j.getString("maxTimestamp")))
          }
          exec.executeInsertOrUpdate(s"update t_check set flag = 1,message='$day:运行成功' where name=?",Array( s"${t_check_pre}t_" + dataType_secondHandTruck))
        }
      }

    }catch {
      case e: Throwable => {
        e.printStackTrace()
        try{
          val exec = new dao.MySqlBaseDao()

          exec.executeInsertOrUpdate(s"update t_check set flag=2, message='$day:${t_check_pre}t_$dataType_secondHandTruck 运行失败' where name=?",Array(s"${t_check_pre}t_" + dataType_secondHandTruck))
        }catch {
          case e:Throwable=>{
            e.printStackTrace()
          }
        }
      }
    }
  }

  /**
    *二手车发布数据
    */
  def secondHandPostTruck(spark:SparkSession,hiveDB:String,day:String):Unit = {
    try{

      val exec = new dao.MySqlBaseDao()
      import spark.implicits._
      import spark.sql
      val maxTimestamp = sql("SELECT max(unix_timestamp(createTime, 'yyyy-MM-dd HH:mm:ss')) from t_secondhandposttruck").first().getLong(0)
      val url = ResourceBundle.getBundle("connection").getString("api.second_truck_data") + maxTimestamp.toString
      val response = com.che.hadoop.logclean.utils.function_.request_data(url)
      if(response == null){
        exec.executeInsertOrUpdate(s"update t_check set flag = 2, message='$day:t_$dataType_secondHandPostTruck 首次请求时status为0'  where name=?",Array("t_" + dataType_secondHandPostTruck))
      }else{
        val j = new JsonQuery(response.body)
        exec.executeInsertOrUpdate("insert t_max_timestamp (data_name, day, last_result) values(?,?,?) on duplicate key update last_result=?", Array(dataType_secondHandPostTruck, lastDay, response.body, response.body))
        if (j.mi("status") == 0) {
          exec.executeInsertOrUpdate(s"update t_check set flag = 2, message='$day:t_$dataType_secondHandPostTruck 首次请求时status为0'  where name=?",Array("t_" + dataType_secondHandPostTruck))
        }else {
          if (j.mi("totalNum") != 0) {
            //      var dfSO = sql("select maxtimestamp from t_maxtimestamp where data_name='dealer'")
            //      val maxTimestamp = dfSO.first().getAs[Long]("maxtimestamp")
            val dataList = new ListBuffer[(Long, Long, Int, Int, Int, Int, Int, Int, Int, Int, Int, Int, String, String, String, String, String, Int, Int, Int, Int, Int)]

            for (i <- j.m("data")) {
              val t = new JsonQuery(i.replace("null", "0"))
              dataList.append((
                t.ms("id").toLong,
                t.ms("uid").toLong,
                t.mi("category"),
                t.mi("catId"),
                t.mi("brandId"),
                t.mi("seriesId"),
                t.mi("tradProvNo"),
                t.mi("tradCityNo"),
                t.mi("tradAreaNo"),
                t.mi("onSale"),
                t.mi("shelves"),
                t.mi("publisher"),
                t.ms("lastModified"),
                t.ms("closingTime"),
                t.ms("createTime"),
                t.ms("shelvesDate"),
                t.ms("refreshDate"),
                t.mi("isDelete"),
                t.mi("type"),
                t.mi("number"),
                t.mi("isSelf"),
                t.mi("tonnageId")))
            }
            dataList.toDF("id", "uid", "category", "catId", "brandId", "seriesId", "tradProvNo", "tradCityNo", "tradAreaNo", "onSale", "shelves", "publisher", "lastModified", "closingTime", "createTime", "shelvesDate", "refreshDate", "isDelete", "type", "number", "isSelf", "tonnageId").createOrReplaceTempView("data_new_secondHandPostTruck")
            sql("set spark.sql.shuffle.partitions=1")
            sql("insert overwrite table " + hiveDB + "t_secondHandPostTruck select coalesce(b.id,a.id)as id,coalesce(b.uid,a.uid)as uid,coalesce(b.category,a.category)as category,coalesce(b.catId,a.catId)as catId,coalesce(b.brandId,a.brandId)as brandId,coalesce(b.seriesId,a.seriesId)as seriesId,coalesce(b.tradProvNo,a.tradProvNo)as tradProvNo,coalesce(b.tradCityNo,a.tradCityNo)as tradCityNo,coalesce(b.tradAreaNo,a.tradAreaNo)as tradAreaNo,coalesce(b.onSale,a.onSale)as onSale,coalesce(b.shelves,a.shelves)as shelves,coalesce(b.publisher,a.publisher)as publisher,coalesce(b.lastModified,a.lastModified)as lastModified,coalesce(b.closingTime,a.closingTime)as closingTime,coalesce(b.createTime,a.createTime)as createTime,coalesce(b.shelvesDate,a.shelvesDate)as shelvesDate,coalesce(b.refreshDate,a.refreshDate)as refreshDate,coalesce(b.isDelete,a.isDelete)as isDelete,coalesce(b.type,a.type)as type,coalesce(b.number,a.number)as number,coalesce(b.isSelf,a.isSelf)as isSelf,coalesce(b.tonnageId,a.tonnageId)as tonnageId from  " + hiveDB + "t_secondHandPostTruck a full join data_new_secondHandPostTruck b on (a.id = b.id)")
            dataList.clear()
            val totalNum = j.m("page").mi("totalCount")
            if (totalNum > 1000) {
              var flag = true
              for (pageNum <- 2 to math.ceil(totalNum / 1000.0).toInt if flag) {
                val dataList = new ListBuffer[(Long, Long, Int, Int, Int, Int, Int, Int, Int, Int, Int, Int, String, String, String, String, String, Int, Int, Int, Int, Int)]
                val url_t = url + maxTimestamp.toString + "&page=" + pageNum
                val response_t = com.che.hadoop.logclean.utils.function_.request_data(url_t)
                if (response_t == null) {
                  exec.executeInsertOrUpdate(s"update t_check set flag = 2, message='$day:t_$dataType_secondHandPostTruck 多页请求时未请求到数据' where name=?",Array("t_" + dataType_secondHandPostTruck))
                  flag = false
                }else{
                  val j_t = new JsonQuery(response_t.body)
                  if (j_t.mi("status") == 0) {
                    exec.executeInsertOrUpdate(s"update t_check set flag = 2, message='$day:t_$dataType_secondHandPostTruck 多页请求时status为0' where name=?",Array("t_" + dataType_secondHandPostTruck))
                    flag = false
                  }else{
                    for (i <- j_t.m("data")) {
                      val t = new JsonQuery(i.replace("null", "0"))
                      dataList.append((
                        t.ms("id").toLong,
                        t.ms("uid").toLong,
                        t.mi("category"),
                        t.mi("catId"),
                        t.mi("brandId"),
                        t.mi("seriesId"),
                        t.mi("tradProvNo"),
                        t.mi("tradCityNo"),
                        t.mi("tradAreaNo"),
                        t.mi("onSale"),
                        t.mi("shelves"),
                        t.mi("publisher"),
                        t.ms("lastModified"),
                        t.ms("closingTime"),
                        t.ms("createTime"),
                        t.ms("shelvesDate"),
                        t.ms("refreshDate"),
                        t.mi("isDelete"),
                        t.mi("type"),
                        t.mi("number"),
                        t.mi("isSelf"),
                        t.mi("tonnageId")))
                    }
                    dataList.toDF("id", "uid", "category", "catId", "brandId", "seriesId", "tradProvNo", "tradCityNo", "tradAreaNo", "onSale", "shelves", "publisher", "lastModified", "closingTime", "createTime", "shelvesDate", "refreshDate", "isDelete", "type", "number", "isSelf", "tonnageId").createOrReplaceTempView("data_new_secondHandPostTruck")
                    sql("insert overwrite table " + hiveDB + "t_secondHandPostTruck select coalesce(b.id,a.id)as id,coalesce(b.uid,a.uid)as uid,coalesce(b.category,a.category)as category,coalesce(b.catId,a.catId)as catId,coalesce(b.brandId,a.brandId)as brandId,coalesce(b.seriesId,a.seriesId)as seriesId,coalesce(b.tradProvNo,a.tradProvNo)as tradProvNo,coalesce(b.tradCityNo,a.tradCityNo)as tradCityNo,coalesce(b.tradAreaNo,a.tradAreaNo)as tradAreaNo,coalesce(b.onSale,a.onSale)as onSale,coalesce(b.shelves,a.shelves)as shelves,coalesce(b.publisher,a.publisher)as publisher,coalesce(b.lastModified,a.lastModified)as lastModified,coalesce(b.closingTime,a.closingTime)as closingTime,coalesce(b.createTime,a.createTime)as createTime,coalesce(b.shelvesDate,a.shelvesDate)as shelvesDate,coalesce(b.refreshDate,a.refreshDate)as refreshDate,coalesce(b.isDelete,a.isDelete)as isDelete,coalesce(b.type,a.type)as type,coalesce(b.number,a.number)as number,coalesce(b.isSelf,a.isSelf)as isSelf,coalesce(b.tonnageId,a.tonnageId)as tonnageId from  " + hiveDB + "t_secondHandPostTruck a full join data_new_secondHandPostTruck b on (a.id = b.id)")
                    dataList.clear()
                  }
                }
              }
              if(flag){
                exec.executeInsertOrUpdate(s"update t_check set flag = 1,message='$day:运行成功' where name=?", Array(s"${t_check_pre}t_" + dataType_secondHandPostTruck))
              }
            }else{
              exec.executeInsertOrUpdate("insert ignore t_max_timestamp (data_name, day, max_timestamp) values(?,?,?) on duplicate key update max_timestamp=?", Array(dataType_secondHandPostTruck, nowDay, j.ms("maxTimestamp"), j.ms("maxTimestamp")))
              exec.executeInsertOrUpdate(s"update t_check set flag = 1,message='$day:运行成功' where name=?", Array(s"${t_check_pre}t_" + dataType_secondHandPostTruck))
            }
          }else{
            exec.executeInsertOrUpdate(s"update t_check set flag = 1,message='$day:运行成功' where name=?", Array(s"${t_check_pre}t_" + dataType_secondHandPostTruck))
          }
        }
      }
    }catch {
      case e: Throwable => {
        e.printStackTrace()
        try{
          val exec = new dao.MySqlBaseDao()

          exec.executeInsertOrUpdate(s"update t_check set flag=2, message='$day:${t_check_pre}t_$dataType_secondHandPostTruck 运行失败' where name=?",Array(s"${t_check_pre}t_" + dataType_secondHandPostTruck))
        }catch {
          case e:Throwable=>{
            e.printStackTrace()
          }
        }
      }
    }
  }
  /**
   *挂靠宝数据
   *项目停止2021-04-30
   */
  def attach(spark:SparkSession,hiveDB:String,day:String):Unit = {
    try{
      import spark.implicits._
      import spark.sql
      val exec = new dao.MySqlBaseDao()
      var rs:ResultSet = exec.executeQuery("select max(max_timestamp) from t_max_timestamp where data_name=? ", Array(dataType_attach))
      rs.next()
      val maxTimestamp = rs.getLong(1)
      val sign = com.che.hadoop.logclean.utils.function_.MD5("DSJ-GKB" + maxTimestamp.toString)
      var response = com.che.hadoop.logclean.utils.function_.request_data("https://gk.360che.com/api/company-car-info/get-user-info?time=" + maxTimestamp.toString+"&sign=" + sign)
      if(response == null){
        throw new Throwable("request failed:no data")
      }
      val j = new JsonQuery(response.body)
      exec.executeInsertOrUpdate("insert t_max_timestamp (data_name, day, last_result) values(?,?,?) on duplicate key update last_result=?", Array(dataType_attach, lastDay, response.body, response.body))
      if (j.mi("status") == 0) {
        throw new Throwable("request failed:" + j.ms("msg"))
      }
      val dataList = new ListBuffer[(String, Int, Int, String, String, String, String, String, String)]
      val unique = Set[String]()
      for(i <- j.m("data")){
        val t = new JsonQuery(i)
        val uniqueKey = t.ms("userPhone") + t.ms("licenceNum")
        if(!unique.contains(uniqueKey)){
          dataList.append((t.ms("userPhone"),t.mi("userIsDelete"),t.mi("truckIsDelete"), t.ms("userName"), t.ms("createTime"),t.ms("licenceNum"),  t.ms("cardId"), t.ms("companyName"), t.ms("brandName")))
          unique.add(uniqueKey)
        }
      }
      val dataColArray: Array[String] = Array("userPhone","userIsDelete","truckIsDelete", "userName", "createTime","licenceNum",  "cardId", "companyName", "brandName")
      dataList.toDF(dataColArray:_*).createOrReplaceTempView("data_new_attach")
      sql("set spark.sql.shuffle.partitions=1")
      sql("insert overwrite table " + hiveDB + " t_attach select coalesce(b.userphone,a.userphone)as userphone,coalesce(b.userisdelete,a.userisdelete)as userisdelete,coalesce(b.truckisdelete,a.truckisdelete)as truckisdelete,coalesce(b.userName,a.userName)as userName,coalesce(b.createTime,a.createTime)as createTime,coalesce(b.licencenum,a.licencenum)as licencenum,coalesce(b.cardId,a.cardId)as cardId,coalesce(b.companyName,a.companyName)as companyName,coalesce(b.brandName,a.brandName)as brandName from t_attach a full join data_new_attach b on (a.licencenum = b.licencenum and a.userphone = b.userphone)")
      exec.executeInsertOrUpdate("insert ignore t_max_timestamp (data_name, day, max_timestamp) values(?,?,?) on duplicate key update max_timestamp=?", Array(dataType_attach, nowDay, j.ms("maxTimestamp"), j.ms("maxTimestamp")))
      exec.executeInsertOrUpdate(s"update t_check set flag = 1,message='$day:运行成功' where name=?",Array("t_" + dataType_attach))
    }catch {
      case e: Throwable => {
        e.printStackTrace()
        try{
          val exec = new dao.MySqlBaseDao()

          exec.executeInsertOrUpdate(s"update t_check set flag=2, message='$day:t_$dataType_attach 运行失败' where name=?",Array("t_" + dataType_attach))
        }catch {
          case e:Throwable=>{
            e.printStackTrace()
          }
        }
      }
    }
  }

  /**
   * 适用于附近商家和挂车用户信息采集接口
   * 请求接口获取Json数据，解析后写入到hive
   * @param tableName  hive表名称
   * @param dataFields 需要从接口返回的json中解析出来的字段名称，与hive表字段一致
   * @return 接口请求状态消息
   */
  def nearbyShops(spark:SparkSession, day:String,tableName: String, dataFields: String*): Unit = {
    val t_check_pre="aliyun_"
    import spark.implicits._
    try{
      val nearbyUrl = ResourceBundle.getBundle("connection").getString("api.nearby_shop")
      // 请求接口获取Json数据
      val response = Http(nearbyUrl).asString
      val resultJson = JSON.parseObject(response.body)
      // 判断接口返回的状态，若失败直接return失败信息，程序不再往下进行
      if (resultJson.getInteger("status") == 1) {
        return resultJson.getString("msg")
      }

      // 解析Json数据构造DataFrame
      val dataList = new ListBuffer[(Long, Long, String, String, Int)]
      val jsonArray = resultJson.getJSONArray("data").iterator()
      while (jsonArray.hasNext) {
        val t = JSON.parseObject(jsonArray.next().toString)
        dataList.append((
          t.getLong(dataFields(0)),
          t.getLong(dataFields(1)),
          t.getString(dataFields(2)),
          t.getString(dataFields(3)),
          t.getInteger(dataFields(4))))
      }
      val data = dataList.toDF(dataFields: _*)
      // 写入hive
      data.repartition(1).write.mode("append").format("Hive").saveAsTable(tableName)
      val exec = new MySqlBaseDao()
      exec.executeInsertOrUpdate(s"update t_check set flag=1 , message='$day:运行成功' where name='${t_check_pre}t_nearby'",null)
    }catch {
      case e: Throwable => {
        e.printStackTrace()
        try{
          val exec = new dao.MySqlBaseDao()
          exec.executeInsertOrUpdate(s"update t_check set flag=2, message='$day:${t_check_pre}t_nearby 运行失败' where name='${t_check_pre}t_nearby'",null)
        }catch {
          case e:Throwable=>{
            e.printStackTrace()
          }
        }
      }
    }
  }

  /**
   * 拉取长尾词别名数据,数据保存在mysql
   * @param sqlBase mysql链接工具类
   * @return
   */
  def getKeyWordAlias(spark:SparkSession,sqlBase: MySqlBaseDao, day: String): Any = {
    try {
      import spark.implicits._
      val resultTable = "db_userportrait.t_keyword_alias"
      val reader = ResourceBundle.getBundle("connection_underlay")
      val url = reader.getString("pullKeywordAlias")
      val dataList = new ListBuffer[(Int, String, String,Int, String)]
      List("2","3","8","11").foreach(x =>{
        val resultStr=MyHttpService.GetHttpPost2(url,
          s"""
             |{
             |  "typeId": "$x"
             |}""".stripMargin)
        //解析接口数据，写入mysql
        JSON.parseObject(resultStr).getJSONArray("data").foreach(y=>{
          val name=JSON.parseObject(y.toString).getString("F_Keyword")
          JSON.parseObject(y.toString).getJSONArray("F_AnotherNameList").foreach(z=>{
            val anotherNameObject=JSON.parseObject(z.toString)
            val id=anotherNameObject.getInteger("F_Id")
            val alias=anotherNameObject.getString("F_Name")
            val createTime=anotherNameObject.getString("F_CreateTime")
            dataList.append((id, name, alias,x.toInt, createTime))
          })
        })
      })
      sqlBase.execute(s"delete from $resultTable")
      sqlBase.saveMysql(dataList.toDF("id","standard_name","alias","type_id","create_time"),resultTable)
      sqlBase.execute(s"update t_check set flag=1, message='$day:t_keyword_alias 运行成功' where name='aliyun_t_keyword_alias'")
    } catch {
      case e: Exception => {
        e.printStackTrace()
        try {
          val exec = new MySqlBaseDao()
          exec.execute(s"update t_check set flag=2, message='$day:t_keyword_alias 运行失败' where name='aliyun_t_keyword_alias'")
        } catch {
          case e: Throwable => {
            e.printStackTrace()
          }
        }
      }
    }

  }

  def test(): Unit ={
    val spark=MySparkSession.conn()
    val sqlBase=new MySqlBaseDao()
    val hiveDB="default."
    sqlBase.getCacheTable("data_new_secondHandTruck",
      """
        |(select a.uid,status as licenceStatus,if(b.uid is null,0,1) as isopen from db_hitsstatistic_test.t_ershouche as a left join
        |db_hitsstatistic_test.tao_open_meal as b on a.uid=b.uid)as t
        |""".stripMargin,spark)
    spark.sql("set spark.sql.shuffle.partitions=1")
    spark.sql(
      s"""
         |insert overwrite table ${hiveDB}t_secondHandTruck
         | SELECT a.uid, userisdelete, usercreatetime, companyid, companyname, companycreatetime, isvip, onmain, onsold, licence, storestatus,
         |  coalesce(b.licenceStatus,a.licenceStatus)as licenceStatus,
         |  coalesce(b.isOpen,a.isOpen)as isOpen
         | from ${hiveDB}t_secondHandTruck a
         |left join data_new_secondHandTruck b on (a.uid = b.uid)
         |""".stripMargin)

  }
}
