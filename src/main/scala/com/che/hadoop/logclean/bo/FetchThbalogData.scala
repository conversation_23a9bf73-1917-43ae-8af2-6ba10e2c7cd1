package com.che.hadoop.logclean.bo

import java.util.ResourceBundle

import com.che.hadoop.underlay.dao.{MySparkSession, MySqlBaseDao}
import org.apache.spark.sql.SparkSession
import com.che.hadoop.underlay.tool.common.Common.urlDecode
import com.che.hadoop.underlay.tool.common.HDFSFileRenamer
import com.che.hadoop.underlay.tool.date.DateTranslate
/**
 * 从thba业务数据中清洗数据
 */
object FetchThbalogData {

    def main(args: Array[String]): Unit = {
        val spark=MySparkSession.conn()
        val day="20220405"
        cleanThbaLog(spark,day)
    }


    /**
     * 将业务数据清洗到hive表中
     */
    def cleanThbaLog(spark:SparkSession,day:String): Unit ={
        val t_check_pre="aliyun_"
        try{
            val resultTable="t_ods_thba_logdata"
            //重命名tmp文件（flume采集异常情况下会有tmp文件）
            val URI=ResourceBundle.getBundle("connection_underlay").getString("oss-hdfs.uri")
            val path=s"/pv-nginx-app-new/$day"
            val today=DateTranslate.getDate(0)
            //当天文件不自动重命名，因为正在采集过程中会有tmp文件
            if(today == day){
                println("当天采集数据不能重命名tmp文件")
            }else{
                HDFSFileRenamer.renameTmpFile(URI,path)
            }
            spark.udf.register("urlDecode", urlDecode(_: String))
            spark.read.json(s"$URI$path").filter("status=200 and http_host='thba.360che.com'").selectExpr("urlDecode(request_body) as request_body").createOrReplaceTempView("temp_table")
            val resultDF=spark.sql(s"select json_tuple(request_body, 'data', 'billType','timestamp','operationState', 'env'),'$day' as day from temp_table")
              .toDF("data","billtype","timestamp","operation_state","env","day")
            spark.sql(s"alter table $resultTable drop if exists partition(day='$day')")
            resultDF.write.format("hive").mode("append").partitionBy("day").saveAsTable(resultTable)
            val exec = new MySqlBaseDao()
            exec.executeInsertOrUpdate(s"update t_check set flag=1 , message='$day:运行成功' where name='${t_check_pre}t_ods_thba_logdata'",null)
        }catch {
            case e:Exception=>{
                try {
                    val exec = new MySqlBaseDao()
                    exec.executeInsertOrUpdate(s"update t_check set flag=2, message='$day:${t_check_pre}t_ods_thba_logdata程序运行失败'  where name ='${t_check_pre}t_ods_thba_logdata'", null)
                    e.printStackTrace()
                } catch {
                    case e: Throwable => {
                        e.printStackTrace()
                    }
                }
            }
        }
    }

}
