import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;

public class LogUtil {
    public static void main(String[] args) {
        System.out.println("1111111111");
    }

    public static void writeLogToFile(String filename, String msg) {

        // 获取当前时间
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String time = dateFormat.format(new Date());

        File file = new File(filename);
        FileWriter writer = null;
        try {
            writer = new FileWriter(file, true);
            //向文件写入当前时间及日志
            writer.write("[" + time + "]: " + msg + "\n");
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                writer.close();
            } catch (IOException e) {
                if (writer != null) {
                    writer = null;
                }
            }

        }
    }
}
