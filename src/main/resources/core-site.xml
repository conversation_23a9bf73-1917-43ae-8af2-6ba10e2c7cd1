<?xml version="1.0" ?>
<!-- Created at 2022-10-13 01:19:31.915 -->
<configuration>
  <property>
    <name>hadoop.http.authentication.kerberos.keytab</name>
    <value>/etc/emr/hadoop-conf/http.keytab</value>
  </property>
  <property>
    <name>fs.oss.idle.timeout.millisecond</name>
    <value>30000</value>
  </property>
  <property>
    <name>fs.oss.tmp.data.dirs</name>
    <value>/mnt/disk1/jindosdk/tmp,/mnt/disk2/jindosdk/tmp,/mnt/disk3/jindosdk/tmp,/mnt/disk4/jindosdk/tmp</value>
  </property>
  <property>
    <name>hadoop.security.auth_to_local</name>
    <value>
      RULE:[1:$1]
      RULE:[2:$1]
      DEFAULT

    </value>
  </property>
  <property>
    <name>fs.oss.retry.count</name>
    <value>5</value>
  </property>
  <property>
    <name>hadoop.proxyuser.kyuubi.hosts</name>
    <value>*</value>
  </property>
  <property>
    <name>hadoop.proxyuser.presto.hosts</name>
    <value>*</value>
  </property>
  <property>
    <name>ipc.client.idlethreshold</name>
    <value>4000</value>
  </property>
  <property>
    <name>hadoop.proxyuser.hadoop.groups</name>
    <value>*</value>
  </property>
  <property>
    <name>file.blocksize</name>
    <value>67108864</value>
  </property>
  <property>
    <name>fs.oss.upload.queue.size</name>
    <value>16</value>
  </property>
  <property>
    <name>fs.trash.checkpoint.interval</name>
    <value>30</value>
  </property>
  <property>
    <name>fs.oss.timeout.millisecond</name>
    <value>30000</value>
  </property>
  <property>
    <name>hadoop.http.authentication.simple.anonymous.allowed</name>
    <value>false</value>
  </property>
  <property>
    <name>hadoop.security.authorization</name>
    <value>false</value>
  </property>
  <property>
    <name>fs.df.interval</name>
    <value>60000</value>
  </property>
  <property>
    <name>ipc.client.connection.maxidletime</name>
    <value>10000</value>
  </property>
  <property>
    <name>ha.health-monitor.check-interval.ms</name>
    <value>1000</value>
  </property>
  <property>
    <name>fs.jindo.impl</name>
    <value>com.aliyun.jindodata.jindo.JindoFileSystem</value>
  </property>
  <property>
    <name>hadoop.registry.zk.session.timeout.ms</name>
    <value>60000</value>
  </property>
  <property>
    <name>hadoop.proxyuser.spark.hosts</name>
    <value>*</value>
  </property>
  <property>
    <name>ha.zookeeper.parent-znode</name>
    <value>/hadoop-ha</value>
  </property>
  <property>
    <name>ipc.client.connect.timeout</name>
    <value>20000</value>
  </property>
  <property>
    <name>hadoop.proxyuser.spark.groups</name>
    <value>*</value>
  </property>
  <property>
    <name>hadoop.security.authentication</name>
    <value>simple</value>
  </property>
  <property>
    <name>hadoop.util.hash.type</name>
    <value>murmur</value>
  </property>
  <property>
    <name>fs.jindofsx.data.cache.enable</name>
    <value>false</value>
  </property>
  <property>
    <name>hadoop.proxyuser.hdfs.hosts</name>
    <value>*</value>
  </property>
  <property>
    <name>hadoop.proxyuser.kyuubi.groups</name>
    <value>*</value>
  </property>
  <property>
    <name>file.replication</name>
    <value>1</value>
  </property>
  <property>
    <name>io.seqfile.local.dir</name>
    <value>${hadoop.tmp.dir}/io/local</value>
  </property>
  <property>
    <name>fs.jindofsx.storage.connect.enable</name>
    <value>true</value>
  </property>
  <property>
    <name>fs.oss.upload.thread.concurrency</name>
    <value>32</value>
  </property>
  <property>
    <name>fs.du.interval</name>
    <value>600000</value>
  </property>
  <property>
    <name>io.compression.codecs</name>
    <value>org.apache.hadoop.io.compress.DefaultCodec,org.apache.hadoop.io.compress.GzipCodec,org.apache.hadoop.io.compress.BZip2Codec,org.apache.hadoop.io.compress.DeflateCodec,org.apache.hadoop.io.compress.SnappyCodec,org.apache.hadoop.io.compress.Lz4Codec</value>
  </property>
  <property>
    <name>fs.AbstractFileSystem.hdfs.impl</name>
    <value>org.apache.hadoop.fs.Hdfs</value>
  </property>
  <property>
    <name>hadoop.proxyuser.hive.groups</name>
    <value>*</value>
  </property>
  <property>
    <name>ha.zookeeper.quorum</name>
    <value>localhost:2181</value>
  </property>
  <property>
    <name>hadoop.http.filter.initializers</name>
    <value>org.apache.hadoop.http.lib.StaticUserWebFilter</value>
  </property>
  <property>
    <name>io.serializations</name>
    <value>org.apache.hadoop.io.serializer.WritableSerialization,org.apache.hadoop.io.serializer.avro.AvroSpecificSerialization,org.apache.hadoop.io.serializer.avro.AvroReflectSerialization</value>
  </property>
  <property>
    <name>fs.defaultFS</name>
    <value>hdfs://hdfs-cluster</value>
  </property>
  <property>
    <name>hadoop.registry.zk.quorum</name>
    <value>localhost:2181</value>
  </property>
  <property>
    <name>fs.jindofsx.slicelet.cache.enable</name>
    <value>false</value>
  </property>
  <property>
    <name>fs.oss.impl</name>
    <value>com.aliyun.jindodata.oss.JindoOssFileSystem</value>
  </property>
  <property>
    <name>fs.oss.connection.timeout.millisecond</name>
    <value>3000</value>
  </property>
  <property>
    <name>ha.failover-controller.new-active.rpc-timeout.ms</name>
    <value>60000</value>
  </property>
  <property>
    <name>hadoop.proxyuser.hdfs.groups</name>
    <value>*</value>
  </property>
  <property>
    <name>hadoop.proxyuser.knox.hosts</name>
    <value>*</value>
  </property>
  <property>
    <name>io.bytes.per.checksum</name>
    <value>512</value>
  </property>
  <property>
    <name>dfs.namenode.delegation.token.max-lifetime</name>
    <value>25920000000</value>
  </property>
  <property>
    <name>fs.trash.interval</name>
    <value>1440</value>
  </property>
  <property>
    <name>ipc.client.kill.max</name>
    <value>10</value>
  </property>
  <property>
    <name>hadoop.http.authentication.type</name>
    <value>simple</value>
  </property>
  <property>
    <name>hadoop.caller.context.enabled</name>
    <value>false</value>
  </property>
  <property>
    <name>hadoop.proxyuser.knox.groups</name>
    <value>*</value>
  </property>
  <property>
    <name>hadoop.proxyuser.livy.groups</name>
    <value>*</value>
  </property>
  <property>
    <name>ipc.client.connect.max.retries.on.timeouts</name>
    <value>45</value>
  </property>
  <property>
    <name>hadoop.proxyuser.hbase.groups</name>
    <value>*</value>
  </property>
  <property>
    <name>hadoop.proxyuser.hbase.hosts</name>
    <value>*</value>
  </property>
  <property>
    <name>fs.oss.upload.max.pending.tasks.per.stream</name>
    <value>10</value>
  </property>
  <property>
    <name>hadoop.proxyuser.hive.hosts</name>
    <value>*</value>
  </property>
  <property>
    <name>io.seqfile.compress.blocksize</name>
    <value>1000000</value>
  </property>
  <property>
    <name>fs.oss.credentials.provider</name>
    <value>com.aliyun.jindodata.oss.auth.SimpleCredentialsProvider,com.aliyun.jindodata.oss.auth.EnvironmentVariableCredentialsProvider,com.aliyun.jindodata.oss.auth.CommonCredentialsProvider,com.aliyun.jindodata.oss.auth.EcsStsCredentialsProvider</value>
  </property>
  <property>
    <name>fs.jindofsx.client.metrics.enable</name>
    <value>false</value>
  </property>
  <property>
    <name>ipc.client.connect.max.retries</name>
    <value>10</value>
  </property>
  <property>
    <name>fs.AbstractFileSystem.jindo.impl</name>
    <value>com.aliyun.jindodata.jindo.JINDO</value>
  </property>
  <property>
    <name>io.file.buffer.size</name>
    <value>4096</value>
  </property>
  <property>
    <name>fs.AbstractFileSystem.oss.impl</name>
    <value>com.aliyun.jindodata.oss.OSS</value>
  </property>
  <property>
    <name>fs.jindofsx.namespace.rpc.address</name>
    <value>master-1-1:8101</value>
  </property>
  <property>
    <name>fs.jindofsx.tmp.data.dirs</name>
    <value>/mnt/disk1/jindosdk/tmp,/mnt/disk2/jindosdk/tmp,/mnt/disk3/jindosdk/tmp,/mnt/disk4/jindosdk/tmp</value>
  </property>
  <property>
    <name>fs.hdfs.impl</name>
    <value>org.apache.hadoop.hdfs.DistributedFileSystem</value>
  </property>
  <property>
    <name>io.mapfile.bloom.size</name>
    <value>1048576</value>
  </property>
  <property>
    <name>ha.zookeeper.session-timeout.ms</name>
    <value>60000</value>
  </property>
  <property>
    <name>hadoop.tmp.dir</name>
    <value>/mnt/disk1/hadoop/tmp</value>
  </property>
  <property>
    <name>ipc.client.connect.retry.interval</name>
    <value>1000</value>
  </property>
  <property>
    <name>fs.permissions.umask-mode</name>
    <value>026</value>
  </property>
  <property>
    <name>hadoop.proxyuser.hadoop.hosts</name>
    <value>*</value>
  </property>
  <property>
    <name>hadoop.registry.zk.connection.timeout.ms</name>
    <value>15000</value>
  </property>
  <property>
    <name>fs.oss.endpoint</name>
    <value>oss-cn-beijing-internal.aliyuncs.com</value>
  </property>
  <property>
    <name>fs.oss.download.thread.concurrency</name>
    <value>32</value>
  </property>
</configuration>