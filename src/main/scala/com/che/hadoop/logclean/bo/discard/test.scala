package com.che.hadoop.logclean.bo.discard

import java.net.URLDecoder
import java.text.SimpleDateFormat
import java.util.Calendar

import com.che.hadoop.underlay.dao
import com.che.hadoop.underlay.dao.{MySparkSession, MyKudu, MySqlBaseDao}
import com.che.hadoop.logclean.utils.Common
import org.apache.spark.sql.Row
import org.apache.spark.sql.types.{StringType, StructField, StructType}

object test {

  def main(args: Array[String]): Unit = {
    val spark=MySparkSession.conn()
    val myKudu=new MyKudu(spark)
    val sqlBse=new MySqlBaseDao("bigdata_test2")
    val df=myKudu.select("t_logdata_appsc_streaming2").where("day='20220522' and `time`>'1653226725688'")
    sqlBse.saveMysql(df,"t_logdata_appsc_streaming2")
  }


  def oldData(year:String): Unit ={
    val spark = MySparkSession.conn()
    spark.sql("select avg(num) from (select  day,count(distinct vid) as num" +
      s" from t_logdata where day between '${year}0601' and '${year}1231' and eventid=''   " +
      s"and (floor((code / power(10.0, 9))) in ('11105','12106','15109','13110','14110') " +
      s" or k like 'file:///data/user/0/com.truckhome.bbs/files/NewsUnZip/dist/index.html%') group by day)t").show()
    //    val sql="(select  a.f_id as id,a.F_parentid as parentid,a.f_name as name," +
    //      "a.f_code as code,floor((F_code / power(10.0, 9))) as channlcode from " +
    //      "t_type as a  where a.f_isdelete=0 and f_parentid in (1,2,3,4,5,6)) as t"
    //    sqlBase.getCacheTable("t_type",sql)

  }

  /*
清洗数据保存到hive
*/
  def savehive(dayValue:String):Unit={

    val dirPath = "hdfs://mycluster/pv-nginx/"+dayValue

    dao.MySparkSession.conn().sqlContext.setConf("hive.exec.dynamic.partition.mode","nonstrict")
    dao.MySparkSession.conn().sqlContext.setConf("hive.exec.orc.default.block.size","134217728")
    dao.MySparkSession.conn().conf.set("spark.sql.broadcastTimeout","1200")
    val LogRdd = dao.MySparkSession.conn().read.json(dirPath).filter(t=>t.getAs[String]("status")=="200"
      && t.getAs[String]("args").split("&").length>=8  && t.getAs[String]("args").split("&").length<=14  )// && t.getAs[String]("args").split("&")(1).contains("105368")


    //转换日志格式
    val rowRDD = LogRdd.rdd.map(line => {
      val args = line.getAs[String]("args").split("&")
      val res = getPValueByName(line.getAs[String]("args"), "res")
      val k = urlDecode(URLDecoder.decode(getPValueByName(line.getAs[String]("args"), "k"),"UTF-8"))
      val vtc = getPValueByName(line.getAs[String]("args"), "vtc")
      val i= urlDecode(URLDecoder.decode(getPValueByName(line.getAs[String]("args"), "i"),"UTF-8"))
      val vtf = getPValueByName(line.getAs[String]("args"), "vtf")
      val vtl = getPValueByName(line.getAs[String]("args"), "vtl")
      val uid = getPValueByName(line.getAs[String]("args"), "uid")
      val vid = getPValueByName(line.getAs[String]("args"), "vid")
      val sid= getPValueByName(line.getAs[String]("args"), "sid")
      val ip = line.getAs[String]("clientip")
      val ua = line.getAs[String]("user_agent")
      val daytime = tranTimeToString(line.getAs[String]("@timestamp")).toString()
      val eventid = if(args.length>=13)  getPValueByName(line.getAs[String]("args"), "eventid") else ""
      val action = if(args.length>=13) URLDecoder.decode(getPValueByName(line.getAs[String]("args"), "action"),"UTF-8") else ""
      val lable =  if(args.length>=13) URLDecoder.decode(getPValueByName(line.getAs[String]("args"), "lable"),"UTF-8") else ""
      val ga_vid=  if(args.length>=14)  getPValueByName(line.getAs[String]("args"), "ga_vid") else ""
      val day=dayValue
      Row(res, k, vtc, i, vtf, vtl, uid, vid,sid,ip,ua,daytime, eventid,action,lable,ga_vid, day)
    })

    val structType = StructType(Array(
      StructField("res", StringType, true),
      StructField("k", StringType, true),
      StructField("vtc", StringType, true),
      StructField("i", StringType, true),
      StructField("vtf", StringType, true),
      StructField("vtl", StringType, true),
      StructField("uid", StringType, true),
      StructField("vid", StringType, true),
      StructField("sid", StringType, true),
      StructField("ip", StringType, true),
      StructField("ua", StringType, true),
      StructField("daytime", StringType, true),
      StructField("eventid", StringType, true),
      StructField("action", StringType, true),
      StructField("lable", StringType, true),
      StructField("ga_vid", StringType, true),
      StructField("day", StringType, true)
    ))

    //转成DataFrame
    //var saveDF=
    dao.MySparkSession.conn().sqlContext.createDataFrame(rowRDD,structType).createOrReplaceTempView("logclean")

    val saveDF= dao.MySparkSession.conn().sql("select *  from logclean where ip not in (select ip from (select ip,count(k) as t,k,i from logclean where i='' group by ip,k,i) s where t>=2000)")//过滤来源为空，且访问数量大于2000的数据

//    val savemysqlDF=dao.MySparkSession.conn.sql("select k as f_url,ip as F_IP,t as f_count,f_updatetime from (select ip,count(k) as t,k,i ,from_unixtime(daytime/1000,'yyyy-MM-dd') as f_updatetime from logclean " +
//      "where i='' group by ip,k,i,from_unixtime(daytime/1000,'yyyy-MM-dd')) s where t>=2000") //来源为空，且访问数量大于2000的数据，保存到mysql
    //val saveDF=spark.sql("SELECT * FROM logclean WHERE ip IN ( SELECT DISTINCT ip FROM ( SELECT ip, COUNT(k) AS t, k, i FROM logclean WHERE i = '' GROUP BY ip, k, i HAVING COUNT(k) < 2000 ) s )")
    // val savemysqlDF=spark.sql("SELECT ip as F_IP, COUNT(k) AS f_count, k as f_url, '"+com.che.hadoop.logclean.utils.function_.getShortDateTime(1)+"' as from_unixtime FROM logclean WHERE i = '' GROUP BY ip, k, i HAVING COUNT(k) >= 2000 ") //来源为空，且访问数量大于2000的数据，保存到mysql
//    com.che.hadoop.logclean.utils.function_.saveMysql(savemysqlDF,"t_filter_Ip")
    saveDF.repartition(1).write.mode("append").format("Hive").partitionBy("day").saveAsTable("t_logcleandatatest")

    dao.MySparkSession.conn().close()
  }
  /*
  得到当前日期前一天
  */
  def getDate(day: Int): String = {
    val dateFormat: SimpleDateFormat = new SimpleDateFormat("yyyyMMdd")
    var cal: Calendar = Calendar.getInstance()
    cal.add(Calendar.DATE, -day)
    val date = dateFormat.format(cal.getTime())
    return date

  }

//  def urldecode(value:String):String={
//    if(isUtf8Url(value))
//    return URLDecoder.decode(value,"UTF-8")
//    else
//      return value
//  }


  /*
   时间戳转换为日期类型
 */
  def tranTimeToString(tm: String): Long = {
    val dateFormat: SimpleDateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssX")
    val dt = dateFormat.parse(tm)
    //val aa = dateFormat.format(dt)
    val tim: Long = dt.getTime()
    // println(tim)
    tim
  }

  //  //返回参数值
  //  def getPValue(args: Array[String],i: Int): String = {
  //    var str:String=""
  //    if (args(i).split("=").length > 1) {
  //      str= args(i).split("=")(1)
  //    }
  //    return str
  //  }

  //
  //返回参数值
  def getPValueByName(args: String,value: String): String = {
    var str:String=""
    val a1 =args
    val p1 = ("""(\\?|\\&)"""+value+"""=([^\\&]+)""").r
    val arg=(p1 findAllIn a1 toList)
    if(arg.length>0&&arg(0).split("=").length>1)
    {
      str= arg(0).split("=")(1)
    }
    return str
  }

//  def isUtf8Url(value:String):Boolean= {
//    var text = value.toLowerCase();
//    var p = text.indexOf("%");
//    if (p != -1 && text.length() - p > 9) {
//      text = text.substring(p, p + 9)
//    }
//    return Utf8codeCheck(text)
//  }
//
//  def Utf8codeCheck(text:String):Boolean={
//    var sign = "";
//    if (text.startsWith("%e"))  {
//      var p=0
//      for ( i <- 0 to 100) {
//        p = text.indexOf("%", p);
//        if (p != -1)
//          p = p + 1;
//        else {
//          break()
//        }
//        sign += p;
//
//      }
//    }
//
//    return sign.equals("147-1");
//  }

//  def urlDecode(code:String ) :String={
//    if (code.matches("^(?:[\\x00-\\x7f]|[\\xe0-\\xef][\\x80-\\xbf]{2})+$"))
//      return URLDecoder.decode(code, "utf-8");
//    else
//      return code;
//  }

  def urlDecode(code:String ) :Boolean={

     var flag = java.nio.charset.Charset.forName("UTF-8").newEncoder().canEncode(code);
    return flag;
  }


//  def urlDecode(code:String ) {
//
//
//    var needEncode = false;
//    for (int i = 0; i < str.length(); i++) {
//      char c = str.charAt(i);
//      if (dontNeedEncoding.get((int) c)) {
//        continue;
//      }
//      if (c == '%' && (i + 2) < str.length()) {
//        // 判断是否符合urlEncode规范
//        char c1 = str.charAt(++i);
//        char c2 = str.charAt(++i);
//        if (isDigit16Char(c1) && isDigit16Char(c2)) {
//          continue;
//        }
//      }
//      // 其他字符，肯定需要urlEncode
//      needEncode = true;
//      break;
//    }
//
//    return !needEncode;
//  }

}
