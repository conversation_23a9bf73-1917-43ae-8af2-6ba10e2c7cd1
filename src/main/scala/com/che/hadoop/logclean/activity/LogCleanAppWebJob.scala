package com.che.hadoop.logclean.activity

import com.che.hadoop.logclean.bo.{LogCleanApp, LogCleanMiniProgram, LogCleanSCWeb, LogCleanWeb}
import com.che.hadoop.underlay.dao.MySparkSession
import com.che.hadoop.underlay.tool.date.DateTranslate

/**
  * 神策平台web数据清洗
  * hadoop app日志离线清洗
  * 神策app日志离线清洗
  */
object LogCleanAppWebJob {
  def main(args: Array[String]): Unit = {
    var startDay,endDay=DateTranslate.getDate(1)
    var dbEnv="default."
    if(args.length>=1){
      dbEnv=args(0)
      if(args.length==2){
        startDay=args(1)
        endDay=startDay
      }
      if(args.length==3){
        startDay=args(1)
        endDay=args(2)
      }
    }
    val spark=MySparkSession.conn()
    /*清洗数据到hive*/
    DateTranslate.getDatesBetween(startDay,endDay).foreach(day=>{
      println(s"计算日期：$day")
      if (dbEnv=="default."){
        //神策平台web数据清洗
        LogCleanSCWeb.webscLogClean(spark, day, dbEnv)
        //神策app日志离线清洗
        LogCleanApp.cleanAppScData(spark, day, dbEnv)
        //维护t_ods_appsc_log表中未关联dm_id的数据，cid作为dmid单独保存
        LogCleanApp.dmIdSupplement(day, dbEnv)
        //神策小程序数据清洗
        LogCleanMiniProgram.cleanMiniProgramLog(spark, day, dbEnv)
        //hadoop web日志离线清洗
        LogCleanWeb.saveLogCleanToHive(spark, day, dbEnv)
        //hadoop app日志离线清洗
        LogCleanApp.cleanAppData(spark, day, dbEnv)
      }else{
        //神策平台web数据清洗
        LogCleanSCWeb.webscLogClean(spark, day, dbEnv)
        //神策app日志离线清洗
        LogCleanApp.cleanAppScData(spark, day, dbEnv)
        //神策小程序数据清洗
        LogCleanMiniProgram.cleanMiniProgramLog(spark, day, dbEnv)
      }
    })
    spark.stop()
  }

}
