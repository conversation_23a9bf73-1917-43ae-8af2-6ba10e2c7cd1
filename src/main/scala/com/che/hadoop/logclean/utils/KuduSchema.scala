package com.che.hadoop.logclean.utils

import org.apache.kudu.{ColumnSchema, Schema, Type}

/**
 * 保存kudu表中的shema信息
 */
object KuduSchema {



  private val t_logdata_appsc_streaming2_schemaList = List[ColumnSchema](
    new ColumnSchema.ColumnSchemaBuilder("offset", Type.STRING).key(true).build(),
    new ColumnSchema.ColumnSchemaBuilder("day",Type.STRING).key(true).nullable(false).build(),
    new ColumnSchema.ColumnSchemaBuilder("month",Type.STRING).build(),
    new ColumnSchema.ColumnSchemaBuilder("id",Type.STRING).nullable(false).build(),
    new ColumnSchema.ColumnSchemaBuilder("cid",Type.STRING).nullable(false).build(),
    new ColumnSchema.ColumnSchemaBuilder("uid",Type.STRING).nullable(false).build(),
    new ColumnSchema.ColumnSchemaBuilder("event",Type.STRING).nullable(false).build(),
    new ColumnSchema.ColumnSchemaBuilder("screen_name",Type.STRING).nullable(false).build(),
    new ColumnSchema.ColumnSchemaBuilder("title",Type.STRING).nullable(false).build(),
    new ColumnSchema.ColumnSchemaBuilder("referrer",Type.STRING).nullable(false).build(),
    new ColumnSchema.ColumnSchemaBuilder("url",Type.STRING).nullable(false).build(),
    new ColumnSchema.ColumnSchemaBuilder("element_id",Type.STRING).nullable(false).build(),
    new ColumnSchema.ColumnSchemaBuilder("element_content",Type.STRING).nullable(false).build(),
    new ColumnSchema.ColumnSchemaBuilder("type_id",Type.STRING).nullable(false).build(),
    new ColumnSchema.ColumnSchemaBuilder("label",Type.STRING).nullable(false).build(),
    new ColumnSchema.ColumnSchemaBuilder("action",Type.STRING).nullable(false).build(),
    new ColumnSchema.ColumnSchemaBuilder("element_type",Type.STRING).nullable(false).build(),
    new ColumnSchema.ColumnSchemaBuilder("element_position",Type.STRING).nullable(false).build(),
    new ColumnSchema.ColumnSchemaBuilder("device_id",Type.STRING).nullable(false).build(),
    new ColumnSchema.ColumnSchemaBuilder("dm_id",Type.STRING).nullable(true).build(),
    new ColumnSchema.ColumnSchemaBuilder("manufacturer",Type.STRING).nullable(false).build(),
    new ColumnSchema.ColumnSchemaBuilder("os",Type.STRING).nullable(false).build(),
    new ColumnSchema.ColumnSchemaBuilder("model",Type.STRING).nullable(false).build(),
    new ColumnSchema.ColumnSchemaBuilder("os_version",Type.STRING).nullable(false).build(),
    new ColumnSchema.ColumnSchemaBuilder("app_id",Type.STRING).nullable(false).build(),
    new ColumnSchema.ColumnSchemaBuilder("screen_width",Type.STRING).nullable(false).build(),
    new ColumnSchema.ColumnSchemaBuilder("screen_height",Type.STRING).nullable(false).build(),
    new ColumnSchema.ColumnSchemaBuilder("ads",Type.STRING).nullable(false).build(),
    new ColumnSchema.ColumnSchemaBuilder("app_version",Type.STRING).nullable(false).build(),
    new ColumnSchema.ColumnSchemaBuilder("latitude",Type.STRING).nullable(false).build(),
    new ColumnSchema.ColumnSchemaBuilder("longitude",Type.STRING).nullable(false).build(),
    new ColumnSchema.ColumnSchemaBuilder("wifi",Type.STRING).nullable(false).build(),
    new ColumnSchema.ColumnSchemaBuilder("network_type",Type.STRING).nullable(false).build(),
    new ColumnSchema.ColumnSchemaBuilder("time",Type.STRING).nullable(false).build(),
    new ColumnSchema.ColumnSchemaBuilder("resume_from_background",Type.STRING).nullable(false).build(),
    new ColumnSchema.ColumnSchemaBuilder("event_duration",Type.STRING).nullable(false).build(),
    new ColumnSchema.ColumnSchemaBuilder("cl",Type.STRING).nullable(false).build(),
    new ColumnSchema.ColumnSchemaBuilder("eventid",Type.STRING).nullable(false).build(),
    new ColumnSchema.ColumnSchemaBuilder("user_id",Type.STRING).nullable(false).build()
  )

  private val t_ods_info_appscdata_streaming_schemaList = List[ColumnSchema](
    new ColumnSchema.ColumnSchemaBuilder("id", Type.STRING).key(true).build(),
    new ColumnSchema.ColumnSchemaBuilder("day",Type.STRING).key(true).build(),
    new ColumnSchema.ColumnSchemaBuilder("cid",Type.STRING).nullable(false).build(),
    new ColumnSchema.ColumnSchemaBuilder("uid",Type.STRING).nullable(false).build(),
    new ColumnSchema.ColumnSchemaBuilder("event",Type.STRING).nullable(false).build(),
    new ColumnSchema.ColumnSchemaBuilder("screen_name",Type.STRING).nullable(false).build(),
    new ColumnSchema.ColumnSchemaBuilder("title",Type.STRING).nullable(false).build(),
    new ColumnSchema.ColumnSchemaBuilder("type_id",Type.STRING).nullable(false).build(),
    new ColumnSchema.ColumnSchemaBuilder("label",Type.STRING).nullable(false).build(),
    new ColumnSchema.ColumnSchemaBuilder("action",Type.STRING).nullable(false).build(),
    new ColumnSchema.ColumnSchemaBuilder("os",Type.STRING).nullable(false).build(),
    new ColumnSchema.ColumnSchemaBuilder("time",Type.STRING).nullable(false).build(),
    new ColumnSchema.ColumnSchemaBuilder("event_duration",Type.STRING).nullable(false).build()
  )

  private val tableSchemaMap = Map(
        "t_logdata_appsc_streaming2" -> t_logdata_appsc_streaming2_schemaList,
        "t_ods_info_appscdata_streaming" -> t_ods_info_appscdata_streaming_schemaList
  )

  /**
   * 根据表名获取schema
   * @param tableName kudu表名
   * @return 对应的schema
   */
  def getKuduSchema(tableName:String): Schema ={
    import scala.collection.JavaConverters._
    val schemaList=tableSchemaMap.get(tableName)
    if(schemaList==None){
      throw new Exception(s"${tableName}表的schema信息未获取成功")
    }
    new Schema(schemaList.get.asJava)
  }

}
