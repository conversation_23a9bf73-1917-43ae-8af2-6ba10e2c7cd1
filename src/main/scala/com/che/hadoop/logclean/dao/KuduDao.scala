package com.che.hadoop.logclean.dao

import java.util.ResourceBundle

import org.apache.kudu.client._
import org.apache.kudu.{ColumnSchema, Schema, Type}

object KuduDao  {
  val kuduMasters = ResourceBundle.getBundle("connection").getString("kudu.master")
  val kuduClient: KuduClient = new KuduClient.KuduClientBuilder(kuduMasters).build()
  var kuduSession: KuduSession = kuduClient.newSession()

  /**
    *
    * @param tableName 表名
    * @param f 需要执行的方法
    */
  def exe(tableName: String, f:KuduTable => Operation): Unit = {
    try {
      val kuduTable = kuduClient.openTable(tableName)
      val exe = f(kuduTable)
      if(kuduSession == null || kuduSession.isClosed){
        kuduSession = kuduClient.newSession()
      }
      kuduSession.apply(exe)
    } catch {
      case e: Exception => e.printStackTrace()
    }
  }
  def query(tableName:String):Unit={
    val kuduTable = kuduClient.openTable(tableName)
    val kuduScanner: KuduScanner = kuduClient.newScannerBuilder(kuduTable).build()

    //4.双层遍历
    while (kuduScanner.hasMoreRows){
      val rowResultIterator: RowResultIterator = kuduScanner.nextRows()
      while (rowResultIterator.hasNext){
        val rowResult = rowResultIterator.next()
        println(rowResult.rowToString())
      }

    }
  }
  def createTable(tableName:String, hashPartitions:String, schemaList:List[ColumnSchema]): Unit ={
    import scala.collection.JavaConverters._
    val options: CreateTableOptions = new CreateTableOptions()
    val schema:Schema = new Schema(schemaList.asJava)
    options.addHashPartitions(List(hashPartitions).asJava,3)

    //执行表的创建
    kuduClient.createTable(tableName,schema,options)
  }
  def deleteTable(tableName:String): Unit ={
    kuduClient.deleteTable(tableName)
  }
  def main(args: Array[String]): Unit = {
  }

}
