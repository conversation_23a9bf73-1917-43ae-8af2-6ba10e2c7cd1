package com.che.hadoop.logclean.activity

import com.che.hadoop.logclean.bo.LogCleanInformationStreaming
import com.che.hadoop.underlay.dao.{MySparkSession, MySqlBaseDao}

/**
 * 实时清洗内容浏览相关数据
 */
object LogCleanInformationStramingJob {
  def main(args: Array[String]): Unit = {
    val spark=MySparkSession.conn()
    val sqlBase=new MySqlBaseDao()
    LogCleanInformationStreaming.dwdInformationLog(spark,sqlBase)
  }
}
