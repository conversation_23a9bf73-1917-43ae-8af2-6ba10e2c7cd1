package com.che.hadoop.logclean.bo.discard

import java.util.ResourceBundle
import scala.collection.JavaConverters._
import com.che.hadoop.underlay.dao.{MySparkSession, MyKudu, MySqlBaseDao}
import com.che.hadoop.logclean.utils.{Common, KuduSchema}
import org.apache.kudu.client.{CreateTableOptions, KuduClient, PartialRow}
import org.apache.kudu.spark.kudu.KuduContext
import org.apache.spark.sql.types._

object kuduTest {
  private val reader=ResourceBundle.getBundle("connection")

  //资讯正则
  val articleurl="(http|https)://((www.360che.com/(news|market|driver|tech|law)/[0-9]+/[0-9]+)|(m.360che.com/(news|market|driver|tech|law)/[0-9]+/[0-9]+).html|(m.360che.com/weixin/(article|SpeedArticle).aspx+)|(app.360che.com/app/ArticleNew/Article.aspx+)|(news-app.360che.com/article.html+))"
  val videourl="(http|https)://(((www.360che.com/v/v_[0-9]+)|(m.360che.com/v/v_[0-9]+)).html|(m.360che.com/appvideo/(VideoInfo|VideoShare|v/info|quickshare).aspx+)|(news-app.360che.com/video.html+))"
  val picurl="(http|https)://(((tu.360che.com/news/[0-9]+)|(tu.360che.com/m/[0-9]+)|(app.360che.com/share_picture/[0-9]+)).html|(api.360che.com/ImgChannel/app/ImageInfo.aspx+))"

  def main(args: Array[String]): Unit = {
    val spark=MySparkSession.conn()
    val myKudu=new MyKudu(spark)
    myKudu.select("t_logdata_appsc_streaming2").createOrReplaceTempView("t_app_log_tmp")
    val appinfolog = spark.sql(
      s"""
         |select  DISTINCT id,cid , uid,event,nvl(screen_name,'') as screen_name,title,type_id,label,action,os,time,event_duration,day  from t_app_log_tmp
         |where day ='20220329' and
         |type_id in ("2","5") and event in ('CHES_00000001','AppClick','CHES_00000002') and  label!=''  and label rlike '^[0-9]*$$'
         |""".stripMargin)
    //          lines.collect().foreach(println(_))
    myKudu.kuduContext.asyncClient.newSession().setMutationBufferSpace(10000)
    myKudu.upsert(appinfolog, "t_ods_info_appscdata_streaming")
  }
  def createTablePartitionDay(tableName:String): Unit ={
    val schema=KuduSchema.getKuduSchema(tableName)
    val kuduMasters = reader.getString("kudu.master")
    //获取kudu客户端对象
    val kuduClient: KuduClient = new KuduClient.KuduClientBuilder(kuduMasters).build()
    if (kuduClient.tableExists(tableName)){
      kuduClient.deleteTable(tableName)
    }
    val options=new CreateTableOptions()
    options.setRangePartitionColumns(List("day").asJava)
    for (i <-0 to 50) {
      val lower: PartialRow = schema.newPartialRow()
      lower.addString("day", Common.getDate(-i))
      val upper: PartialRow = schema.newPartialRow()
      upper.addString("day", Common.getDate(-i-1))
      options.addRangePartition(lower, upper)
    }
    kuduClient.createTable(tableName, schema, options)
  }


  def schemas(): Unit ={
    //t_logdata_appsc_streaming,神策实时清洗
    val schema = StructType(Array(
      StructField("id", StringType, nullable = false),
      StructField("day", StringType, nullable = false),
      StructField("cid", StringType, nullable = false),
      StructField("uid", StringType, nullable = false),
      StructField("event", StringType, nullable = false),
      StructField("screen_name", StringType),
      StructField("title", StringType),
      StructField("referrer", StringType),
      StructField("url", StringType),
      StructField("element_id", StringType, nullable = false),
      StructField("element_content", StringType, nullable = false),
      StructField("type_id", StringType, nullable = false),
      StructField("label", StringType, nullable = false),
      StructField("action", StringType, nullable = false),
      StructField("element_type", StringType, nullable = false),
      StructField("element_position", StringType, nullable = false),
      StructField("device_id", StringType),
      StructField("dm_id", StringType),
      StructField("manufacturer", StringType, nullable = false),
      StructField("os", StringType, nullable = false),
      StructField("model", StringType),
      StructField("os_version", StringType),
      StructField("app_id", StringType),
      StructField("screen_width", StringType),
      StructField("screen_height", StringType),
      StructField("app_version", StringType),
      StructField("ads", StringType),
      StructField("latitude", StringType),
      StructField("longitude", StringType),
      StructField("wifi", StringType),
      StructField("network_type", StringType),
      StructField("time", StringType),
      StructField("resume_from_background", StringType),
      StructField("event_duration", StringType),
      StructField("cl", StringType),
      StructField("eventid", StringType)
          ))

    //t_dwd_dmid
        val schema2 = StructType(Array(
          StructField("id", StringType, nullable = false),
          StructField("uid", StringType, nullable = false),
          StructField("vid", StringType, nullable = false),
          StructField("dm_id", StringType, nullable = false),
          StructField("vid_type", IntegerType, nullable = false),
          StructField("cs", StringType, nullable = false),
          StructField("is_del", IntegerType, nullable = false),
          StructField("timestamp", LongType, nullable = false),
          StructField("first_time", IntegerType, nullable = false),
          StructField("update_time", IntegerType, nullable = false)
        ))
  }

  def createTable(tableName:String,schema:StructType): Unit ={
    val spark = MySparkSession.conn()
    spark.sparkContext.setLogLevel("ERROR")
    val sqlBase = new MySqlBaseDao()
    //t_logdata_appsc_streaming,神策实时清洗
    val kuduMasters = reader.getString("kudu.master")
    val kuduContext = new KuduContext(kuduMasters, spark.sparkContext)
    if(kuduContext.tableExists(tableName)) kuduContext.deleteTable(tableName)
    val options=new CreateTableOptions()
    options.addHashPartitions(Seq("id").asJava, 3)
    kuduContext.createTable(tableName, schema,
      Seq("id"),
      options.setNumReplicas(3))
  }

  /**
   * 数据从kudu查询写入另一个kudu表中
   */
  def copyData(): Unit ={
    val spark=MySparkSession.conn()
    val myKudu=new MyKudu(spark)
    val resultDF=myKudu.select("t_logdata_appsc_streaming").where("day=20210702")
    myKudu.saveAsKudu(resultDF,"t_logdata_appsc_streaming_1")
  }
  /**
   * 数据从hive查询写入另一个kudu表中
   */
  def copyData2(): Unit ={
    val spark = MySparkSession.conn()
    val myKudu=new MyKudu(spark)
    spark.udf.register("uuid", Common.getUUID(_: String))
    spark.conf.set("spark.rpc.askTimeout", "600s")
    val resultDF=spark.sql(
      """
        |select uuid('') as id,cid, uid, event, screen_name, title, referrer, url,
        |element_id, element_content, type_id, label, `action`, element_type,
        |element_position, device_id, dm_id, manufacturer, os, model, os_version,
        |app_id, screen_width, screen_height, app_version, ads, latitude, longitude,
        |wifi, network_type, `time`, resume_from_background, event_duration, cl, `day`
        |from t_ods_appsc_log where day='20210630'
        |""".stripMargin)
    myKudu.saveAsKudu(resultDF,"t_logdata_appsc_streaming_1")
  }

  /**
   * 删除某一天的数据
   */
  def delete(tableName:String): Unit ={
    val spark=MySparkSession.conn()
    val myKudu=new MyKudu(spark)
    val resultDF=myKudu.select(tableName).select("id").where("day<=20210714")
    myKudu.delete(resultDF,tableName)
  }

  /**
   * 测试函数
   */
  def test(): Unit ={
    val spark = MySparkSession.conn()
    spark.sparkContext.setLogLevel("ERROR")
    val sqlBase = new MySqlBaseDao()
    //t_logdata_app_streaming
    val t_logdata_app_streaming_schema = StructType(Array(
      StructField("id", StringType, nullable = false),
      StructField("av", StringType, nullable = false),
      StructField("cid", StringType, nullable = false),
      StructField("t", StringType, nullable = false),
      StructField("time", StringType, nullable = false),
      StructField("m", StringType, nullable = false),
      StructField("sr", StringType),
      StructField("p", StringType),
      StructField("cs", StringType, nullable = false),
      StructField("eventid", StringType),
      StructField("action", StringType),
      StructField("lable", StringType),
      StructField("uid", StringType),
      StructField("appid", StringType),
      StructField("visit_day", StringType, nullable = false),
      StructField("cl", StringType),
      StructField("page", StringType),
      StructField("deviceid", StringType),
      StructField("dm_id", StringType),
      StructField("lib", StringType),
      StructField("day", StringType, nullable = false)
    ))
    val myKudu=new MyKudu(spark)
    myKudu.createTable("t_logdata_app_streaming_test", t_logdata_app_streaming_schema, Seq("id"))

    //    val schema = StructType(Array(StructField("id", StringType, nullable = false), StructField("res", StringType, nullable = false),
    //      StructField("k", StringType, nullable = false), StructField("vtc", StringType, nullable = false),
    //      StructField("i", StringType, nullable = false),
    //      StructField("vtf", StringType, nullable = false), StructField("vtl", StringType, nullable = false),
    //      StructField("uid", StringType),
    //      StructField("vid", StringType, nullable = false), StructField("sid", StringType, nullable = false),
    //      StructField("ip", StringType),
    //      StructField("ua", StringType),
    //      StructField("daytime", StringType),
    //      StructField("eventid", StringType),
    //      StructField("action", StringType),
    //      StructField("lable", StringType),
    //      StructField("code", StringType),
    //      StructField("ga_vid", StringType),
    //      StructField("dealers_uid", StringType),
    //      StructField("hours", IntegerType),
    //      StructField("dm_id", StringType),
    //      StructField("day", StringType, nullable = false)
    //    ))
    /*小程序schama*/
    //        val schema = StructType(Array(StructField("id", StringType, nullable = false),
    //          StructField("openid", StringType),
    //          StructField("anonymous_id", StringType, nullable = false),
    //          StructField("distinct_id", StringType, nullable = false),
    //          StructField("uid", StringType),
    //          StructField("event", StringType, nullable = false),
    //          StructField("screen", StringType),
    //          StructField("latest_scene", StringType),
    //          StructField("url_path", StringType),
    //          StructField("title", StringType),
    //          StructField("url_query", StringType),
    //          StructField("referrer", StringType),
    //          StructField("element_id", StringType, nullable = false),
    //          StructField("element_content", StringType, nullable = false),
    //          StructField("element_name", StringType, nullable = false),
    //          StructField("type_id", StringType, nullable = false),
    //          StructField("label", StringType, nullable = false),
    //          StructField("action", StringType, nullable = false),
    //          StructField("element_type", StringType, nullable = false),
    //          StructField("manufacturer", StringType, nullable = false),
    //          StructField("os", StringType, nullable = false),
    //          StructField("model", StringType),
    //          StructField("os_version", StringType),
    //          StructField("app_id", StringType),
    //          StructField("screen_width", StringType),
    //          StructField("screen_height", StringType),
    //          StructField("ads", StringType),
    //          StructField("latitude", StringType),
    //          StructField("longitude", StringType),
    //          StructField("network_type", StringType),
    //          StructField("time", StringType),
    //          StructField("event_duration", StringType),
    //          StructField("share_depth", StringType),
    //          StructField("share_distinct_id", StringType),
    //          StructField("share_url_path", StringType),
    //          StructField("share_method", StringType),
    //          StructField("day", StringType, nullable = false)
    //        ))

    //    val schema = StructType(Array(StructField("id", StringType, nullable = false), StructField("uid", StringType, nullable = false),
    //      StructField("vid", StringType, nullable = false), StructField("dm_id", StringType, nullable = false),
    //      StructField("vid_type", IntegerType, nullable = false),
    //      StructField("cs", StringType, nullable = false), StructField("is_del", IntegerType, nullable = false),
    //      StructField("timestamp", LongType, nullable = false),
    //      StructField("first_time", IntegerType, nullable = false), StructField("update_time", IntegerType, nullable = false)))


    val kuduMasters = "dn-02.360che.com:7051,dn-03.360che.com:7051,dn-04.360che.com:7051"
    //        val kuduMasters = "dn01:7051,dn02:7051"
    val kuduContext = new KuduContext(kuduMasters, spark.sparkContext)
    spark.conf.set("spark.rpc.askTimeout", "900s")
    //    spark.conf.set("spark.serializer", "org.apache.spark.serializer.KryoSerializer")
    //    session.setTimeoutMillis(60000)
    //    new Nothing("hadoop6").defaultAdminOperationTimeoutMs(600000).build


    //    val lower = new PartialRow(kuduContext.createSchema(schema, Seq("partition")))
    //    lower.addInt("partition",0)
    //    val upper = new PartialRow(kuduContext.createSchema(schema, Seq("partition")))
    //    upper.addInt("partition",1)
    //    val lower2 = new PartialRow(kuduContext.createSchema(schema, Seq("partition")))
    //    lower2.addInt("partition",1)
    //    val upper2 = new PartialRow(kuduContext.createSchema(schema, Seq("partition")))
    //    upper2.addInt("partition",2)
    //    kuduContext.deleteTable("t_logdata_app_streaming_test")
    //    kuduContext.createTable("t_logdata_app_streaming_test", schema,
    //            Seq("id"),
    //          new CreateTableOptions().addHashPartitions(Seq("id").asJava, 9).setNumReplicas(3))

    //   spark.read
    //      .options(Map("kudu.master" -> kuduMasters, "kudu.table" -> "t_dwd_dmid"))
    //      .format("kudu").load.createOrReplaceTempView("t_dmid_tmp")
    //    spark.read
    //      .options(Map("kudu.master" -> kuduMasters, "kudu.table" -> "t_logdata_streaming"))
    //      .format("kudu").load
    //      .where("day='20200909'")
    //      .createOrReplaceTempView("t_log_tmp")
    //
    //
    //    spark.udf.register("uuid", Common.getUUID(_: String))
    //    spark.udf.register("getTimestamp", Common.getCurrentTimestamp(_: String, _: Int))
    //    val d = MyKudu.select("t_dwd_dmid_tmp")
    //      .createOrReplaceTempView("t_log_tmp")
    //    val d = spark.sql(
    //  """
    //    |select id,uid,vid,dm_id,is_del,vid_type,update_time,first_time,timestamp,if(cs='__HIVE_DEFAULT_PARTITION__','',cs) as cs
    //    |from t_dwd_dmid where day='20200916'
    //    |""".stripMargin)
    //    MyKudu.insert(d,"t_dwd_dmid")
    //    MyKudu.select("t_dwd_dmid").createOrReplaceTempView("t_dmid_tmp")

    //
    //    MyKudu.select("t_dwd_dmid_0803").createOrReplaceTempView("t_app_logs_tmp")
    //    val data = spark.sql("select id from t_app_logs_tmp where cs in ('pc','m','wx','') " +
    //      "and update_time<=20201101 and uid='' ")
    //    kuduContext.deleteRows(data,"t_dwd_dmid_0803")

    //    MyKudu.select("t_dwd_dmid").createOrReplaceTempView("t_app_logs_tmp")
    //    val data = spark.sql("select id from t_app_logs_tmp ")
    //    kuduContext.deleteRows(data,"t_dwd_dmid")

    //    val d = spark.sql(
    //      """
    //        |select id,uid,vid,dm_id,is_del,vid_type,update_time,first_time,timestamp,
    //        |if(cs='__HIVE_DEFAULT_PARTITION__','',cs) as cs from t_dwd_dmid where day='20201229'
    //        |""".stripMargin)
    //    kuduContext.insertRows(d,"t_dwd_dmid")

    myKudu.select("t_logdata_appsc_streaming_test").createOrReplaceTempView("t_app_logs_tmp")
    val data = spark.sql("select id from t_app_logs_tmp ")
    kuduContext.deleteRows(data, "t_logdata_appsc_streaming_test")

    //        val data = MyKudu.select("t_dwd_dmid")
    //    kuduContext.insertRows(data,"t_dwd_dmid_0803")
    //    spark.sql(
    //      """
    //        |select vid,count(1) from t_app_logs_tmp where is_del=0 and vid_type=0 group by vid having count(1)>1
    //        |""".stripMargin).foreach(f=>println(f))
    //

    //      spark.sql(
    //      """
    //        |select vid,dm_id,collect_set(uid) from t_app_logs_tmp group by vid,dm_id having size(collect_set(uid))>1 and
    //        | array_contains(collect_set(uid),'')
    //        |""".stripMargin).foreach(f=>println(f))

    //    MyKudu.select("t_logdata_streaming").where("day='20200919'")
    //      .createOrReplaceTempView("t_log_tmp")

    //    spark.sql(
    //      """
    //        |select l.cid,a.cid from
    //        |(select vid as cid,count(1) as num from t_log_tmp  group by vid) l left join (
    //        |select vid as cid,count(1) as num from t_logdata where day='20200919' group by vid) a on l.cid=a.cid and  l.num!=a.num
    //        |where a.cid is not null
    //        |""".stripMargin).foreach(f=>println(f))


  }

  def news: Unit ={
    val infoWebSchema = StructType(Array(
      StructField("id", StringType, nullable = false),
      StructField("k", StringType, nullable = false),
      StructField("uid", StringType, nullable = false),
      StructField("vid", StringType, nullable = false),
      StructField("sid", StringType, nullable = false),
      StructField("ip", StringType, nullable = false),
      StructField("daytime", StringType, nullable = false),
      StructField("billtypeid", StringType, nullable = false),
      StructField("eventid", StringType, nullable = false),
      StructField("action", StringType, nullable = false),
      StructField("lable", StringType, nullable = false),
      StructField("ua", StringType, nullable = false),
      StructField("typeid", StringType, nullable = false),
      StructField("day", StringType, nullable = false)
    ))
    createTable("t_ods_info_webdata_streaming",infoWebSchema)
    val infoAppscSchema = StructType(Array(
      StructField("id", StringType, nullable = false),
      StructField("cid", StringType, nullable = false),
      StructField("uid", StringType, nullable = false),
      StructField("event", StringType, nullable = false),
      StructField("screen_name", StringType, nullable = false),
      StructField("title", StringType, nullable = false),
      StructField("type_id", StringType, nullable = false),
      StructField("label", StringType, nullable = false),
      StructField("action", StringType, nullable = false),
      StructField("os", StringType, nullable = false),
      StructField("time", StringType, nullable = false),
      StructField("event_duration", StringType, nullable = false),
      StructField("day", StringType, nullable = false)
    ))
    createTable("t_ods_info_appscdata_streaming",infoAppscSchema)
    val infoAppSchema = StructType(Array(
      StructField("id", StringType, nullable = false),
      StructField("vid", StringType, nullable = false),
      StructField("t", StringType, nullable = false),
      StructField("writetime", StringType, nullable = false),
      StructField("daytime", StringType, nullable = false),
      StructField("cs", StringType, nullable = false),
      StructField("eventid", StringType, nullable = false),
      StructField("action", StringType, nullable = false),
      StructField("lable", StringType, nullable = false),
      StructField("uid", StringType, nullable = false),
      StructField("appid", StringType, nullable = false),
      StructField("typeid", StringType, nullable = false),
      StructField("day", StringType, nullable = false)
    ))
    createTable("t_ods_info_appdata_streaming",infoAppSchema)
    val spark=MySparkSession.conn()
    val myKudu=new MyKudu(spark)
    myKudu.select("t_logdata_appsc_streaming").createOrReplaceTempView("t_logdata_appsc_streaming_test")
    val ss=MySparkSession.conn()
    val appscDF=ss.sql(
      """
        |select id,cid ,uid,event,nvl(screen_name,"") as screen_name,title,type_id,label,action,os,time,event_duration,day from t_logdata_appsc_streaming_test
        |where day >='20211010' and
        |type_id in ("2","5") and event in ('CHES_00000001','AppClick','CHES_00000002') and  label!=''  and label rlike '^[0-9]*'
        |""".stripMargin)
    //    appscDF.show(false)
    myKudu.saveAsKudu(appscDF,"t_ods_info_appscdata_streaming")
    myKudu.select("t_logdata_streaming").createOrReplaceTempView("t_logdata_streaming_tmp")
    val webDF=ss.sql(
      s"""select id,k,uid,vid,sid,ip,daytime,
         |(case when k rlike '$articleurl' then  '1'  when k rlike '$videourl' then '2'
         | when k rlike '$picurl' then '3' end ) as billtypeid,eventid,action,lable,ua,substr(code,1,2) as typeid,
         | day from t_logdata_streaming_tmp where day='20211013' and  (k rlike '$articleurl'  or k rlike '$videourl'  or k rlike '$picurl') """.stripMargin)
    //    appscDF.show(false)
    myKudu.saveAsKudu(webDF,"t_ods_info_webdata_streaming")
    //app数据
    val newsAppLogData=myKudu.select("t_ods_info_appdata_streaming").select("id").where(s"day='20211010'")
    myKudu.delete(newsAppLogData,"t_ods_info_appdata_streaming")
    myKudu.select("t_logdata_app_streaming").createOrReplaceTempView("t_logdata_app_streaming_test")
    val appDF=ss.sql(
      s"""
         |select id,cid as vid,t, `time` as writetime,'' as daytime,cs,nvl(eventid,'') as eventid,nvl(action,'') as action,nvl(lable,'') as lable,uid,nvl(appid,'') as appid,
         |case cs when "android" then '14' when "iOS" then '13' else '0' end as typeid,day
         |from t_logdata_app_streaming_test
         |where day='20211010' and (eventid in('CHE_00000056','CHE_00000057','CHE_00000058') or (eventid in ('CHES_00000010','CHES_00000011','CHES_00000012') and t='event')) and cs in ("android","iOS")
         |""".stripMargin)
    //    appDF.show(false)
    myKudu.saveAsKudu(appDF,"t_ods_info_appdata_streaming")
  }

  /**
   * 实时数据数据手动恢复方法，删除kudu中的数据，从新从原始log数据查询导入,数据范围是天
   * @param day 恢复数据的日期
   */
  def newsRecover(day:String): Unit ={
    val spark=MySparkSession.conn()
    val myKudu=new MyKudu(spark)
    val newsAppLogTable="t_ods_info_appdata_streaming"
    val newsAppscLogTable="t_ods_info_appscdata_streaming"
    val newsWebLogTable="t_ods_info_webdata_streaming"
    //appsc数据
    val newsAppscLogData=myKudu.select(newsAppscLogTable).select("id").where(s"day='$day'")
    myKudu.delete(newsAppscLogData,newsAppscLogTable)
    myKudu.select("t_logdata_appsc_streaming").createOrReplaceTempView("t_logdata_appsc_streaming_test")
    val ss=MySparkSession.conn()
    val appscDF=ss.sql(
      s"""
         |select id,cid ,uid,event,nvl(screen_name,"") as screen_name,title,type_id,label,action,os,time,event_duration,day from t_logdata_appsc_streaming
         |where day ='$day' and
         |type_id in ("2","5") and event in ('CHES_00000001','AppClick','CHES_00000002') and  label!=''  and label rlike '^[0-9]*'
         |""".stripMargin)
    //    appscDF.show(false)
    myKudu.saveAsKudu(appscDF,"t_ods_info_appscdata_streaming")
    //app数据
    val newsAppLogData=myKudu.select(newsAppLogTable).select("id").where(s"day='$day'")
    myKudu.delete(newsAppLogData,newsAppLogTable)
    myKudu.select("t_logdata_app_streaming").createOrReplaceTempView("t_logdata_app_streaming_test")
    val appDF=ss.sql(
      s"""
         |select id,cid as vid,t, `time` as writetime,`time` as daytime,cs,nvl(eventid,'') as eventid,nvl(action,'') as action,nvl(lable,'') as lable,uid,nvl(appid,'') as appid,
         |case cs when "android" then '14' when "iOS" then '13' else '0' end as typeid,day
         |from t_logdata_app_streaming_test
         |where day='$day' and (eventid in('CHE_00000056','CHE_00000057','CHE_00000058') or (eventid in ('CHES_00000010','CHES_00000011','CHES_00000012') and t='event')) and cs in ("android","iOS")
         |""".stripMargin)
    //    appDF.show(false)
    myKudu.saveAsKudu(appDF,"t_ods_info_appdata_streaming")
    myKudu.select("t_logdata_streaming").createOrReplaceTempView("t_logdata_streaming_tmp")
    //web数据
    val newsWebLogData=myKudu.select(newsWebLogTable).select("id").where(s"day='$day'")
    myKudu.delete(newsWebLogData,newsWebLogTable)
    val webDF=ss.sql(
      s"""select id,k,uid,vid,sid,ip,daytime,
         |(case when k rlike '$articleurl' then  '1'  when k rlike '$videourl' then '2'
         | when k rlike '$picurl' then '3' end ) as billtypeid,eventid,action,lable,ua,substr(code,1,2) as typeid,
         | day from t_logdata_streaming_tmp where day='20211013' and  (k rlike '$articleurl'  or k rlike '$videourl'  or k rlike '$picurl') """.stripMargin)
    //    appscDF.show(false)
    myKudu.saveAsKudu(webDF,"t_ods_info_webdata_streaming")
  }

  /**
   * 实时数据数据手动恢复方法，删除kudu中的数据，从新从原始log数据查询导入,数据范围小时
   * @param day 恢复数据的日期
   */
  def newsRecover(day:String,startTimeStamp:String,endTimeStamp:String): Unit ={
    val newsAppLogTable="t_ods_info_appdata_streaming"
    val newsAppscLogTable="t_ods_info_appscdata_streaming"
    val newsWebLogTable="t_ods_info_webdata_streaming"
//    val day="20211108"
    val spark=MySparkSession.conn()
    val myKudu=new MyKudu(spark)
//    appsc数据
    val newsAppscLogData=myKudu.select(newsAppscLogTable).select("id").where(s"day='$day' and cast(`time` as bigint) BETWEEN $startTimeStamp and $endTimeStamp ")
    myKudu.kuduContext.asyncClient.newSession().setMutationBufferSpace(10000)
    myKudu.delete(newsAppscLogData,newsAppscLogTable)
    myKudu.select("t_logdata_appsc_streaming").createOrReplaceTempView("t_logdata_appsc_streaming_test")
    val ss=MySparkSession.conn()
    val appscDF=ss.sql(
      s"""
         |select max(id) as id ,cid ,uid,event,nvl(screen_name,"") as screen_name,title,type_id,label,action,os,time,event_duration,day
         |from t_logdata_appsc_streaming_test
         |where day ='$day'
         |and cast(`time` as bigint) BETWEEN $startTimeStamp and $endTimeStamp
         |and type_id in ("2","5") and event in ('CHES_00000001','AppClick','CHES_00000002') and  label!=''  and label rlike '^[0-9]*'
         |group by cid ,uid,event, screen_name,title,type_id,label,action,os,time,event_duration,day
         |""".stripMargin)
    //    appscDF.show(false)
    myKudu.upsert(appscDF,"t_ods_info_appscdata_streaming")
//        app数据
    val newsAppLogData=myKudu.select(newsAppLogTable).select("id").where(s"day='$day' and cast(`daytime` as bigint) BETWEEN $startTimeStamp and $endTimeStamp ")
    myKudu.kuduContext.asyncClient.newSession().setMutationBufferSpace(100000)
    myKudu.delete(newsAppLogData,newsAppLogTable)
    myKudu.select("t_logdata_app_streaming_test").createOrReplaceTempView("t_logdata_app_streaming_test")
    val appDF=ss.sql(
      s"""
         |select max(id) id ,cid as vid,t, `time` as writetime,`time` as daytime,cs,eventid,action,lable,uid,nvl(appid,'') as appid,case cs when 'iOS' then '13' when 'android' then '14' else '0' end as typeid,day
         |from t_logdata_app_streaming_test
         |where day='$day'
         |and cast(`time` as bigint) BETWEEN $startTimeStamp and $endTimeStamp
         |and (eventid in('CHE_00000056','CHE_00000057','CHE_00000058') or (eventid in ('CHES_00000010','CHES_00000011','CHES_00000012') and t='event')) and cs in ("android","iOS")
         |group by cid ,t, `time`,cs,eventid,action,lable,uid,appid, cs ,day
         |""".stripMargin)
//            appDF.show(false)
    myKudu.saveAsKudu(appDF,"t_ods_info_appdata_streaming")
    myKudu.select("t_logdata_streaming").createOrReplaceTempView("t_logdata_streaming_tmp")

//    web数据
    val newsWebLogData=myKudu.select(newsWebLogTable).select("id").where(s"day='$day' and cast(`daytime` as bigint) BETWEEN $startTimeStamp and $endTimeStamp")
    myKudu.kuduContext.asyncClient.newSession().setMutationBufferSpace(10000)
    myKudu.delete(newsWebLogData,newsWebLogTable)
    val webDF=ss.sql(
      s"""select max(id) id ,k,uid,vid,sid,ip,daytime,
         |(case when k rlike '$articleurl' then  '1'  when k rlike '$videourl' then '2'
         | when k rlike '$picurl' then '3' end ) as billtypeid,eventid,action,lable,ua,substr(code,1,2) as typeid,
         | day from t_logdata_streaming_tmp
         |where day='$day'
         |and cast(`daytime` as bigint) BETWEEN $startTimeStamp and $endTimeStamp
         |and  (k rlike '$articleurl'  or k rlike '$videourl'  or k rlike '$picurl')
         |group by k,uid,vid,sid,ip,daytime,k,eventid,action,lable,ua,code,day
         |  """.stripMargin)
    //    appscDF.show(false)
    myKudu.saveAsKudu(webDF,"t_ods_info_webdata_streaming")
  }



  //埋点查询
  def buryPoint(): Unit ={
    val ss=MySparkSession.conn()
    val myKudu=new MyKudu(ss)
    myKudu.select("t_logdata_appsc_streaming_test").createOrReplaceTempView("t_logdata_appsc_streaming_test")
    ss.sql(
      """
        |select event,screen_name,title,element_id,element_content,type_id,label,`action` ,time
        |from t_logdata_appsc_streaming_test
        |where day="20211105"
        |and  uid="1453577"
        | and manufacturer="HUAWEI"
        |--  and manufacturer="Apple"
        |-- and event="CHES_00000004"
        |order by time desc
        |""".stripMargin).show(1000,false)
  }




}
