package com.che.hadoop.logclean.activity.discard

import java.text.SimpleDateFormat
import java.util.Calendar

import com.che.hadoop.logclean.bo.discard.LogCleanWebHour
import com.che.hadoop.underlay.dao.MySparkSession

/**
  * Created by zkpk on 1/11/19.
  * web日志清洗，小时任务改成定时任务
  */

object LogCleanWebHourJob {
  def main(args: Array[String]): Unit = {
    val days=getDate(0,1)
    var day=days.substring(0,8)
    var hour=days.substring(8,10)
    if(args.length>0){
      val days=args(0)
      day=days.substring(0,8)
      hour=days.substring(8,10)
    }
    println(s"day=$day,hour=$hour")
//    val day="20220220"
//    val hour="01"
    val spark=MySparkSession.conn()
    LogCleanWebHour.saveLogCleanToHive(spark,day,hour) /*清洗数据到hive*/
    spark.stop()


  }

  /*
   得到当前日期前一天
   */
  def getDate(day: Int,hour: Int): String = {
    val dateFormat: SimpleDateFormat = new SimpleDateFormat("yyyyMMddHH")
    var cal: Calendar = Calendar.getInstance()
    cal.add(Calendar.DATE, -day)
    cal.add(Calendar.HOUR, -hour)
    val date = dateFormat.format(cal.getTime())
    return date

  }


}
