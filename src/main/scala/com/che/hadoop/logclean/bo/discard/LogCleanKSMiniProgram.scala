package com.che.hadoop.logclean.bo.discard

import java.util.{Base64, ResourceBundle}

import com.alibaba.fastjson.JSON
import com.che.hadoop.underlay.dao.{MySparkSession, MyKudu}
import com.che.hadoop.logclean.utils.{Common, GZIPUtils, UrlDecodeUtil}
import org.apache.kudu.spark.kudu.KuduContext
import org.apache.spark.sql.Row
import org.apache.spark.sql.types.{StringType, StructField, StructType}

/**
 * 清洗快手小程序合并微信小程序日志清洗
 * --上报神策
 * 快手司机招聘appid:ks682013872806558279
 * 微信卡车之家appid:wx9e5224a972e8865b
 * 微信智能名片appid:wxf0a9b3f2249087ca
 * --未上报神策
 * 微信司机招聘appid:wx0ac404e0e304e869
 * 微信卡家二手车appid:wx8ff059ebddf1438b
 * 李晓乾
 */
object LogCleanKSMiniProgram {
  def main(args: Array[String]): Unit = {
    Common.getBetweenDates("20211101","20211103").foreach(day=>{
      cleanKSMiniProgramLog(day)
    })
  }

  /**
   * 小程序日志清洗
   * @param day
   */
  def cleanKSMiniProgramLog(day:String): Unit ={
    val dataStruct = StructType(Array(
      StructField("openid", StringType, true), StructField("anonymous_id", StringType, true),
      StructField("distinct_id", StringType, true), StructField("uid", StringType, true),
      StructField("event", StringType, true), StructField("screen", StringType, true),
      StructField("latest_scene", StringType, true), StructField("url_path", StringType, true),
      StructField("title", StringType, true), StructField("url_query", StringType, true),
      StructField("referrer", StringType, true), StructField("element_id", StringType, true),
      StructField("element_content", StringType, true), StructField("element_name", StringType, true),
      StructField("type_id", StringType, true), StructField("label", StringType, true),
      StructField("action", StringType, true), StructField("element_type", StringType, true),
      StructField("manufacturer", StringType, true), StructField("os", StringType, true),
      StructField("model", StringType, true), StructField("os_version", StringType, true),
      StructField("app_id", StringType, true), StructField("screen_width", StringType, true),
      StructField("screen_height", StringType, true), StructField("ads", StringType, true),
      StructField("latitude", StringType, true), StructField("longitude", StringType, true),
      StructField("network_type", StringType, true), StructField("time", StringType, true),
      StructField("event_duration", StringType, true), StructField("share_depth", StringType, true),
      StructField("share_distinct_id", StringType, true), StructField("share_url_path", StringType, true),
      StructField("share_method", StringType, true),
      StructField("day", StringType, true)
    ))
    val spark = MySparkSession.conn("LogClean_LogCleanMiniProgram")
    val logTable = "t_ods_miniprogram_log"
    val dirPath = "hdfs://nameservice1/pv-nginx-mpsc/" + day
    val LogRdd = spark.read.json(dirPath).filter(t => t.getAs[String]("status") == "200"
      && t.getAs[String]("http_host") == "thamp.360che.com") //过滤业务日志，参数为"-"
    val logData = LogRdd.rdd.map(line =>{
      var list: List[Row] = Nil
      val request_body = line.getAs[String]("request_body")
      val args=line.getAs[String]("args")
      //日志数据的主体
      val decoder = Base64.getDecoder
      var logdata=""
      //如果request_body为"-",说明是GET请求，从args中取数据；否则从request_body中取数据
      var dataList=""
      try{
        if(request_body=="-"){
          logdata=Common.getResByName(args, "data")
          val dataObject=new String(decoder.decode(UrlDecodeUtil.urlDecode(logdata)))
          //把GET请求中的Object类型数据组装成list类型数据
          dataList="["+dataObject+"]"
        }else{
          logdata=Common.getResByName(request_body, "data_list")
          val decodeStr=decoder.decode(UrlDecodeUtil.urlDecode(logdata))
          //判断字符是否被GZIP压缩
          if(decodeStr(0)==0x1F && decodeStr(1)== -117){
            dataList = GZIPUtils.uncompressToString(decodeStr)
          }else{
            dataList=new String(decodeStr)
          }
        }
      }catch {
        case ex:Exception=>{
          ex.printStackTrace()
        }
      }
      var dataJson:com.alibaba.fastjson.JSONArray=null
      try{
        dataJson = JSON.parseArray(dataList)
      }catch {
        case ex:com.alibaba.fastjson.JSONException=>{
          ex.printStackTrace()
          println(dataList)
        }
      }
      if (dataJson != null) {
        for (a <- 0 until dataJson.size()) {
          try {
            if(List("MiniProgram","KuaishouMini").contains(dataJson.getJSONObject(a).getJSONObject("properties").getOrDefault("$lib", "").toString)){
              val argObj = dataJson.getJSONObject(a)
              val properties = argObj.getJSONObject("properties")
              val time = argObj.getOrDefault("time",properties.getString("$time")).toString
              val event = argObj.getOrDefault("event","").toString.replace("$", "")
              val uid = argObj.getOrDefault("login_id", "").toString
              val distinctId = argObj.getOrDefault("distinct_id","").toString
              val anonymousId = argObj.getOrDefault("anonymous_id","").toString
              val lib = properties.getOrDefault("$lib", "").toString
              val openId = properties.getOrDefault("$open_id", "").toString
              var title=""
              //快手司机招聘小程序title 和其它小程序不一致
              if(lib=="KuaishouMini") {
                title = properties.getOrDefault("title", "").toString
              }else if(lib=="MiniProgram"){
                title = properties.getOrDefault("$title", "").toString
              }
              val screen = properties.getOrDefault("$screen", "").toString
              val latestScene = properties.getOrDefault("$latest_scene", "").toString
              val urlPath = properties.getOrDefault("$url_path", "").toString
              val urlQuery = properties.getOrDefault("$url_query", "").toString
              val referrer = properties.getOrDefault("$referrer", "").toString
              val elementId = properties.getOrDefault("$element_id", "").toString
              val elementName = properties.getOrDefault("$element_name", "").toString
              val elementContent = properties.getOrDefault("$element_content", "").toString
              val typeId = properties.getOrDefault("$type_id", "").toString
              val label = properties.getOrDefault("$label", "").toString
              val action = properties.getOrDefault("$action", "").toString
              val elementType = properties.getOrDefault("$element_type", "").toString
              val manufacturer = properties.getOrDefault("$manufacturer","").toString
              val os = properties.getOrDefault("$os","").toString
              val model = properties.getOrDefault("$model","").toString
              val osVersion = properties.getOrDefault("$os_version","").toString
              val appId = properties.getOrDefault("$app_id","").toString
              val screenWidth = properties.getOrDefault("$screen_width","").toString
              val screenHeight = properties.getOrDefault("$screen_height","").toString
              val ads = properties.getOrDefault("$ads", "").toString
              var latitude = ""
              var longitude = ""
              //微信司机招聘小程序appid,不能直接取经纬度
              if(appId=="wx0ac404e0e304e869"){
                if(properties.containsKey("$latitude")){
                  latitude = (properties.getDouble("$latitude")/1000000).toString
                }
                if(properties.containsKey("$longitude")){
                  longitude = (properties.getDouble("$longitude")/1000000).toString
                }
              }else{
                latitude = properties.getOrDefault("$latitude", "").toString
                longitude = properties.getOrDefault("$longitude", "").toString
              }
              val networkType = properties.getOrDefault("$network_type","").toString
              val eventDuration = properties.getOrDefault("$event_duration", "").toString
              val shareDepth = properties.getOrDefault("$share_depth", "").toString
              val shareDistinctId = properties.getOrDefault("$share_distinct_id", "").toString
              val shareUrlPath = properties.getOrDefault("$share_url_path", "").toString
              val shareMethod = properties.getOrDefault("$share_method", "").toString
              list = list :+ Row(openId,anonymousId,distinctId, uid,event,screen,latestScene,
                urlPath,title,  urlQuery,referrer, elementId,elementContent,elementName,  typeId, label, action,elementType,
                manufacturer, os, model, osVersion,appId,screenWidth,screenHeight,ads,latitude,longitude,networkType,time,
                eventDuration,shareDepth,shareDistinctId,shareUrlPath,shareMethod,day)
            }
          }catch {
            case ex: ClassCastException => {
              ex.printStackTrace()
            }
            case ex: NullPointerException => {
              ex.printStackTrace()
            }
            case ex:Exception=>{
              ex.printStackTrace()
            }
          }
        }
      }
      list
    }).flatMap(x=>x)
    spark.createDataFrame(logData,dataStruct).repartition(1).write.mode("append").format("Hive")
      .partitionBy("day").saveAsTable(logTable)
    spark.stop()
  }

  /**
   * 删除
   * @param day
   */
  def delKuduLog(day:String): Unit ={
    val spark = MySparkSession.conn()
    val mpLogKuduTable = "t_logdata_mp_streaming"
    val kuduMasters = ResourceBundle.getBundle("connection").getString("kudu.master")
    spark.conf.set("spark.rpc.askTimeout", "600s")
    // 删除两天前的实时数据
    val before2Day = Common.getSpecifyDate(day, 2)
    val myKudu=new MyKudu(spark)
    myKudu.select(mpLogKuduTable).createOrReplaceTempView("t_log_tmp")
    val mpOldData = spark.sql(s"select id from t_log_tmp where day<='$before2Day'")
    val kuduContext = new KuduContext(kuduMasters, spark.sparkContext)
    kuduContext.deleteRows(mpOldData, mpLogKuduTable)

  }

}
