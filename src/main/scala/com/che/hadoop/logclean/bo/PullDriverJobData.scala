package com.che.hadoop.logclean.bo

import java.util.ResourceBundle

import com.alibaba.fastjson.JSON
import com.che.hadoop.underlay.tool.http.MyHttpService
import com.che.hadoop.logclean.utils.Common
import com.che.hadoop.underlay.dao.{MySparkSession,MySqlBaseDao}
import org.apache.spark.sql.{Row, SparkSession}
import org.apache.spark.sql.types.{IntegerType, StringType, StructField, StructType}
/**
 * 司机招聘相关数据同步
 * Author:LiXiaoQian
 */

object PullDriverJobData {
  def main(args: Array[String]): Unit = {
    val spark=MySparkSession.conn()
    val sqlBase=new MySqlBaseDao()
    PullDriverJobData.getRecruitPublish(spark,sqlBase,"20230418")
//    Common.getBetweenDates("20210326","20210529").foreach(day=>{
//      getDriverRegisterData(spark,day)
//    })
//    spark.stop()
  }

  /**
   * 拉取司机招聘用户注册信息
   */
  def getDriverRegisterData(spark:SparkSession, day:String): Unit ={
    val sqlBase=new MySqlBaseDao("bigdata2")
    try {
      //获取请求参数
      val sign_time=System.currentTimeMillis().toString
      val date:String =day.substring(0,4)+"-"+day.substring(4,6)+"-"+day.substring(6,8)
      val message:String=s"date=$date&sign_time=$sign_time"
      //签名算法
      val sign=Common.sign(message)
      //    println(sign_time,sign)
      val paramMap:Map[String,String]=Map(("date",date),("sign",sign),("sign_time",sign_time))
      paramMap.foreach(item=>{println(item)})
      val reader=ResourceBundle.getBundle("connection")
      val api=reader.getString("api.driverjob.register")
      //    println(api)
      val entityStr=MyHttpService.post(api,paramMap)
      val arrayData=JSON.parseArray(entityStr)
      val indexRDD=spark.sparkContext.parallelize(0 until arrayData.size())
      val rowRDD=indexRDD.map(i=>{
        val itemData=arrayData.getJSONObject(i)
        val id=itemData.getIntValue("id")
        val name=itemData.getString("name")
        val createDatetime=itemData.getString("created_at")
        val clientType=itemData.getIntValue("type")
        Row(id,name,createDatetime,clientType,day)
      })
      val schema=StructType(Array(
        StructField("id",IntegerType,true),
        StructField("name",StringType,true),
        StructField("create_datetime",StringType,true),
        StructField("client_type",IntegerType,true),
        StructField("day",StringType,true)
      ))
      val resultDF=spark.sqlContext.createDataFrame(rowRDD,schema)
      spark.sql(
        s"""
           |ALTER TABLE t_ods_driverjob_register_info DROP IF EXISTS PARTITION(day=$day)
           |""".stripMargin)
      resultDF.repartition(1).write.mode("append")
        .format("Hive").partitionBy("day").saveAsTable("t_ods_driverjob_register_info")
      sqlBase.executeInsertOrUpdate(s"update t_check set flag=1, message='$day:运行成功' where name='aliyun_t_ods_driverjob_register_info'",null)
      //结果数据写入mysql
      sqlBase.execute(s"delete from db_hitsstatistic.t_driverjob_register_num where f_date=$date")
      val registerNumDF=spark.sql(
        s"""
           |select count(1)as f_num,client_type as f_client,
           |date_format(create_datetime ,"yyyy-MM-dd") as f_date
           |from t_ods_driverjob_register_info
           |where date_format(create_datetime ,"yyyyMMdd")="$day"
           |group by date_format(create_datetime ,"yyyy-MM-dd"),client_type
           |union all
           |select count(1)as f_num,0 as f_client,
           |date_format(create_datetime ,"yyyy-MM-dd") as f_date
           |from t_ods_driverjob_register_info
           |where date_format(create_datetime ,"yyyyMMdd")="$day"
           |group by date_format(create_datetime ,"yyyy-MM-dd")
           |""".stripMargin)
      sqlBase.saveMysql(registerNumDF,"db_hitsstatistic.t_driverjob_register_num")
    } catch {
      case e: Exception => {
        e.printStackTrace()
        try{
          sqlBase.executeInsertOrUpdate(s"update t_check set flag=2, message='$day:aliyun_t_ods_driverjob_register_info 运行失败' where name='aliyun_t_ods_driverjob_register_info'",null)
        }catch {
          case e:Throwable=>{
            e.printStackTrace()
          }
        }
      }
    }

  }

  /**
   * 业务数据同步，司机招聘发布职位
   * @param spark SparkSession
   * @param sqlBase mysql数据库操作实例
   * @param day 计算日期
   */
  def getRecruitPublish(spark:SparkSession,sqlBase:MySqlBaseDao,day:String): Unit ={
    try{
      val resultMysqlTable="db_userportrait.t_recruit_publish"
      val resultDF=spark.sql(
        s"""
           |select c0 as id,c1 as vid,c2 as uid,base64(c3) as tel,c4 as province_id,c5 as province_name,c6 as city_id,c7 as city_name,c8 as driver_license,c9 as start_money,c10 as end_money,c11 as `type`,c12 as status,c13 as create_time,c14 as name,`timestamp` as update_timestamp,ROW_NUMBER() OVER(partition by c0 order by `timestamp` desc) as rank from
           |(select json_tuple(data, 'id', 'vid', 'userId','userPhone', 'province_id','province_name', 'city_id','city_name', 'driver_license','start_money','end_money','type','status','createTime','position_name'),`timestamp`
           |from t_biz_data_temp where billType='recruit_publish') as t
           |""".stripMargin).filter("rank=1").drop("rank")
      //保存数据
      val args=resultDF.collect().map(row=>List(row.getAs[Int]("id") ,row.getAs[String]("name"),row.getAs[String]("vid"),row.getAs[String]("uid"),row.getAs[String]("tel"),row.getAs[String]("province_id"),row.getAs[String]("province_name"),
        row.getAs[String]("city_id"),row.getAs[String]("city_name"),row.getAs[Int]("driver_license"),row.getAs[Int]("start_money"),row.getAs[Int]("end_money")
        ,row.getAs[String]("type"),row.getAs[String]("status"),row.getAs[String]("create_time"),row.getAs[String]("update_timestamp"))).toList
      sqlBase.executeMany(
        s"""
           |REPLACE INTO $resultMysqlTable (id, name, vid, uid, tel, province_id, province_name, city_id, city_name, driver_license, start_money, end_money, `type`, status, create_time, update_timestamp) VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)
           |""".stripMargin,args)
      sqlBase.execute(s"update t_check set flag=1, message='$day:t_recruit_publish 运行成功' where name='aliyun_t_recruit_publish'")
    }catch {
      case e: Exception => {
        e.printStackTrace()
        try{
          val exec = new MySqlBaseDao()
          exec.execute(s"update t_check set flag=2, message='$day:t_recruit_publish 运行失败' where name='aliyun_t_recruit_publish'")
        }catch {
          case e:Throwable=>{
            e.printStackTrace()
          }
        }
      }
    }
  }

}
