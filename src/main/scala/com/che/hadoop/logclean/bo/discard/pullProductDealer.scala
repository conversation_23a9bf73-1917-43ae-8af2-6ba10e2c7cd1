package com.che.hadoop.logclean.bo.discard

import com.che.hadoop.underlay.dao.{MyKudu, MySparkSession, MySqlBaseDao}
import org.apache.spark.sql.SparkSession

import scala.util.matching.Regex

/**
 * 产品库相关数据 -转实时离线废弃
 * */
object pullProductDealer {
  case class ProductInfo(id: Int, name: String, brandId: Int, seriesId: Int, subCategoryId: Int, enable: Int, tonnageType: Int, cateId: Int, isSell: Int, publishTime: String, price: Float, drivingForm: String, horsePower: Int, containerLength: String, containerForm: String, totalMass: Float,fuelType:String, energyType: String, forwardGear: Int, engineBrand: String, emissionStandard: String, seatNum: String, marketSeg: String, batteryLife: Int, displacement: Double, containerBrand: String, torque: Float, ratedSpeed: Int, gearBrand: String, axleDesc: String, axleRatio: Double, axleLoad: Float, vmass: Float, maxSpeed: Float, vwidth: Float, ratedLoad: Double, shiftForm: String, peakPower: Int, wheelbase: String, vlength: Double, chassisSeries: String, cabCapacity: Int, longBeamHeight: String, axleLoad140: String, axleBrand: String, priceMark: String, spectacle: String)
  def main(args: Array[String]): Unit = {
    val spark = MySparkSession.conn()
    val sqlBase=new MySqlBaseDao("che_test")
    val day="20231227"
    val myKudu=new MyKudu(spark)
    myKudu.select("t_logdata_thba_streaming").select("data", "billtype", "timestamp", "operation_state", "env").where(
      s"""
         |day='$day'
         |and env = 'pro'
         |""".stripMargin).createOrReplaceTempView("t_biz_data_temp")
//    pullDealerArticle(spark,sqlBase)
    productTruck(spark,sqlBase,day)
//    productSeries(spark,sqlBase,day)
//    productBrand(spark,sqlBase,day)
//    productSubcate(spark, sqlBase, day)
//    productSeriesTonnage(spark, sqlBase, day)
//    productBrandSeriesSubcate(spark, sqlBase, day)

  }

  /**
   * 同步产品库车型相关数据
   * @param spark SparkSession
   * @param sqlBase MySql工具类
   * @param day 日期
   * */
  def productTruck(spark: SparkSession, sqlBase: MySqlBaseDao, day: String): Unit = {
    try {
      val productTable = "db_userportrait.t_product"
      val truckAttributeTable = "db_userportrait.t_truck_attribute"
      import spark.implicits._
      val myKudu=new MyKudu(spark)
      myKudu.select("t_logdata_thba_streaming").select("data", "billtype","timestamp","operation_state","env").where(
        s"""
          |day>='$day'
          |and env = 'pro' and `data` regexp '179021' and  billtype ='product_truck'
          |""".stripMargin).createOrReplaceTempView("t_biz_data_temp")
//      spark.sql(
//        """
//          |select explode(from_json(data, 'array<string>')) AS item,timestamp
//          |from  t_biz_data_temp where billType='product_truck'
//          |""".stripMargin).show()
      spark.sql(
        s"""
           |select c0 as id,c1 as name,c2 as brandId,c3 as seriesId,c4 as subCategoryId,c5 as enable,c6 as dwjb,c7 as cateId,c8 as isStopMake,c9 as publishTime,c10 as price,c11 as qdxs,c12 as power,c13 as hxcd,c14 as hxxs,c15 as zzl,c16 as rlzl, c17 as rllb, c18 as qjdw, c19 as fdjpp, c20 as pfbz, c21 as seatNum, c22 as marketSeg, c23 as batteryLife, c24 as displacement, c25 as containerBrand, c26 as torque, c27 as ratedSpeed, c28 as gearBrand, c29 as axleDesc, c30 as axleRatio, c31 as axleLoad, c32 as vmass, c33 as maxSpeed, c34 as vwidth, c35 as ratedLoad110,c36 as ratedLoad041, c37 as ratedLoad140, c38 as shiftForm, c39 as peakPower, c40 as wheelbase, c41 as vlength, c42 as chassisSeries, c43 as cabCapacity, c44 as longBeamHeight, c45 as axleLoad140, c46 as axleBrand, c47 as priceMark, c48 as spectacle, `timestamp`, ROW_NUMBER() OVER(partition by c0 order by `timestamp` desc) as rank from
           |(select json_tuple(item, 'id','name','brandId','seriesId','subCategoryId','enable','dwjb','cateId','isStopMake','publishTime','price','qdxs','power','hxcd','hxxs','zzl','rlzl','rllb','qjdw','fdjpp','pfbz','seatNum','marketSeg','batteryLife','displacement','containerBrand','torque','ratedSpeed','gearBrand','axleDesc','axleRatio','axleLoad','vmass','maxSpeed','vwidth','ratedLoad110','ratedLoad041','ratedLoad140','shiftForm','peakPower','wheelbase','vlength','chassisSeries','cabCapacity','longBeamHeight','axleLoad140','axleBrand','priceMark','spectacle'),`timestamp`
           |from (select explode(from_json(data, 'array<string>')) AS item,timestamp
           |from  t_biz_data_temp where billType='product_truck') as t ) as tt
           |""".stripMargin).filter("rank=1").drop("rank").show(1000,false)
      return
      val resultDF = spark.sql(
        s"""
           |select c0 as id,c1 as name,c2 as brandId,c3 as seriesId,c4 as subCategoryId,c5 as enable,c6 as dwjb,c7 as cateId,c8 as isStopMake,c9 as publishTime,c10 as price,c11 as qdxs,c12 as power,c13 as hxcd,c14 as hxxs,c15 as zzl,c16 as rlzl, c17 as rllb, c18 as qjdw, c19 as fdjpp, c20 as pfbz, c21 as seatNum, c22 as marketSeg, c23 as batteryLife, c24 as displacement, c25 as containerBrand, c26 as torque, c27 as ratedSpeed, c28 as gearBrand, c29 as axleDesc, c30 as axleRatio, c31 as axleLoad, c32 as vmass, c33 as maxSpeed, c34 as vwidth, c35 as ratedLoad110,c36 as ratedLoad041, c37 as ratedLoad140, c38 as shiftForm, c39 as peakPower, c40 as wheelbase, c41 as vlength, c42 as chassisSeries, c43 as cabCapacity, c44 as longBeamHeight, c45 as axleLoad140, c46 as axleBrand, c47 as priceMark, c48 as spectacle, `timestamp`, ROW_NUMBER() OVER(partition by c0 order by `timestamp` desc) as rank from
           |(select json_tuple(item, 'id','name','brandId','seriesId','subCategoryId','enable','dwjb','cateId','isStopMake','publishTime','price','qdxs','power','hxcd','hxxs','zzl','rlzl','rllb','qjdw','fdjpp','pfbz','seatNum','marketSeg','batteryLife','displacement','containerBrand','torque','ratedSpeed','gearBrand','axleDesc','axleRatio','axleLoad','vmass','maxSpeed','vwidth','ratedLoad110','ratedLoad041','ratedLoad140','shiftForm','peakPower','wheelbase','vlength','chassisSeries','cabCapacity','longBeamHeight','axleLoad140','axleBrand','priceMark','spectacle'),`timestamp`
           |from (select explode(from_json(data, 'array<string>')) AS item,timestamp
           |from  t_biz_data_temp where billType='product_truck' ) as t ) as tt
           |""".stripMargin).filter("rank=1").drop("rank").map(row=>{
        val id = row.getAs[Any]("id") match {
          case null => 0
          case _ =>
            row.getAs[String]("id").trim.toInt
        }
        val name = row.getAs[String]("name") match {
          case null => ""
          case _ => row.getAs[String]("name").trim
        }
        val brandId = row.getAs[Any]("brandId") match {
          case null => 0
          case _ => row.getAs[String]("brandId").trim.toInt
        }
        val seriesId = row.getAs[Any]("seriesId") match {
          case null => 0
          case _ => row.getAs[String]("seriesId").trim.toInt
        }
        val subCategoryId = row.getAs[Any]("subCategoryId") match {
          case null => 0
          case _ => row.getAs[String]("subCategoryId").trim.toInt
        }
        val enable = row.getAs[Any]("enable") match {
          case null => 0
          case _ => row.getAs[String]("enable").trim.toInt
        }
        val tonnageType = row.getAs[String]("dwjb") match {
          case null => 0
          case "重卡" => 1
          case "重型载货车" => 1
          case "中卡" => 2
          case "轻卡" => 3
          case "微卡" => 4
          case _ => 0
        }
        val cateId = row.getAs[Any]("cateId") match {
          case null => 0
          case _ => row.getAs[String]("cateId").trim.toInt
        }
        val isSell = row.getAs[Any]("isStopMake") match {
          case null => 0
          case _ => row.getAs[String]("isStopMake").trim.toInt
        }
        val publishTime = row.getAs[String]("publishTime") match {
          case null => ""
          case _ => row.getAs[String]("publishTime")
        }
        val priceRegex = new Regex("""(\d+(\.\d+)?)万元""")
        val price = row.getAs[String]("price")match {
          case null => 0.0f
          case text: String => priceRegex.findFirstMatchIn(text) match {
            case Some(m) => m.group(1).toFloat
            case None => 0.0f
          }
          case _ => 0.0f
        }
        val drivingForm = row.getAs[String]("qdxs") match{
          case null => ""
          case _ => row.getAs[String]("qdxs").trim
        }
        val powerRegex = new Regex("""(\d+)马力""")
        val horsePower = row.getAs[String]("power") match {
          case null => 0
          case text: String => powerRegex.findFirstMatchIn(text) match {
            case Some(m) => m.group(1).toInt
            case None => 0
          }
          case _ => 0
        }
        val hxcdRegex = new Regex("""(\d+(\.\d+)?)米""")
        val containerLength = row.getAs[String]("hxcd") match {
          case null => "0"
          case text: String => hxcdRegex.findFirstMatchIn(text) match {
            case Some(m) => m.group(1)
            case None => "0"
          }
          case _ => "0"
        }
        val containerForm = row.getAs[String]("hxxs") match {
          case null => ""
          case _ => row.getAs[String]("hxxs").trim
        }
        val zzlRegex = new Regex("""(\d+(\.\d+)?)吨|T""")
        val totalMass = row.getAs[String]("zzl") match {
          case null => 0.0f
          case text: String => zzlRegex.findFirstMatchIn(text) match {
            case Some(m) => m.group(1).toFloat
            case None => 0.0f
          }
          case _ => 0.0f
        }
        val fuelType= row.getAs[String]("rllb") match {
          case null => ""
          case _ => row.getAs[String]("rllb").trim
        }
        val energyType = row.getAs[String]("rllb") match {
          case null => ""
          case _ => row.getAs[String]("rllb").trim
        }
        val qjdwRegex = new Regex("""(\d+)档|挡""")
        val forwardGear = row.getAs[String]("qjdw") match {
          case null => 0
          case text:String => qjdwRegex.findFirstMatchIn(text) match {
            case Some(m) => m.group(1).toInt
            case None => 0
          }
          case _ => 0
        }
        val engineBrand = row.getAs[String]("fdjpp") match {
          case null => ""
          case _ => row.getAs[String]("fdjpp").trim
        }
        val emissionStandard = row.getAs[String]("pfbz") match {
          case null => ""
          case _ => row.getAs[String]("pfbz").trim
        }
        val seatNum = row.getAs[String]("seatNum") match {
          case null => ""
          case _ => row.getAs[String]("seatNum").trim
        }
        val marketSeg = row.getAs[String]("marketSeg") match {
          case null => ""
          case _ => row.getAs[String]("marketSeg").trim
        }
        val batteryLifeRegex = new Regex("""(\d+)km""")
//      厂标续航 batteryLife
        val batteryLife = row.getAs[String]("batteryLife") match {
          case null => 0
          case text:String => batteryLifeRegex.findFirstMatchIn(text) match {
            case Some(m) => m.group(1).toInt
            case None => 0
          }
          case _ => 0
        }
        val displacementRegex = new Regex("""(\d+(\.\d+)?)(l|L)""")
        val displacement = row.getAs[String]("displacement") match {
          case null => 0.0
          case text: String => displacementRegex.findFirstMatchIn(text) match {
            case Some(m) => m.group(1).toDouble
            case None => 0.0
          }
          case _ => 0.0
        }
        val containerBrand = row.getAs[String]("containerBrand") match {
          case null => ""
          case _ => row.getAs[String]("containerBrand").trim
        }
        val torqueRegex = new Regex("""(\d+(\.\d+)?)(N|n)·(M|m)""")
        val torque = row.getAs[String]("torque") match {
          case null => 0.0f
          case text: String => torqueRegex.findFirstMatchIn(text) match {
            case Some(m) => m.group(1).toFloat
            case None => 0.0f
          }
          case _ => 0.0f
        }
        val ratedSpeedRegex = new Regex("""(\d+)rpm""")
        val ratedSpeed = row.getAs[String]("ratedSpeed") match {
          case null => 0
          case text: String => ratedSpeedRegex.findFirstMatchIn(text) match {
            case Some(m) => m.group(1).toInt
            case None => 0
          }
          case _ => 0
        }
        val gearBrand = row.getAs[String]("gearBrand") match {
          case null => ""
          case _ => row.getAs[String]("gearBrand").trim
        }
        val axleDesc = row.getAs[String]("axleDesc") match {
          case null => ""
          case _ => row.getAs[String]("axleDesc").trim
        }
        val axleRatioRegex = new Regex("""(\d+(\.\d+)?)""")
        val axleRatio = row.getAs[String]("axleRatio") match {
          case null => 0.0
          case text: String => axleRatioRegex.findFirstMatchIn(text) match {
            case Some(m) => m.group(1).toDouble
            case None => 0.0
          }
          case _ => 0.0
        }
        val axleLoadRegex = new Regex("""(\d+(\.\d+)?)(kg|KG)""")
        val axleLoad = row.getAs[String]("axleLoad") match {
          case null => 0.0f
          case text: String => axleLoadRegex.findFirstMatchIn(text) match {
            case Some(m) => m.group(1).toFloat
            case None => 0.0f
          }
          case _ => 0.0f
        }
        val vmassRegex = new Regex("""(\d+(\.\d+)?)吨""")
        val vmass = row.getAs[String]("vmass") match {
          case null => 0.0f
          case text: String => vmassRegex.findFirstMatchIn(text) match {
            case Some(m) => m.group(1).toFloat
            case None => 0.0f
          }
          case _ => 0.0f
        }
        val maxSpeedRegex = new Regex("""(\d+(\.\d+)?)km/h""")
        val maxSpeed = row.getAs[String]("maxSpeed") match {
          case null => 0.0f
          case text: String => maxSpeedRegex.findFirstMatchIn(text) match {
            case Some(m) => m.group(1).toFloat
            case None => 0.0f
          }
          case _ => 0.0f
        }
        val vwidthRegex = new Regex("""(\d+(\.\d+)?)米""")
        val vwidth = row.getAs[String]("vwidth") match {
          case null => 0.0f
          case text: String => vwidthRegex.findFirstMatchIn(text) match {
            case Some(m) => m.group(1).toFloat
            case None => 0.0f
          }
          case _ => 0.0f
        }
        val ratedLoad110 = row.getAs[String]("ratedLoad110")
        val ratedLoad041 = row.getAs[String]("ratedLoad041")
        val ratedLoad140 = row.getAs[String]("ratedLoad140")
        val ratedLoadTmp = Option(ratedLoad110).orElse(Option(ratedLoad041)).orElse(Option(ratedLoad140)).getOrElse("0")
        val ratedLoadRegex = new Regex("""(\d+(\.\d+)?)吨""")
        val ratedLoad = ratedLoadTmp match {
          case text: String => ratedLoadRegex.findFirstMatchIn(text) match {
            case Some(m) => m.group(1).toDouble
            case None => 0.0
          }
          case _ => 0.0
        }
        val shiftForm = row.getAs[String]("shiftForm") match {
          case null => ""
          case _ => row.getAs[String]("shiftForm").trim
        }
        val peakPowerRegex = new Regex("""(\d+)(kW|kw)""")
        val peakPower = row.getAs[String]("peakPower") match {
          case null => 0
          case text: String => peakPowerRegex.findFirstMatchIn(text) match {
            case Some(m) => m.group(1).toInt
            case None => 0
          }
          case _ => 0
        }
        val wheelbase = row.getAs[String]("wheelbase") match {
          case null => ""
          case _ => row.getAs[String]("wheelbase")
        }
        val vlengthRegex = new Regex("""(\d+(\.\d+)?)米""")
        val vlength = row.getAs[String]("vlength") match {
          case null => 0.0
          case text: String => vlengthRegex.findFirstMatchIn(text) match {
            case Some(m) => m.group(1).toDouble
            case None => 0.0
          }
          case _ => 0.0
        }
        val chassisSeries = row.getAs[String]("chassisSeries") match {
          case null => ""
          case _ => row.getAs[String]("chassisSeries").trim
        }
        val cabCapacityRegex = new Regex("""(\d+)人""")
        val cabCapacity = row.getAs[String]("cabCapacity") match {
          case null => 0
          case text: String => cabCapacityRegex.findFirstMatchIn(text) match {
            case Some(m) => m.group(1).toInt
            case None => 0
          }
          case _ => 0
        }
        val longBeamHeightRegex = new Regex("""(\d+)毫米""")
        val longBeamHeight = row.getAs[String]("longBeamHeight") match {
          case null => "0"
          case text:String => longBeamHeightRegex.findFirstMatchIn(text) match {
            case Some(m) => m.group(1)
            case None => "0"
          }
          case _ => "0"
        }
        val axleLoad140 = row.getAs[String]("axleLoad140") match {
          case null => ""
          case _ => row.getAs[String]("axleLoad140")
        }
        val axleBrand = row.getAs[String]("axleBrand") match {
          case null => ""
          case _ => row.getAs[String]("axleBrand").trim
        }
        val priceMark = row.getAs[String]("priceMark") match {
          case null => ""
          case _ => row.getAs[String]("priceMark").trim
        }
        val spectacle = ""
        ProductInfo(id,name,brandId,seriesId,subCategoryId, enable, tonnageType, cateId, isSell, publishTime, price, drivingForm, horsePower,containerLength, containerForm, totalMass,fuelType, energyType, forwardGear, engineBrand, emissionStandard, seatNum, marketSeg, batteryLife, displacement, containerBrand, torque, ratedSpeed, gearBrand, axleDesc, axleRatio, axleLoad, vmass,maxSpeed,vwidth,ratedLoad,shiftForm,peakPower,wheelbase,vlength,chassisSeries,cabCapacity,longBeamHeight,axleLoad140,axleBrand,priceMark,spectacle)
      }).createTempView("product_info_view")
      val productDF = spark.sql("select id,name,seriesId,brandId, cateId,subCategoryId, enable, isSell, publishTime, tonnageType from product_info_view")
      sqlBase.executeMany2(
        s"""
           |REPLACE INTO $productTable
           |(id, name, seriesId, brandId, cateId, subCategoryId, enable, isSell, publishTime, tonnageType)
           |VALUES (?,?,?,?,?,?,?,?,?,?)
           |""".stripMargin, productDF.collect())
        val truckAttributeDF=spark.sql(
        s"""
           |select id,tonnageType,cateId,subCategoryId,price,drivingForm,horsePower,containerLength,containerForm,totalMass,fuelType,forwardGear,enable,emissionStandard,engineBrand,isSell,seatNum,marketSeg,batteryLife,displacement,containerBrand,torque,ratedSpeed,gearBrand,axleDesc,axleRatio,axleLoad,vmass,maxSpeed,vwidth,ratedLoad,shiftForm,peakPower,wheelbase,vlength,chassisSeries,cabCapacity,longBeamHeight,axleLoad140,axleBrand,energyType,priceMark,spectacle,name,seriesId,brandId,publishTime from product_info_view
           |""".stripMargin)
      sqlBase.executeMany2(
        s"""
           |REPLACE INTO $truckAttributeTable
           |(f_product_id, f_tonnage_type, f_cate_id, f_subcate_id, f_price, f_driving_form, f_horsepower, f_container_length, f_container_form, f_total_mass, f_fuel_type, f_forward_gear, f_enable, f_emission_standard, f_engine_brand, f_is_sell, f_seat_num, f_market_seg, f_battery_life, f_displacement, f_container_brand, f_torque, f_rated_speed, f_gear_brand, f_axle_desc, f_axle_ratio, f_axle_load, f_vmass, f_max_speed, f_vwidth, f_rated_load, f_shift_form, f_peak_power, f_wheelbase, f_vlength, f_chassis_series, f_cab_capacity, f_long_beam_height, f_axle_load_140, f_axle_brand, f_energy_type, f_price_mark, f_scenario, f_product_name, f_series_id, f_brand_id, f_publish_time)
           |VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);
           |
           |""".stripMargin, truckAttributeDF.collect())
//      sqlBase.execute(s"update t_check set flag=1, message='$day:tao_400 运行成功' where name='aliyun_tao_400'")
    } catch {
      case e: Exception => {
        e.printStackTrace()
        try {
          val exec = new MySqlBaseDao()
          exec.execute(s"update t_check set flag=2, message='$day:tao_400 运行失败' where name='aliyun_tao_400'")
        } catch {
          case e: Throwable => {
            e.printStackTrace()
          }
        }
      }
    }
  }

  def productSeries(spark: SparkSession, sqlBase: MySqlBaseDao, day: String): Unit = {
    val resultMysqlTable = "db_userportrait_test.t_series"
    val resultDF = spark.sql(
      s"""
         |select c0 as id,c1 as name,c2 as enable,ROW_NUMBER() OVER(partition by c0 order by `timestamp` desc) as rank from
         |(select json_tuple(data, 'id', 'name', 'enable'),`timestamp`
         |from t_biz_data_temp where billType='product_series') as t
         |""".stripMargin).filter("rank=1").drop("rank")
    sqlBase.executeMany2(
      s"""
         |REPLACE INTO $resultMysqlTable (id, name,enable)
         |VALUES (?,?,?)
         |""".stripMargin, resultDF.collect())
  }

  def productBrand(spark: SparkSession, sqlBase: MySqlBaseDao, day: String): Unit = {
    val resultMysqlTable = "db_userportrait_test.t_brand"
    val resultDF = spark.sql(
      s"""
         |select c0 as id,c1 as name,c2 as enable,ROW_NUMBER() OVER(partition by c0 order by `timestamp` desc) as rank from
         |(select json_tuple(data, 'id', 'name', 'enable'),`timestamp`
         |from t_biz_data_temp where billType='product_brand') as t
         |""".stripMargin).filter("rank=1").drop("rank")
    sqlBase.executeMany2(
      s"""
         |REPLACE INTO $resultMysqlTable (id, name,enable)
         |VALUES (?,?,?)
         |""".stripMargin, resultDF.collect())
  }

  def productSubcate(spark: SparkSession, sqlBase: MySqlBaseDao, day: String): Unit = {
    val resultMysqlTable = "db_userportrait_test.t_subcategory"
    val resultDF = spark.sql(
      s"""
         |select c0 as id,c1 as name,c2 as enable,ROW_NUMBER() OVER(partition by c0 order by `timestamp` desc) as rank from
         |(select json_tuple(data, 'id', 'name', 'enable'),`timestamp`
         |from t_biz_data_temp where billType='product_subcate') as t
         |""".stripMargin).filter("rank=1").drop("rank")
    sqlBase.executeMany2(
      s"""
         |REPLACE INTO $resultMysqlTable (id, name,enable)
         |VALUES (?,?,?)
         |""".stripMargin, resultDF.collect())
  }

  def productSeriesTonnage(spark: SparkSession, sqlBase: MySqlBaseDao, day: String): Unit = {
    val resultMysqlTable = "db_userportrait_test.t_series_tonnage"
    val resultDF = spark.sql(
      s"""
         |select c0 as seriesId,c1 as tonnageType,ROW_NUMBER() OVER(partition by c0 order by `timestamp` desc) as rank from
         |(select json_tuple(data, 'seriesId', 'tonnageType'),`timestamp`
         |from t_biz_data_temp where billType='product_series_tonnage') as t
         |""".stripMargin).filter("rank=1").drop("rank")
    sqlBase.executeMany2(
      s"""
         |REPLACE INTO $resultMysqlTable (seriesId, tonnageType)
         |VALUES (?,?)
         |""".stripMargin, resultDF.collect())
  }

  def productBrandSeriesSubcate(spark: SparkSession, sqlBase: MySqlBaseDao, day: String): Unit = {
    val resultMysqlTable = "db_userportrait_test.t_brand_series_subcate"
    val resultDF = spark.sql(
      s"""
         |select c0 as seriesId,c1 as brandId,c2 as subcategoryId,c3 as enable,ROW_NUMBER() OVER(partition by c0,c2 order by `timestamp` desc) as rank from
         |(select json_tuple(data, 'seriesId', 'brandId','subcategoryId','enable'),`timestamp`
         |from t_biz_data_temp where billType='product_brand_series_subcate') as t
         |""".stripMargin).filter("rank=1").drop("rank")
    sqlBase.executeMany2(
      s"""
         |REPLACE INTO $resultMysqlTable (seriesId, brandId,subcategoryId,enable)
         |VALUES (?,?,?,?)
         |""".stripMargin, resultDF.collect())
  }

  def pullDealerArticle(spark: SparkSession, sqlBase: MySqlBaseDao): Unit = {
    val resultMysqlTable = "db_userportrait.t_dealer_article"
    //获取经销商文章数据
    val resultDF = spark.sql(
      s"""
         |select c0 as id,nvl(c1,'') as title,nvl(c2,'') as create_time,nvl(c3,'') as publish_time,nvl(c4,'') as start_time,nvl(c5,'') as end_time,nvl(c6,'') as relation_id,nvl(c7,0) as articletype_id,nvl(c8,'') as articletype,nvl(c9,'') as shopid,nvl(c10,'') as dealerid,nvl(c10,0) as province_code,nvl(c12,0) as city_code,nvl(c13,'') as province,nvl(c14,'') as city,if(operation_state='delete',1,0) as is_del,timestamp,from_unixtime(cast(substr(`timestamp`,1,10) as int),'yyyyMMdd') as day,c15 as relation_products,ROW_NUMBER() over(partition by c0 order by timestamp desc) as rank  from
         |(select json_tuple(data,'articleId','title','createTime','publishTime','startTime','endTime','relationId','articleTypeId','articleType','shopID','dealerId','provinceCode','cityCode','province','city','relationProducts'),`timestamp`,billType,operation_state
         |from t_biz_data_temp where billType ='dealer_article') as t
         |""".stripMargin).filter("rank=1").drop("rank").collect()

    sqlBase.executeMany2(
      s"""
         |REPLACE INTO $resultMysqlTable
         |(id, title, create_time, publish_time, start_time, end_time, relation_id, articletype_id, articletype, shopid, dealerid, province_code, city_code, province, city, is_del, `timestamp`,day,relation_products)
         |VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)
         |""".stripMargin, resultDF)
  }

  def pullDealerMarketCoupons(spark: SparkSession, sqlBase: MySqlBaseDao): Unit = {
    val resultMysqlTable = "db_userportrait.t_dealer_market_coupons"
    val resultDF = spark.sql(
      """
        |select c0 as id,c1 as coupons_number,c2 create_time,c3 as publish_time,
        | c4 as start_time,c5 as end_time,c6 as is_active,c7 as relation_id,c8 as ralation_products,c9 as shop_id,c10 as dealer_id,c11 as province_code,c12 as city_code,c13 as province,c14 as city , ROW_NUMBER() OVER(partition by c0 order by `timestamp` desc) as rank from
        |(select json_tuple(data, 'id', 'couponsNumber','createTime','publishTime','startTime','endTime','isActive','relationId','relationProducts','shopID','dealerId','provinceCode','cityCode','province','city'),`timestamp`
        |from t_biz_data_temp where billType='dealer_market_coupons') as t
        |""".stripMargin).filter("rank=1").drop("rank")
    sqlBase.executeMany2(
      s"""
        |REPLACE INTO $resultMysqlTable
        |(id, coupons_number, create_time, publish_time, start_time, end_time, is_active, relation_id, ralation_products, shop_id, dealer_id, province_code, city_code, province, city)
        |VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        |""".stripMargin, resultDF.collect())
  }

}
