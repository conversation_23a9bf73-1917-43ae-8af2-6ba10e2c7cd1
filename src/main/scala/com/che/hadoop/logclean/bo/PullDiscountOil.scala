package com.che.hadoop.logclean.bo

import java.util.ResourceBundle

import com.che.hadoop.underlay.dao
import com.che.hadoop.underlay.dao.{MySparkSession, MySqlBaseDao}
import com.che.hadoop.logclean.utils.{DingRobot, UrlDecodeUtil}
import org.apache.spark.sql.SparkSession

/**
 * 拉取优惠加油数据
 * 20221202日以后没有数据（20231203）
 */
object PullDiscountOil {
  def main(args: Array[String]): Unit = {
    val spark = MySparkSession.conn("Logclean_PullBusinessData")
    saveData(spark,"20210610")

  }

  def saveData(spark:SparkSession,day: String): Unit = {
    val oilTable = "t_discount_oil"
    val sqlBase = new MySqlBaseDao()
    val t_check_pre="aliyun_"
    spark.sparkContext.setLogLevel("ERROR")
    try {
      //优惠加油
      spark.sql("select json_tuple(data, 'id', 'orderId', 'phone', 'province', 'city','county', 'amountPay'," +
        "'amountGun', 'amountDiscounts', 'orderStatusName', 'litre', 'orderSource','orderTime','refundTime','createTime'," +
        "'updateTime'),`timestamp`, operation_state,from_unixtime(substr(`timestamp`,0,10), 'yyyyMMdd') from t_biz_data_temp where billtype='discount_oil'")
        .toDF("f_id", "f_order_id", "f_phone", "f_province", "f_city", "f_country", "f_amount_pay", "f_amount_gun",
          "f_amount_discounts", "f_order_status_name", "f_litre", "f_order_source", "f_order_time", "f_refund_time",
          "f_create_time", "f_update_time", "timestamp", "operationState", "day")
        .createOrReplaceTempView("t_discount_oil_temp")
      //避免插入重复数据报错
      val f_ids=sqlBase.getDfTable("(select f_id from t_discount_oil)t",spark)
        .collect()
        .map(row=>row.getAs[Int]("f_id"))
        .mkString("(",",",")")
      println(f_ids)
      //插入数据
      val insertDF = spark.sql("select f_id, f_order_id, f_phone, f_province, f_city,f_country, f_amount_pay," +
        "f_amount_gun,f_amount_discounts, f_order_status_name,f_litre, f_order_source, f_order_time, f_refund_time," +
        "f_create_time, f_update_time, day as f_date from t_discount_oil_temp where operationState='insert' and f_id>0")
        .filter(s"f_id not in ${f_ids}")
//    insertDF.foreach(f=>println(f))
      sqlBase.saveMysql(insertDF, oilTable)
      //更新数据
      spark.sql("select * from t_discount_oil_temp where operationState='update' and f_id>0").collect()
        .foreach(f => {
          val args = Array(f.getAs[String]("f_order_id"), f.getAs("f_phone"),
            f.getAs("f_province"), f.getAs("f_city"), f.getAs("f_country"),
            f.getAs("f_amount_pay"), f.getAs("f_amount_gun"), f.getAs("f_amount_discounts"),
            f.getAs("f_order_status_name"), f.getAs("f_litre"), f.getAs("f_order_source"),
            f.getAs("f_order_time"), f.getAs("f_refund_time"),
            f.getAs("f_create_time"), f.getAs("f_update_time"),
            f.getAs("day"), f.getAs[Int]("f_id"))
          sqlBase.executeInsertOrUpdate(s"UPDATE t_discount_oil SET f_order_id = ?, f_phone = ?, f_province = ?, f_city = ?," +
            s" f_country = ?, f_amount_pay = ?, f_amount_gun = ?, f_amount_discounts = ?, f_order_status_name = ?, f_litre = ?, " +
            s"f_order_source = ?, f_order_time = ?, f_refund_time = ?, f_create_time = ?, " +
            s"f_update_time = ?, f_date = ? WHERE f_id = ?", args)
        })
      sqlBase.executeInsertOrUpdate(s"update t_check set flag=1 , message='$day:运行成功' where name='${t_check_pre}t_discount_oil'",null)
    } catch {
      case e: Exception => {
        e.printStackTrace()
        try{
          val exec = new MySqlBaseDao()

          exec.executeInsertOrUpdate(s"update t_check set flag=2, message='$day:${t_check_pre}t_discount_oil 运行失败' where name='${t_check_pre}t_discount_oil'",null)
        }catch {
          case e:Throwable=>{
            e.printStackTrace()
          }
        }

      }

    }

  }

}
