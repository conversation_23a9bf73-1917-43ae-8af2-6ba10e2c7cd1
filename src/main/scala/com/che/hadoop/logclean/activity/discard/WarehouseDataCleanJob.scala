package com.che.hadoop.logclean.activity.discard

import com.che.hadoop.logclean.bo.LogCleanApp
import com.che.hadoop.logclean.utils.Common

/**
 * 数据库表维护，删除过期表数据
 * 没有定时运行
 */
object WarehouseDataCleanJob {
  def main(args: Array[String]): Unit = {
//    com.che.hadoop.logclean.bo.WarehouseDataClean.cleanMysqlTableData()
    val day = Common.getDate(1)
    //维护kudu过期数据
    LogCleanApp.kuduTableMaintain(day)
  }
}
