package com.che.hadoop.logclean.bo

import com.che.hadoop.underlay.dao.{MySparkSession, MySqlBaseDao}
import com.che.hadoop.logclean.utils.Common
import org.apache.spark.sql.SparkSession

/**
 * 从日志中清洗文章明细数据
 * 定时任务中
 */
object LogCleanInformation {

  def main(args: Array[String]): Unit = {
    val spark=MySparkSession.conn()
    Common.getBetweenDates("20240814","20240820").foreach(day=>{
      dwdInformationLog(spark,day)
    })
    spark.stop()
  }

  /**
   * 从日志钟清洗文章明细数据
   * @param day
   */
  def dwdInformationLog(spark:SparkSession,day:String): Unit ={
    val sqlBase=new MySqlBaseDao()
    try {
      //把code等于0的单独拿出来然后进行判断
      //todo 增加经验文章和经验视频的正则
      spark.sql(
        s"""
           |select distinct vid ,day,'pageview' as eventid,daytime,ip,uid,sid,i,vtc,vtl,
           |CASE
           |					    WHEN lower(k) rlike 'articleid=([0-9]+)' THEN regexp_extract(LOWER(k) , 'articleid=([0-9]+)', 1)
           |					    WHEN lower(k) rlike 'videoid=([0-9]+)' THEN regexp_extract(LOWER(k), 'videoid=([0-9]+)', 1)
           |              WHEN lower(k) rlike 'liveid=([0-9]+)' THEN regexp_extract(LOWER(k), 'liveid=([0-9]+)', 1)
           |              WHEN lower(k) rlike '(www|m).360che.com/(news|market|driver|tech|law)/[0-9]+/([0-9]+)' THEN regexp_extract(LOWER(k), '(www|m).360che.com/(news|market|driver|tech|law)/[0-9]+/([0-9]+)', 3)
           |					    WHEN lower(k) rlike '(www|m).360che.com/v/v_([0-9]+)' THEN regexp_extract(LOWER(k), '(www|m).360che.com/v/v_([0-9]+)', 2)
           |					    WHEN lower(k) rlike 'tu.360che.com/(news|m)/([0-9]+)' THEN regexp_extract(LOWER(k), 'tu.360che.com/(news|m)/([0-9]+)', 2)
           |					    WHEN lower(k) rlike 'app.360che.com/share_picture/([0-9]+)' THEN regexp_extract(LOWER(k), 'app.360che.com/share_picture/([0-9]+)', 1)
           |					    WHEN lower(k) rlike '(www|m).360che.com/v/v_([0-9]+)' THEN regexp_extract(LOWER(k), '(www|m).360che.com/v/v_([0-9]+)', 2)
           |					    WHEN lower(k) rlike 'https://m.360che.com/wx-share/(article|video)' THEN regexp_extract(LOWER(k), 'https://m.360che.com/wx-share/(article|video).*html\\\\?id=([0-9]+)', 2)
           |              WHEN lower(k) rlike 'id=([0-9]+)' THEN regexp_extract(LOWER(k) , 'id=([0-9]+)', 1)
           |ELSE -1 end as `label`,
           |case when  k rlike '(http|https)://((www.360che.com/(news|market|driver|tech|law)/[0-9]+/[0-9]+)|(m.360che.com/(news|market|driver|tech|law)/[0-9]+/[0-9]+).html|(m.360che.com/weixin/(article|SpeedArticle).aspx+)|(app.360che.com/app/ArticleNew/Article.aspx+)|(news-app.360che.com/article.html+)|(m.360che.com/wx-share/article))'
           |		then 1
           |	when k rlike '(http|https)://(((www.360che.com/v/v_[0-9]+)|(m.360che.com/v/v_[0-9]+)).html|(m.360che.com/appvideo/(VideoInfo|VideoShare|v/info|quickshare).aspx+)|(news-app.360che.com/video.html+)|(m.360che.com/wx-share/video))'
           |	    then 2
           |	when  k rlike '(http|https)://(((tu.360che.com/news/[0-9]+)|(tu.360che.com/m/[0-9]+)|(app.360che.com/share_picture/[0-9]+)).html|(api.360che.com/ImgChannel/app/ImageInfo.aspx+))'
           |		then 3
           |  when k rlike 'https?://live-h5.360che.com'
           |    then 4
           |  when k rlike '(news-app.360che.com/new/experience/article.html|s.kcimg.cn/landing/experience/detail.html)\\\\?id=[0-9]+&articleId=[0-9]+'
           |    then 5
           |  when k rlike 's.kcimg.cn/landing/experience/detail.html\\\\?id=[0-9]+&videoId=[0-9]+'
           |    then 6
           |end as typeid,
           |case when ua like '%Windows%' or ua like '%Macintosh%' then 11
           |       when ua like '%MicroMessenger%' then 15
           |       when ua not like '%MicroMessenger%' and ua not like '%360CHE%' and ua not like '%Windows%' and ua not like '%Macintosh%' then 12
           |       when ua like '%iPhone%' and ua like '%360CHE%'then 13
           |       when ua like '%Android%' and ua like '%360CHE%' then 14
           |	end as client
           |from t_logdata
           |where day=$day and ip <> '************'
           |-- and code=0
           |and  eventid ='' and
           |( k rlike '(http|https)://((www.360che.com/(news|market|driver|tech|law)/[0-9]+/[0-9]+)|(m.360che.com/(news|market|driver|tech|law)/[0-9]+/[0-9]+).html|(m.360che.com/weixin/(article|SpeedArticle).aspx+)|(app.360che.com/app/ArticleNew/Article.aspx+)|(news-app.360che.com/article.html+))'
           |or k rlike '(http|https)://(((www.360che.com/v/v_[0-9]+)|(m.360che.com/v/v_[0-9]+)).html|(m.360che.com/appvideo/(VideoInfo|VideoShare|v/info|quickshare).aspx+)|(news-app.360che.com/video.html+))'
           |or k rlike '(http|https)://(((tu.360che.com/news/[0-9]+)|(tu.360che.com/m/[0-9]+)|(app.360che.com/share_picture/[0-9]+)).html|(api.360che.com/ImgChannel/app/ImageInfo.aspx+))'
           |or k rlike 'https?://live-h5.360che.com'
           |or k rlike 'https?://s.kcimg.cn/landing/experience/detail.html\\\\?id=[0-9]+&(articleId|videoId)=[0-9]+'
           |or k rlike 'https?://news-app.360che.com/new/experience/article.html\\\\?id=[0-9]+&articleId=[0-9]+'
           |or k rlike 'https://m.360che.com/wx-share/(article|video)'
           |)
           |""".stripMargin).createOrReplaceTempView("t_tmp_code0")
//      //2021年5月以前
//      spark.sql(
//        s"""
//           |select distinct vid ,day,'' as eventid,daytime,ip,uid,sid,
//           |case when split(regexp_replace(regexp_extract(k,'(id=[0-9]+)|(Id=[0-9]+)|((?<=(news|market|driver|tech|law|share_picture|tu.360che.com/m)/).+(?=\\.html))|(v_[0-9]+)|(articleid=[0-9]+)',0),('/|_'),'='),'=')[1]!=''
//           |            then split(regexp_replace(regexp_extract(k,'(id=[0-9]+)|(Id=[0-9]+)|((?<=(news|market|driver|tech|law|share_picture|tu.360che.com/m)/).+(?=\\.html))|(v_[0-9]+)|(articleid=[0-9]+)',0),('/|_'),'='),'=')[1]
//           |     when split(regexp_replace(regexp_extract(k,'(id=[0-9]+)|(Id=[0-9]+)|((?<=(news|market|driver|tech|law|share_picture|tu.360che.com/m)/).+(?=\\.html))|(v_[0-9]+)|(articleid=[0-9]+)',0),('/|_'),'='),'=')[0]!=''
//           |            then split(regexp_replace(regexp_extract(k,'(id=[0-9]+)|(Id=[0-9]+)|((?<=(news|market|driver|tech|law|share_picture|tu.360che.com/m)/).+(?=\\.html))|(v_[0-9]+)|(articleid=[0-9]+)',0),('/|_'),'='),'=')[0]
//           |     else '-1'
//           |     end as label,
//           |case when code in ('11102100110000','12103100110000','15107100110000')
//           |		then 1
//           |	when code in ('11102100210000','12103100210000','15107100210000')
//           |	    then 2
//           |	when code in ('11102100310000','12103100310000','15107100310000')
//           |		then 3
//           |    end as typeid,
//           |case when code rlike '^11' then 11
//           |	when code rlike '^12' then 12
//           |	when code rlike '^15' then 15
//           |	end as client
//           |from t_logdata
//           |where day=$day
//           |and code in ('11102100110000','11102100210000','11102100310000','12103100110000','12103100210000','12103100310000','15107100110000',
//           |'15107100210000','15107100310000') and eventid =''
//           |""".stripMargin).createOrReplaceTempView("t_tmp_web")
//      spark.sql(
//        s"""
//           |select eventid ,lable as label,cid as vid ,
//           |case when eventid ='CHE_00000056' then 1
//           |     when eventid ='CHE_00000058' then 2
//           |     when eventid ='CHE_00000057' then 3 end as typeid,day ,
//           |case when cs ="android" then 14
//           |	 when cs ="iOS" then 13 end as client
//           |from t_logdata_app
//           |where day =$day
//           |and  lable!=''and lable rlike '^[0-9]*$$'
//           |and eventid in ('CHE_00000056','CHE_00000058','CHE_00000057')
//           |""".stripMargin).createOrReplaceTempView("t_tmp_app1")
//      spark.sql(
//        s"""
//           |select eventid ,lable as label,cid as vid ,
//           |case when eventid ='CHE_00000063' then 1
//           |     when eventid ='CHE_00000064' then 2
//           |     when eventid ='CHE_00000065' then 3 end as typeid,day ,
//           |case when cs ="android" then 14
//           |	 when cs ="iOS" then 13 end as client
//           |from t_logdata_app
//           |where day =$day
//           |and eventid in ('CHE_00000063','CHE_00000064','CHE_00000065')
//           |and t = 'event'
//           |and  lable!=''and lable rlike '^[0-9]*$$'
//           |""".stripMargin).createOrReplaceTempView("t_tmp_app2")
//      spark.sql(
//        s"""
//           |select eventid ,lable as label,cid as vid,
//           |case when eventid ='CHES_00000010' then 1
//           |     when eventid ='CHES_00000011' then 2
//           |     when eventid ='CHES_00000012' then 3 end as typeid,day ,
//           |case when cs ="android" then 14
//           |	 when cs ="iOS" then 13 end as client
//           |from t_logdata_app
//           |where day =$day
//           |and eventid in ('CHES_00000010','CHES_00000011','CHES_00000012')
//           |and t = 'event'
//           |and  lable!=''and lable rlike '^[0-9]*$$'
//           |""".stripMargin)createOrReplaceTempView("t_tmp_app3")
//
//      val saveDF = spark.sql(
//        """
//          |-- web,pc,m,wx 文章和视频
//          |select eventid,label,vid,typeid,client,day from t_tmp_web
//          |union all
//          |-- 标准版 app   文章和视频
//          |select eventid,label,vid,typeid,client,day from t_tmp_app1
//          |union all
//          |-- 标准版 app   文章和视频
//          |select eventid,label,vid,typeid,client,day from t_tmp_app2
//          |union all
//          |-- 极速版 app   文章和视频
//          |select eventid,label,vid,typeid,client,day from t_tmp_app3
//          |union all
//          |-- code等于0 的
//          |select eventid,label,vid,typeid,client,day from t_tmp_code0
//          |""".stripMargin).cache()


      //2021年5月以后
//      spark.sql(
//        s"""
//           |select distinct vid  ,day,'pageview' as eventid,daytime,ip,uid,sid,i,vtc,vtl,
//           |case when split(regexp_replace(regexp_extract(k,'(id=[0-9]+)|(Id=[0-9]+)|((?<=(news|market|driver|tech|law|share_picture|tu.360che.com/m)/).+(?=\\.html))|(v_[0-9]+)|(articleid=[0-9]+)',0),('/|_'),'='),'=')[1]!=''
//           |            then split(regexp_replace(regexp_extract(k,'(id=[0-9]+)|(Id=[0-9]+)|((?<=(news|market|driver|tech|law|share_picture|tu.360che.com/m)/).+(?=\\.html))|(v_[0-9]+)|(articleid=[0-9]+)',0),('/|_'),'='),'=')[1]
//           |     when split(regexp_replace(regexp_extract(k,'(id=[0-9]+)|(Id=[0-9]+)|((?<=(news|market|driver|tech|law|share_picture|tu.360che.com/m)/).+(?=\\.html))|(v_[0-9]+)|(articleid=[0-9]+)',0),('/|_'),'='),'=')[0]!=''
//           |            then split(regexp_replace(regexp_extract(k,'(id=[0-9]+)|(Id=[0-9]+)|((?<=(news|market|driver|tech|law|share_picture|tu.360che.com/m)/).+(?=\\.html))|(v_[0-9]+)|(articleid=[0-9]+)',0),('/|_'),'='),'=')[0]
//           |     when regexp_extract(k,'liveId=([0-9]+)',1) != ''
//           |            then regexp_extract(k,'liveId=([0-9]+)',1)
//           |     else '-1'
//           |     end as label,
//           |case when code in ('11102100110000','12103100110000','15107100110000','13102100110000','14102100110000')
//           |		then 1
//           |	when code in ('11102100210000','12103100210000','15107100210000')
//           |	    then 2
//           |	when code in ('11102100310000','12103100310000','15107100310000','13102100310000','14102100310000')
//           |		then 3
//           |  when code in ('11108100010000','12117100010000','15119100010000')
//           |     then 4
//           |    end as typeid,
//           |case when code rlike '^11' then 11
//           |	when code rlike '^12' then 12
//           |	when code rlike '^13' then 13
//           |	when code rlike '^14' then 14
//           |	when code rlike '^15' then 15
//           |	end as client
//           |from t_logdata
//           |where day='$day' and ip <> '************'
//           |and code in ('11102100110000','11102100210000','11102100310000','12103100110000','12103100210000','12103100310000','15107100110000',
//           |'15107100210000','15107100310000','13102100110000','13102100310000','14102100110000','14102100310000','11108100010000','12117100010000','15119100010000') and eventid =''
//           |""".stripMargin).createOrReplaceTempView("t_tmp_web1")
//
//      spark.sql(
//        s"""
//           |select eventid ,lable as label,cid as vid , 2 as typeid,day ,
//           |case when cs ="android" then 14
//           |	 when cs ="iOS" then 13 end as client
//           |from t_logdata_app
//           |where day =$day
//           |and eventid ='CHES_00000011'
//           |and t = 'event'
//           |and  lable!=''and lable rlike '^[0-9]*$$'
//           |""".stripMargin).createOrReplaceTempView("t_tmp_appVideo")
      //视频数据切换到神策统计20240116
      spark.sql(
        s"""
           |select 'pageview'  as eventid ,label,cid as vid , 2 as typeid,day ,
           |case when os ="iOS" then 13 else 14 end as client
           |from t_ods_appsc_log
           |where day ='$day'
           |and event='AppViewScreen'
           |and title='视频详情页'
           |and label rlike '^[0-9]+$$'
           |union
           |select 'pageview'  as eventid ,regexp_extract(label,'[0-9]+\\\\|([0-9]+)',1) as label,cid as vid ,6 as typeid,day ,
           |case when os ="iOS" then 13 else 14 end as client
           |from t_ods_appsc_log
           |where day ='$day'
           |and event='AppViewScreen'
           |and title='经验视频详情页'
           |and type_id ="27"
           |and label !=''and label rlike '[0-9]+\\\\|([0-9]+)'
           |""".stripMargin).createOrReplaceTempView("t_tmp_appVideo")
      //计算直播的pv
      spark.sql(
        s"""
           |select 'pageview' as eventid,label,cid as vid,4 as typeid,day,
           |case when os ="iOS" then 13
           |	 else 14 end as client
           |from t_ods_appsc_log where day =$day and title ="直播详情页" and event ="AppViewScreen"
           |and type_id ="13" and label rlike'^[0-9]*$$' and label !=''
           |
           |""".stripMargin).createOrReplaceTempView("t_appsc_zhibo")
      //微信小程序文章和视频数据
      spark.sql(
        s"""
          |select 'pageview' as eventid,label,anonymous_id as vid, case type_id when '2' then 1 else 2 end as typeid,'16' as client,day
          |from t_ods_miniprogram_log where day='$day' and event='on_mpclick' and eventid='v_page' and type_id in ('5', '2') and label rlike '\\\\d+'
          |union all
          |SELECT 'pageview' as eventid,regexp_extract(LOWER(url_query) , '(?:articleid|videoid)=([1-9][0-9]*)', 1) as label,anonymous_id as vid, if(LOWER(url_query) rlike 'articleid',2,5) as typeid,'16' as client,day
          |from t_ods_miniprogram_log where day='$day' and event='MPViewScreen' and LOWER(url_path) rlike 'pages/experience/detail/(articledetail|videodetail)'
          |and LOWER(url_query) rlike '(articleid|videoid)=([1-9][0-9]*)'
          |""".stripMargin).createOrReplaceTempView("t_tmp_wx")
      val saveDF2 =spark.sql(
        """
          |select eventid,label,vid,typeid,client,day from
          |(
          |-- web pc,m,wx的文章和视频、直播，app的文章
          |-- select eventid,label,vid,typeid,client,day from t_tmp_web1
          |-- union all
          |-- app的视频
          |select eventid,label,vid,typeid,client,day from t_tmp_appVideo
          |union all
          |-- code等于0的
          |select eventid,label,vid,typeid,client,day from t_tmp_code0
          |union all
          |-- app的直播
          |select eventid,label,vid,typeid,client,day from t_appsc_zhibo
          |union all
          |-- 微信小程序
          |select eventid,label,vid,typeid,client,day from t_tmp_wx) as t where label rlike '^[1-9][0-9]*$'
          |""".stripMargin)
      spark.sql(s"ALTER TABLE t_dwd_information_log DROP IF EXISTS PARTITION(day=$day)")
      saveDF2.repartition(1).write.mode("append").format("Hive").partitionBy("day").saveAsTable("t_dwd_information_log")
      sqlBase.executeInsertOrUpdate(s"update t_check set flag=1, message='$day:运行成功' where name='aliyun_t_dwd_information_log'",null)
    } catch {
      case e: Exception => {
        e.printStackTrace()
        try{
          sqlBase.executeInsertOrUpdate(s"update t_check set flag=2, message='$day:aliyun_t_dwd_information_log 运行失败' where name='aliyun_t_dwd_information_log'",null)
        }catch {
          case e:Throwable=>{
            e.printStackTrace()
          }
        }
      }
    }
  }

}
