package com.che.hadoop.logclean.dao

import java.sql.{Connection, DriverManager}
import java.util.{LinkedList, ResourceBundle}

/**
 * mysql数据库连接池，允许指定连接信息，并设定默认连接
 * @param connectionName 配置文件中连接名称
 */
class DBConnectionPool(connectionName:String="che") {

  private val reader = ResourceBundle.getBundle("connection")
  private val max_connection = reader.getString(s"$connectionName.max_connection") //连接池总数
  private val connection_num = reader.getString(s"$connectionName.connection_num") //产生连接数
  private var current_num = 0 //当前连接池已产生的连接数
  private val pools = new LinkedList[Connection]() //连接池
  private val driver = reader.getString(s"$connectionName.driver")
  val url = reader.getString(s"$connectionName.url")
  val username = reader.getString(s"$connectionName.username")
  val password = reader.getString(s"$connectionName.password")

  /**
    * 加载驱动
    */
  private def before() {
    if (current_num > max_connection.toInt && pools.isEmpty()) {
      print("busyness")
      Thread.sleep(2000)
      before()
    } else {
      Class.forName(driver)
    }
  }

  /**
    * 获得连接
    */
  private def initConn(): Connection = {
    val conn = DriverManager.getConnection(url, username, password)
    conn
  }

  /**
    * 初始化连接池
    */
  private def initConnectionPool(): LinkedList[Connection] = {
    AnyRef.synchronized({
      if (pools.isEmpty()) {
        before()
        for (i <- 1 to connection_num.toInt) {
          pools.push(initConn())
          current_num += 1
        }
      }
      pools
    })
  }

  /**
    * 获得连接
    */
  def getConn():Connection={
    initConnectionPool()
    pools.poll()
  }

  /**
    * 释放连接
    */
  def releaseCon(con:Connection){
    pools.push(con)
  }

  }
