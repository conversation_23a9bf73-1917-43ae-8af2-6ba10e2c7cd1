#LogClean_UserMapStreamingJob
type=command
command=spark-submit --master yarn \
--deploy-mode cluster \
--name LogClean_UserMapStreamingJob \
--num-executors 1 \
--executor-cores 1 \
--executor-memory 1g \
--driver-memory 1g \
--queue streaming \
--jars 360che.hadoop.underlay-1.0.jar \
--class com.che.hadoop.logclean.activity.UserMapStreamingJob \
360che.hadoop.logclean-1.0.jar
retries=12
#单位毫秒
retry.backoff=300000