package com.che.hadoop.logclean.bo

import com.che.hadoop.underlay.dao.{MySparkSession, MySqlBaseDao}
import org.apache.spark.sql.SparkSession

/**
 * 清洗文章视频评论数据
 */
object PullCommentData {

  def main(args: Array[String]): Unit = {
    val spark=MySparkSession.conn()
    saveOdsData(spark,"20210327")
    saveDwdData(spark,"20210327")

  }

  /**
   * 清洗文章视频评论数据
   * @param spark SparkSession
   * @param day 日期
   */
  def saveOdsData(spark:SparkSession,day: String): Unit = {
    val t_check_pre="aliyun_"
    try{
      //评论
      val t = spark.sql(s"select json_tuple(data, 'id', 'topicId', 'userId', 'nikeName', 'viewTime', 'content', 'parentId', 'replyId', " +
        s"'toUserId', 'toNikeName', 'isDelete', 'viewTerminal', 'siteId'),`timestamp`, operation_state,'$day' as day  from t_biz_data_temp where billtype='comment'")
        .toDF("id", "topicId", "userId", "nikeName", "viewTime", "content", "parentId", "replyId", "toUserId", "toNikeName",
          "isDelete", "viewTerminal", "siteId", "timestamp", "operationState", "day")
      spark.sql(s"alter table t_comment drop if exists partition(day='$day')")
      t.dropDuplicates().write.mode("append").partitionBy("day").format("hive")
        .saveAsTable("t_comment")
      val exec = new MySqlBaseDao()
      exec.executeInsertOrUpdate(s"update t_check set flag=1 , message='$day:运行成功' where name='${t_check_pre}t_comment'",null)
    }catch{
      case e:Exception=>{
        e.printStackTrace()
        try{
          val exec = new MySqlBaseDao()

          exec.executeInsertOrUpdate(s"update t_check set flag=2, message='$day:${t_check_pre}t_comment 运行失败' where name='${t_check_pre}t_comment'",null)
        }catch {
          case e:Throwable=>{
            e.printStackTrace()
          }
        }
      }
    }
  }


  def saveDwdData(spark:SparkSession,day:String): Unit = {
    val t_check_pre="aliyun_"
    try {
      val commentTable = "t_dwd_comment"
      spark.sql("set spark.sql.shuffle.partitions=1")
      //求助回答数据
      spark.sql(
        s"""
           |INSERT OVERWRITE table $commentTable
           | SELECT
           |   nvl(ot.id,t.id) as id,nvl(ot.topicid,t.topicid) as topicid,
           |   nvl(ot.userid,t.userid) as userid,
           |   nvl(ot.nikename,t.nikename) as nikename,
           |   nvl(ot.viewtime,t.viewtime) as viewtime,
           |   nvl(ot.content,t.content) as content,
           |   nvl(ot.parentid,t.parentid) as parentid,
           |   nvl(ot.replyid,t.replyid) as replyid,
           |   nvl(ot.touserid,t.touserid) as touserid,
           |   nvl(ot.tonikename,t.tonikename) as tonikename,
           |   nvl(ot.isdelete,t.isdelete) as isdelete,
           |   nvl(ot.viewterminal,t.viewterminal) as viewterminal,
           |   nvl(ot.siteid,t.siteid) as siteid,
           |   nvl(ot.update_date,t.update_date) as update_date
           | from $commentTable t
           |full outer join
           | (
           | select  * from(
           | select ROW_NUMBER() over(PARTITION by id,topicid,siteid order by `timestamp` desc) as row_num,*,
           | from_unixtime(cast(`timestamp`/1000 as int),'yyyyMMdd') as update_date,
           | from_unixtime(to_unix_timestamp(viewtime, 'yyyy/MM/dd HH:mm:ss'),'yyyy-MM-dd HH:mm:ss') as create_date
           | from t_comment where day='$day') t0
           | where row_num=1
           | ) ot
           |ON t.id=ot.id and  t.topicid=ot.topicid and t.siteid=ot.siteid
           |""".stripMargin)
      val exec = new MySqlBaseDao()
      exec.executeInsertOrUpdate(s"update t_check set flag=1 , message='$day:运行成功' where name='${t_check_pre}t_dwd_comment'",null)
    } catch {
      case e: Exception => {
        e.printStackTrace()
        try {
          val exec = new MySqlBaseDao()

          exec.executeInsertOrUpdate(s"update t_check set flag=2, message='$day:${t_check_pre}t_dwd_comment 运行失败' where name='${t_check_pre}t_dwd_comment'", null)
        } catch {
          case e: Throwable => {
            e.printStackTrace()
          }
        }
      }
    }
  }


}
