package com.che.hadoop.logclean.bo.discard

import com.che.hadoop.logclean.utils.Common
import com.che.hadoop.underlay.dao
import com.che.hadoop.underlay.dao.MySqlBaseDao

object WarehouseDataClean {
  def main(args: Array[String]): Unit = {
    for(i<- 1 to 1){
      val day=Common.getDate(i)
      cleanHiveTableData()
    }

  }
  def cleanHiveTableData(day:String=Common.getDate(8)):Unit={
    //获取sparkSession
    val ss=dao.MySparkSession.conn("LogClean_HiveDataCleanJob")
    //定义删除表名
    val day_7=day
    val tableInfoMap:Map[String,Map[String,Array[String]]]=Map(
      day_7->
      Map(
        "Recommend_Product"->
          Array("t_train_product_data","t_recall_product_data_cb","t_recall_product_data_usercf","t_scan_product_with_city","t_recommend_hot_product_by_city"),
        "Recommend_ProductVid"->
          Array("t_train_product_data_vid","t_recall_product_data_vid_cb","t_recall_product_data_vid_usercf_all")
      )
    )
    tableInfoMap.foreach{f=>{ f._2.foreach{
      ff=>ff._2.map(
        tableName=>ss.sql(s"alter TABLE $tableName drop if exists partition(day='${f._1}')")
      )
    }}}
  }

  def cleanMysqlTableData(): Unit ={
    //资讯实时任务mysql表维护
    val before4day=Common.getDate(4)
    val sqlBase=new MySqlBaseDao()
    sqlBase.execute(s"delete from db_hitsstatistic.t_news_web where day<='$before4day'")
    sqlBase.execute(s"delete from db_hitsstatistic.t_news_appsc where day<='$before4day'")
  }
}
