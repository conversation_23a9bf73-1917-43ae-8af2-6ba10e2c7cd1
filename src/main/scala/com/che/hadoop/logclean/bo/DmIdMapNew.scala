package com.che.hadoop.logclean.bo

import java.util

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.serializer.SerializerFeature
import com.che.hadoop.logclean.utils.Common
import com.che.hadoop.underlay.dao.{MyKudu, MySparkSession}
import org.apache.spark.sql.SparkSession

import scala.collection.mutable.{ListBuffer, Map}

/**
 * dmid离线维护
 */
object DmIdMapNew {

  def main(args: Array[String]): Unit = {
//        dmId2Hive("20210105")
    val spark = MySparkSession.conn()
    Common.getBetweenDates("20230815","20230815").foreach(day=>{
      hive2Kudu(spark)
    })
  }




  /**
   * 根据已有的映射关系获取最新的天眼ID映射
   *
   * @param spark    SparkSession
   * @param dataList 已有的映射列表
   * @return
   */
  def getDataList(spark: SparkSession, dataList: util.ArrayList[(String, String, String, String, Int, String, Long, Int, Int, Int)]):
  (util.ArrayList[(String, String, String, String, Int, String, Long, Int, Int, Int)], ListBuffer[(String, String, String, Int, String)]) = {
    //    val dataListBc.valueBc =
    val changeData = ListBuffer[(String, String, String, Int, String)]()
    val changeDataBc = spark.sparkContext.broadcast(changeData)
    val dataListBc = spark.sparkContext.broadcast(dataList)
    val vidUidLog = spark.sql("select * from t_log_tmp").rdd.map(row => {
      val cid = row.getAs[String]("cid")
      val uid = row.getAs[String]("uid")
      val cs = row.getAs[String]("cs")
      val timestamp = row.getAs[String]("timestamp")
      val minTimestamp = row.getAs[String]("min_timestamp")
      val did = row.getAs[String]("deviceid")
      //      (ClientKey("key", timestamp.toLong), (cid, did, uid, cs, timestamp))
      (cid, did, uid, cs, timestamp,minTimestamp)
    }).sortBy(f => f._5.toLong).collect()
    vidUidLog.foreach(f => {
      //          println(f)
      val cs = f._4
      val uid = f._3
      val vid = f._1
      val did = f._2
      val time = Common.tranTimestamp(f._5).toInt
      val minTime = Common.tranTimestamp(f._6).toInt
      val timestamp = f._5.toLong
      var dmId = ""
      var dmIdUid = ""
      var isHaveDid = false
      if (did != "0" && did != null && did != "") {
        isHaveDid = true
      }
      val uidData = ListBuffer[(String, String, String, String, Int, String, Long, Int)]()
      val vidData = Map[String, Map[String, (String, String, String, String, Int, String, Long, Int)]]()
      val didData = Map[String, Map[String, (String, String, String, String, Int, String, Long, Int)]]()
      var uidVidData = Map[String, (String, String, String, String, Int, String, Long, Int)]()
      val idIndex = Map[String, Int]()
      val len = dataListBc.value.size()
      //          println(dataListBc.value)
      for (i <- 0 until len) {
        val old = dataListBc.value.get(i)
        val uidOld = old._2
        val vidType = old._5
        val id = old._1
        val dmIdOld = old._4
        val vidOld = old._3
        val timestampOld = old._7
        val firstTimeOld = old._8
        val isDel = old._10
        val csOld = old._6
        if (uid != "" && uidOld == uid) {
          //                  println(old)
          idIndex += (id -> i)
          uidData += ((id, dmIdOld, uidOld, vidOld, vidType, csOld, timestampOld, firstTimeOld))
          if (vid == vidOld && isDel == 1 && cs == csOld) {
            uidVidData += (vid -> (id, dmIdOld, uidOld, vidOld, vidType, csOld, timestampOld, firstTimeOld))
          }
          if (did == vidOld && isDel == 1 && cs == csOld) {
            uidVidData += (did -> (id, dmIdOld, uidOld, did, vidType, csOld, timestampOld, firstTimeOld))
          }
        }
        if (isDel == 0 && vidOld == vid && cs == csOld && vidType == 0) {
          //                  println(old)
          idIndex += (id -> i)
          //                  vidData = vidData :+ (id,dmIdOld,uid,vid,vidType,cs,timestampOld)
          vidData += (csOld -> Map(vid -> (id, dmIdOld, uidOld, vidOld, vidType, csOld, timestampOld, firstTimeOld)))
        }
        if (isHaveDid && isDel == 0 && vidOld == did && cs == csOld) {
          //                  println(old)
          idIndex += (id -> i)
          didData += (csOld -> Map(did -> (id, dmIdOld, uidOld, vidOld, vidType, csOld, timestampOld, firstTimeOld)))
        }
      }
      //          println(uidData)
      //          println(vidData)
      //          println(didData)
      //1、首先根据uid找天眼ID
      if (uid != "") {
        if (uidData.isEmpty) { //此uid没有关联的天眼id
          //判断是否有需要更新的关联关系
          if (vidData.isEmpty && didData.isEmpty) {
            dmId = Common.getUUID()
            dataListBc.value.add((Common.getUUID(), uid, vid, dmId, 0, cs, timestamp, minTime, time, 0))
            if (isHaveDid) {
              dataListBc.value.add((Common.getUUID(), uid, did, dmId, 1, cs, timestamp, minTime, time, 0))
            }
          } else {
            //如果当前vid对应的有别的uid则把之前的关联关系删除，并且新增一条当前登录ID的数据
            //先判断该设备id有没有数据
            if (didData.nonEmpty) {
              //id,dmIdOld,uid,vid,vidType,cs,timestampOld,firstTimeOld
              //该设备id有天眼id,并且没有对应的uid和vid
              if (didData.contains(cs) && didData(cs)(did)._3 == "" && didData(cs)(did)._7 < timestamp) {
                dmId = didData(cs)(did)._2
                val index = idIndex(didData(cs)(did)._1)
                dataListBc.value.remove(index)
                dataListBc.value.add(index, (didData(cs)(did)._1, uid, did, dmId, 1, cs, timestamp, didData(cs)(did)._8, time, 0))
                if (vidData.isEmpty) {
                  dataListBc.value.add((Common.getUUID(), uid, vid, dmId, 0, cs, timestamp, minTime, time, 0))
                }
              }
            }
            //判断vid
            if (vidData.nonEmpty) {
              dmId = vidData(cs)(vid)._2
              if (vidData(cs)(vid)._3 != "" && vidData(cs)(vid)._3 != uid) {
                if (vidData(cs)(vid)._7 < timestamp) {
                  //此vid对应的有别的uid,将之前的关联关系删除，并且重新添加关联关系
                  dataListBc.value.remove(idIndex(vidData(cs)(vid)._1))
                  dataListBc.value.add(idIndex(vidData(cs)(vid)._1), (vidData(cs)(vid)._1, vidData(cs)(vid)._3, vidData(cs)(vid)._4, dmId, 0, cs, timestamp, vidData(cs)(vid)._8, time, 1))
                  dmId = Common.getUUID()
                  dataListBc.value.add((Common.getUUID(), uid, vid, dmId, 0, cs, timestamp, minTime, time, 0))
                  if (isHaveDid && didData.contains(cs) && didData(cs)(did)._3 != uid) {
                    dataListBc.value.remove(idIndex(didData(cs)(did)._1))
                    dataListBc.value.add((didData(cs)(did)._1, didData(cs)(did)._3, didData(cs)(did)._4, didData(cs)(did)._2, 1, cs, timestamp, didData(cs)(did)._8, time, 1))
                    dataListBc.value.add((Common.getUUID(), uid, did, dmId, 1, cs, timestamp, minTime, time, 0))
                  }
                } else if (vidData(cs)(vid)._7 > timestamp) {
                  dataListBc.value.add((Common.getUUID(), uid, vid, Common.getUUID(), 0, cs, timestamp, minTime, time, 1))
                }
              } else if (vidData(cs)(vid)._3 == "") {
                //否则直接补充uid
                dataListBc.value.remove(idIndex(vidData(cs)(vid)._1))
                dataListBc.value.add(idIndex(vidData(cs)(vid)._1), (vidData(cs)(vid)._1, uid, vid, dmId, 0, cs, timestamp, vidData(cs)(vid)._8, time, 0))
                if (isHaveDid && didData.isEmpty) {
                  //更新设备id
                  dataListBc.value.add((Common.getUUID(), uid, did, dmId, 1, cs, timestamp, minTime, time, 0))
                } else if (isHaveDid && didData.contains(cs) && didData(cs)(did)._2 != dmId) {
                  dataListBc.value.remove(idIndex(didData(cs)(did)._1))
                  dataListBc.value.add(idIndex(didData(cs)(did)._1), (didData(cs)(did)._1, uid, did, dmId, 0, cs, timestamp, vidData(cs)(vid)._8, time, 0))
                }
              }
            }
          }
        } else {
          //如果有天眼id,开始判断vid是否需要再新增一条
          //若当前vid在数据库中没有记录，则添加一条
          //id,dmIdOld,uid,vid,vidType,cs,timestampOld,firstTimeOld
          val uidDataHead = uidData.head
          dmIdUid = uidDataHead._2 //获取当前uid的天眼ID
          if (vidData.isEmpty) {
            //当前vid没有映射，直接添加一条记录
            dataListBc.value.add((Common.getUUID(), uid, vid, dmIdUid, 0, cs, timestamp, minTime, time, 0))
            if (isHaveDid) { //有设备id
              if (didData.isEmpty) { //该设备id没有映射
                dataListBc.value.add((Common.getUUID(), uid, did, dmIdUid, 1, cs, timestamp, minTime, time, 0))
              } else { //该设备id有映射
                if (didData(cs)(did)._2 != dmIdUid && didData(cs)(did)._7 < timestamp) {
                  dataListBc.value.remove(idIndex(didData(cs)(did)._1))
                  dataListBc.value.add(idIndex(didData(cs)(did)._1), (didData(cs)(did)._1, uid, did, dmIdUid, 1, cs, timestamp, didData(cs)(did)._8, time, 0))
                }
              }
            }
          } else if (vidData(cs)(vid)._7 < timestamp) {
            if (vidData(cs)(vid)._2 != dmIdUid) {
              // 当前vid有映射，但是映射的uid不是当前uid,则删除以前的数据，重新插入一条新的数据
              if (vidData(cs)(vid)._3 != "" && vidData(cs)(vid)._3 != uid) {
                dataListBc.value.remove(idIndex(vidData(cs)(vid)._1))
                dataListBc.value.add(idIndex(vidData(cs)(vid)._1), (vidData(cs)(vid)._1, vidData(cs)(vid)._3, vidData(cs)(vid)._4, vidData(cs)(vid)._2, 0, cs, timestamp, vidData(cs)(vid)._8, time, 1))
                if (uidVidData.contains(vid)) {
                  dataListBc.value.remove(idIndex(uidVidData(vid)._1))
                  dataListBc.value.add(idIndex(uidVidData(vid)._1), (uidVidData(vid)._1, uidVidData(vid)._3, uidVidData(vid)._4, uidVidData(vid)._2, 0, cs, timestamp, uidVidData(vid)._8, time, 0))
                } else {
                  dataListBc.value.add((Common.getUUID(), uid, vid, dmIdUid, 0, cs, timestamp, minTime, time, 0))
                }
                if (isHaveDid && didData.contains(cs) && didData(cs)(did)._3 != uid) {
                  dataListBc.value.remove(idIndex(didData(cs)(did)._1))
                  dataListBc.value.add(idIndex(didData(cs)(did)._1), (didData(cs)(did)._1, didData(cs)(did)._3, didData(cs)(did)._4, didData(cs)(did)._2, 1, cs, timestamp, didData(cs)(did)._8, time, 1))
                  if (uidVidData.contains(did)) {
                    dataListBc.value.remove(idIndex(uidVidData(did)._1))
                    dataListBc.value.add(idIndex(uidVidData(did)._1), (uidVidData(did)._1, uidVidData(did)._3, uidVidData(did)._4, uidVidData(did)._2, 1, cs, timestamp, uidVidData(did)._8, time, 0))
                  } else {
                    dataListBc.value.add((Common.getUUID(), uid, did, dmIdUid, 1, cs, timestamp, minTime, time, 0))
                  }
                }
              } else if (vidData(cs)(vid)._3 == "") {
                //有vid映射，uid也为空，则把当前vid归为现在的uid
                //当前vid没有映射，直接添加一条记录
                if (uidData.length == 1 && uidDataHead._4 == "") {
                  dataListBc.value.remove(idIndex(uidDataHead._1))
                  dataListBc.value.add(idIndex(uidDataHead._1), (uidDataHead._1, uid, vid, dmIdUid, 0, cs, timestamp, uidDataHead._8, time, 0))
                } else {
                  dataListBc.value.remove(idIndex(vidData(cs)(vid)._1))
                  dataListBc.value.add(idIndex(vidData(cs)(vid)._1), (vidData(cs)(vid)._1, uid, vid, dmIdUid, 0, cs, timestamp, vidData(cs)(vid)._8, time, 0))
                }
                if (isHaveDid && didData.contains(cs) && didData(cs)(did)._3 == "") {
                  dataListBc.value.remove(idIndex(didData(cs)(did)._1))
                  dataListBc.value.add(idIndex(didData(cs)(did)._1), (didData(cs)(did)._1, uid, did, dmIdUid, 1, cs, timestamp, didData(cs)(did)._8, time, 0))
                }
                changeDataBc.value += ((Common.getUUID(), vidData(cs)(vid)._2, dmIdUid, time, cs))
              }
            } else if (isHaveDid && didData.isEmpty) {
              //改uid,vid都有天眼id,设备ID没有映射
              dataListBc.value.add((Common.getUUID(), uid, did, dmIdUid, 1, cs, timestamp, minTime, time, 0))
            }
          }
        }
      } else {
        //未登录用户
        //设备id不为空
        if (didData.nonEmpty) {
          dmId = didData(cs)(did)._2
          if (vidData.isEmpty) {
            dataListBc.value.add((Common.getUUID(), didData(cs)(did)._3, vid, dmId, 0, cs, timestamp, minTime, time, 0))
          } else {
            //当前vid有映射，设备id也有映射，并且天眼id还不一样
            if (dmId != vidData(cs)(vid)._2 && vidData(cs)(vid)._7 < timestamp) {
              if (vidData(cs)(vid)._3 == "") {
                //当前vid的映射没有uid,则用当前设备id的映射
                dataListBc.value.remove(idIndex(vidData(cs)(vid)._1))
                dataListBc.value.add(idIndex(vidData(cs)(vid)._1), (vidData(cs)(vid)._1, didData(cs)(did)._3, vid, dmId, 0, cs, timestamp, vidData(cs)(vid)._8, time, 0))
                changeDataBc.value += ((Common.getUUID(), vidData(cs)(vid)._2, dmId, time, cs))
              } else {
                //当前vid有UID，则把当前的设备id改变
                dataListBc.value.remove(idIndex(didData(cs)(did)._1))
                dataListBc.value.add(idIndex(didData(cs)(did)._1), (didData(cs)(did)._1, vidData(cs)(vid)._3, did, vidData(cs)(vid)._2, 1, cs, timestamp, didData(cs)(did)._8, time, 0))
              }

            }
          }
        } else {
          if (vidData.isEmpty) {
            dmId = Common.getUUID()
            dataListBc.value.add((Common.getUUID(), "", vid, dmId, 0, cs, timestamp, minTime, time, 0))
            if (isHaveDid) {
              dataListBc.value.add((Common.getUUID(), "", did, dmId, 1, cs, timestamp, minTime, time, 0))
            }
          } else if (isHaveDid) {
            dataListBc.value.add((Common.getUUID(), vidData(cs)(vid)._3, did, vidData(cs)(vid)._2, 1, cs, timestamp, minTime, time, 0))
          }
        }

      }
    })
    (dataList, changeData)
  }

  /**
   *
   * 将kudu中的数据存入HIVE
   *
   * @param day 日期
   */
  def dmId2Hive(spark: SparkSession,day: String): Unit = {
    import spark.implicits._
    val myKudu=new MyKudu(spark)
    val dmIdTable = "t_dwd_dmid"
    //数据只保留近30天的数据
    val historyDay = Common.getSpecifyDate(day, 30)
    println(s"alter table $dmIdTable drop if exists partition(day='$historyDay')")
    spark.sql(s"alter table $dmIdTable drop if exists partition(day='$historyDay')")
    //删除计算当天的数据避免重复数据
    println(s"alter table $dmIdTable drop if exists partition(day='$day')")
    spark.sql(s"alter table $dmIdTable drop if exists partition(day='$day')")
    //将KUDU里的数据存入hive
    myKudu.select(dmIdTable).createOrReplaceTempView("t_kudu_dmid")
    spark.sql(s"select  id, uid, vid,dm_id ,is_del,vid_type, first_time,timestamp, update_time,cs,'$day' as day from t_kudu_dmid where cs<>''").repartition(1).write.mode("append").format("Hive")
      .partitionBy("day", "cs").saveAsTable("t_dwd_dmid")
    val oldDay = Common.getSpecifyDate(day, 31)
    val oldDay2 = Common.getSpecifyDate(day, 548) //一年半
    val data = spark.sql(s"select id from t_kudu_dmid where cs in ('pc','m','wx','') " +
      s" and uid='' and update_time<$oldDay ")
    myKudu.delete(data,dmIdTable)
    val data2 = spark.sql(s"select id from t_kudu_dmid where  uid='' and vid_type=0 and first_time<$oldDay2 and first_time=update_time  " +
      " and vid_type=0")
    myKudu.delete(data2,dmIdTable)

  }
  /**
   *离线方式重新清洗t_dwd_dmid,只处理app映射，如果修复数据，优先使用此方法
   * 如果需要修复当天的数据，从t_logdata_app_streaming查数据即可
   * @param day
   */
  def cleanDmid1(spark:SparkSession,day: String): Unit = {
    import spark.implicits._
    val myKudu=new MyKudu(spark)
    val dmIdTable = "t_dwd_dmid"
    val dmIdChangeTable = "t_dwd_dmid_change"
    val appLogTable = "t_logdata_app"
    //设置日志级别
    //    spark.sparkContext.setLogLevel("ERROR")
    spark.udf.register("uuid", Common.getUUID(_: String))
    spark.conf.set("spark.rpc.askTimeout", "600s")
//    myKudu.select("t_logdata_app_streaming")
//      .where(s"day='$day'")
//      .createOrReplaceTempView("t_app_log_data")
    //读取app日志数据
    spark.sql(
      s"""
         |select * from t_logdata_app where day='$day'
         |""".stripMargin)
      .createOrReplaceTempView("t_app_log_data")
    val vidUidLogTmp = spark.sql(
      s"""
         |select cid,if(uid='0','',uid) as uid,deviceid,max(timestamp) as timestamp,min(timestamp) as min_timestamp,cs from
         |(SELECT cid,uid,deviceid,if(LENGTH(`time`)==10,concat(`time`,'000'),time) as timestamp,
         |(case when (lib in ('com.truckhome.bbs','com.360che.truckhome') or av='7.4.0') and appid='ches' then concat(cs,"_c")
         |when appid='ches' then concat(cs,"_s") else  concat(cs,'_c')  end) as cs
         |from t_app_log_data )t group by cid,uid,deviceid,cs
         |""".stripMargin)
    vidUidLogTmp.createOrReplaceTempView("t_log_tmp")
    //读取kudu映射数据
    myKudu.select(dmIdTable).createOrReplaceTempView("t_dmid_tmp")
    spark.catalog.cacheTable("t_dmid_tmp")
    //获取和本次相关的数据进行更新
    val data = spark.sql(
      """
        |select distinct d.* from t_dmid_tmp d inner join t_log_tmp l on l.cid=d.vid
        |union
        |select distinct d.* from t_dmid_tmp d inner join t_log_tmp l1 on d.uid=l1.uid and l1.uid!=''
        |union
        |select distinct d.* from t_dmid_tmp d inner join t_log_tmp l2 on l2.deviceid = d.vid and d.vid_type=1
        |""".stripMargin)
    val arrList = data.map(row => {
      (row.getAs[String]("id"), row.getAs[String]("uid"), row.getAs[String]("vid"),
        row.getAs[String]("dm_id"), row.getAs[Int]("vid_type"),
        row.getAs[String]("cs"), row.getAs[Long]("timestamp"), row.getAs[Int]("first_time"),
        row.getAs[Int]("update_time"), row.getAs[Int]("is_del"))
    })
    val dataList = new util.ArrayList(arrList.collectAsList())
    val (result, changeData) = getDataList(spark, dataList)

    import scala.collection.JavaConverters._
    //    println(dataListBc.value)
    val updateDF = spark.sparkContext.parallelize(result.asScala).toDF("id", "uid", "vid", "dm_id", "vid_type", "cs",
      "timestamp", "first_time", "update_time", "is_del")
    myKudu.upsert(updateDF, dmIdTable)
    //    println(changeData)
    if (changeData.nonEmpty) {
      val changeDataDF = spark.sparkContext.parallelize(changeData).toDF("id", "dm_id_old", "dm_id_new", "update_time", "cs")
      myKudu.insert(changeDataDF, dmIdChangeTable)
    }
  }
  /**
   *离线方式重新清洗t_dwd_dmid,同时处理web日志和appr日志dmid映射
   * @param day
   */
  def cleanDmid2(spark:SparkSession,day: String): Unit = {
    import spark.implicits._
    val myKudu=new MyKudu(spark)
    val dmIdTable = "t_dwd_dmid"
    val dmIdChangeTable = "t_dwd_dmid_change"
    val appLogTable = "t_logdata_app"
    //设置日志级别
    //    spark.sparkContext.setLogLevel("ERROR")
    spark.udf.register("uuid", Common.getUUID(_: String))
    spark.conf.set("spark.rpc.askTimeout", "600s")
    //读取web日志
    val webData = spark.sql(
      s"""
         |select if(uid=='null','',uid) as uid,vid,daytime ,ua,"" as deviceid from t_logdata where day='$day' and uid ='null'
         |""".stripMargin).rdd.map(line => {
      val uid=line.getAs[String]("uid")
      val vid=line.getAs[String]("vid")
      val daytime=line.getAs[String]("daytime")
      val ua=line.getAs[String]("ua")
      val cs = Common.getClientByUa(ua)
      (uid, vid, daytime, cs, "")
    }).toDF("uid", "cid", "timestamp", "cs", "deviceid")
    //读取app日志数据
    spark.sql(
      s"""
         |select * from t_logdata_app where day='$day'
         |""".stripMargin)
      .createOrReplaceTempView("t_app_log")
    //将app数据和web数据合并
    spark.sql(
      """
        |SELECT if(uid='0','',uid) as uid,cid,if(LENGTH(`time`)==10,concat(`time`,'000'),time) as timestamp,(case when (lib in ('com.truckhome.bbs','com.360che.truckhome') or av='7.4.0') and appid='ches' then concat(cs,"_c")
        |when appid='ches' then concat(cs,"_s") else  concat(cs,'_c')  end) as cs,deviceid
        |from t_app_log
        |""".stripMargin).union(webData).createOrReplaceTempView("t_data_tmp")
    //根据uid,cid,设备id,客户端分组，获取每个用户标识的最新一次访问时间
    val logData = spark.sql(
      """
        |select cid,uid,cs,deviceid,max(timestamp) as timestamp,min(timestamp) as min_timestamp
        |from t_data_tmp group by cid,uid,cs,deviceid
        |""".stripMargin)
    logData.createOrReplaceTempView("t_log_tmp")
    //读取kudu表中的dm_id映射数据
    myKudu.select(dmIdTable).createOrReplaceTempView("t_dmid_tmp")
    spark.catalog.cacheTable("t_dmid_tmp")
    //获取和本次相关的数据进行更新
    val data = spark.sql(
      """
        |select distinct d.* from t_dmid_tmp d inner join t_log_tmp l on l.cid=d.vid
        |union
        |select distinct d.* from t_dmid_tmp d inner join t_log_tmp l1 on d.uid=l1.uid and l1.uid!=''
        |union
        |select distinct d.* from t_dmid_tmp d inner join t_log_tmp l2 on l2.deviceid = d.vid and d.vid_type=1
        |""".stripMargin)
    val arrList = data.map(row => {
      (row.getAs[String]("id"), row.getAs[String]("uid"), row.getAs[String]("vid"),
        row.getAs[String]("dm_id"), row.getAs[Int]("vid_type"),
        row.getAs[String]("cs"), row.getAs[Long]("timestamp"), row.getAs[Int]("first_time"),
        row.getAs[Int]("update_time"), row.getAs[Int]("is_del"))
    })
    val dataList = new util.ArrayList(arrList.collectAsList())
    val (result, changeData) = getDataList(spark, dataList)
    import scala.collection.JavaConverters._
    val updateDF = spark.sparkContext.parallelize(result.asScala).toDF("id", "uid", "vid", "dm_id", "vid_type", "cs",
      "timestamp", "first_time", "update_time", "is_del")
    myKudu.upsert(updateDF, dmIdTable)
    if (changeData.nonEmpty) {
      val changeDataDF = spark.sparkContext.parallelize(changeData).toDF("id", "dm_id_old", "dm_id_new", "update_time", "cs")
      myKudu.insert(changeDataDF, dmIdChangeTable)
    }
  }

  def hive2Kudu(spark:SparkSession): Unit ={
    spark.udf.register("toJson",toJson(_:String))
    val myKudu=new MyKudu(spark)
    //    val dmIdDF=spark.sql(
//      """
//        |SELECT id, uid, vid, dm_id, is_del, vid_type, update_time, first_time, cast(`timestamp` as int) as `timestamp`, `day`, cs
//        |FROM default.t_dwd_dmid where day='20230815'
//        |""".stripMargin)
//    myKudu.insert(dmIdDF,"t_dwd_dmid")
    //    val t_ba_articleDF=spark.sql(
    //      """
    //        |SELECT article_id as articleid, publish_time as publishtime, "article_t_article" as billType,"" daytime, "" as operationState, ""  publishTimeEnd, "" as `day`  from t_dws_article where publish_date ='20230821'
    //        |union all
    //        |select id as articleid, publish_time as publishtime, "article_t_video" as billType,"" daytime, "" as operationState, ""  publishTimeEnd, "" as `day` from t_dwd_video where publish_time ='20230821'
    //        |""".stripMargin).collect()
    //    t_ba_articleDF.foreach(println(_))
    //    val sqlBase=new MySqlBaseDao()
    //    //t_ba_article
    //    sqlBase.executeMany2("INSERT INTO t_ba_article(articleid, publishtime, billType, daytime, operationState, publishTimeEnd, `day`)VALUES(?,?,?,?,?,?,?)",t_ba_articleDF)
    //    // t_clues_realtime
    //    val t_clues_realtimeDF=spark.sql(
    //      """
    //        |SELECT id, vid, tel, truck_id as truckid,brand_id  as brandid,series_id  as seriesid,province_name as provincename,city_name as cityname,create_date,day,'' as uid,spm,clue_resource_id,clue_resource_name,province_id,city_id from t_inquiry_clue where day='20230821'
    //        |""".stripMargin).collect()
    //    sqlBase.executeMany2("REPLACE INTO t_clues_realtime(id, vid, tel, truckid,brandid,seriesid,provincename,cityname,create_date,day,uid,spm,clue_resource_id,clue_resource_name,province_id,city_id)VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)",t_clues_realtimeDF)
    //t_dws_article_streaming
    val t_dws_article_streamingDF=spark.sql(
      """
        |SELECT article_id, title, mtitle, publish_time, create_time, first_classify_id, first_classify_name, second_classify_id, second_classify_name, content, cate_id, sub_cate_id, brand_id, series_id, product_id, label_id, label_type_id, source_type, province, city, first_label, second_label, third_label, is_del, publish_date, relation_id, tonnage_type, is_dealer, is_dealer_push, toJson(concat_ws(',',mtitle_seg)) as mtitle_seg, scenario, census_typeone_id, census_typeone_name, census_typetwo_id, census_typetwo_name, user_type_id, user_type_name from t_dws_article where publish_date ="20230821"
        |""".stripMargin)
    myKudu.upsert(t_dws_article_streamingDF,"t_dws_article_streaming")
    //t_dws_article_streaming
    val t_dwd_video_streamingDF=spark.sql(
      """
        |SELECT id, title, url, publish_time, classify_id, classify_name, video_type, program_id, program_name, cate_id, sub_cate_id, brand_id, series_id, product_id, label_id, label_type_id, first_label, second_label, third_label, is_del, publish_date, relation_id, is_dealer, scenario, f_total_time, census_typeone_id, census_typeone_name, census_typetwo_id, census_typetwo_name, user_type_id, user_type_name, first_classify_id, first_classify_name, second_classify_id, second_classify_name from t_dwd_video where publish_date ="20230821";
        |""".stripMargin)
    myKudu.upsert(t_dwd_video_streamingDF,"t_dwd_video_streaming")
  }
  def toJson(myObject: String): String ={
    val myArray=myObject.split(",")

    JSON.toJSONString(myArray,SerializerFeature.PrettyFormat)
  }


}
